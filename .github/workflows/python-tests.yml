# .github/workflows/backend-ci.yml
name: Backend CI

on:
  push:
    paths:
      - 'backoffice/src/**'
  pull_request:
    paths:
      - 'backoffice/src/**'

jobs:
  tests:
    name: Run Python Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        working-directory: backoffice/src
        run: |
          pip install -r requirements.txt

      - name: Run pytest
        working-directory: backoffice/src
        run: |
          uv run python -m pytest tests/on_commit -v

  type-check:
    name: Pyrefly Type Check
    runs-on: ubuntu-latest
    needs: tests
    # allow warnings but not block merge
    continue-on-error: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Pyrefly
        working-directory: backoffice/src
        run: |
          pip install pyrefly

      - name: Run type check (warning only)
        working-directory: backoffice/src
        run: |
          echo "⚠️ Running pyrefly check (warnings only)"
          pyrefly check || true
