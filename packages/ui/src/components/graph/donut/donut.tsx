"use client";
import { useEffect, useState } from 'react'
import * as d3 from 'd3'
import { SegmentData, SegmentMapType } from '@ui/components/graph/donut/types'


import './donut.css'
import { appendText, setupArc } from '@ui/components/graph/donut/donut-lib'
import Image from 'next/image'

const colorScales: { [key: string]: any } = {
    "green-flags-inner": d3.scaleLinear()
        .domain([0.00001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#4a9e6e", "#87e6af"]),

    "green-flags-middle": d3.scaleLinear()
        .domain([0.00001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#4a9e6e", "#87e6af"]),

    "green-flags-outer": d3.scaleLinear()
        .domain([0.00001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#4a9e6e", "#87e6af"]),

    "red-flags-inner": d3.scaleLinear()
        .domain([0.000001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#b02e3c", "#e6153e"]),

    "red-flags-middle": d3.scaleLinear()
        .domain([0.000001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#b02e3c", "#e6153e"]),

    "red-flags-outer": d3.scaleLinear()
        .domain([0.000001, 0.5, 1.0])
        // @ts-ignore
        .range(["#222", "#b02e3c", "#e6153e"]),


    "warning-inner": d3.scaleLinear()
        .domain([0.00001, 0.1, 0.5])
        // @ts-ignore
        .range(["#60A37C", "#FFBF00", "#DC143C"]),
    "warning-outer": d3.scaleLinear()
        .domain([0.00001, 0.5, 1])
        // @ts-ignore
        .range(["#60A37C", "#FFBF00", "#DC143C"]),
    "brand-inner": d3.scaleLinear()
        .domain([0.00001, 1])
        // @ts-ignore
        .range(["#DC143C", "#DC143C"]),
    "brand-outer": d3.scaleLinear()
        .domain([0.00001, 1])
        // @ts-ignore
        .range(["#60A37C", "#60A37C"]),
    "reports-inner": d3.scaleLinear()
        .domain([0.00001, 10, 100])
        // @ts-ignore
        .range(["#DC143C", "#FFBF00", "#60A37C"]),
    "reports-outer": d3.scaleLinear()
        .domain([0.00001, 10, 100])
        // @ts-ignore
        .range(["#DC143C", "#FFBF00", "#60A37C"]),
    "fp-inner": d3.scaleLinear()
        .domain([0.00001, 1])
        // @ts-ignore
        .range(["#218A51", "#218A51"]),
    "fp-outer": d3.scaleLinear()
        .domain([0.00001, 1])
        // @ts-ignore
        .range(["#218A51", "#218A51"]),
};
export default function Donut({
                                  className,
                                  id,
                                  width,
                                  height,
                                  breakpoints,
                                  ecoSegments,
                                  socialSegments,
                                  governanceSegments = [],
                                  colorScale,
                                  model,
                                  'data-testid': dataTestId,
                              }: {
    className?: string | null,
    id: string,
    width?: number | "auto" | "responsive" | string,
    height?: number | "auto" | "responsive" | string,
    breakpoints?: { [breakpoint: number]: number },
    ecoSegments: SegmentData[],
    socialSegments: SegmentData[],
    governanceSegments?: SegmentData[],
    colorScale: "brand" | "warning" | "reports" | "fp" | "red-flags" | "green-flags",
    model: SegmentMapType,
    'data-testid'?: string
}) {
    console.log("DOUGHNUT GRAPH SEGMENTS (governanceSegments)", governanceSegments);
    const [divWidth, setDivWidth] = useState(0);
    const [ready, setReady] = useState(false);
    useEffect(() => {

        const doughnutElement = document.getElementById(id);
        if (doughnutElement) {
            doughnutElement.innerHTML = "";

        }
        let innerColorScale = colorScales[colorScale + "-inner"];
        let outerColorScale = colorScales[colorScale + "-outer"];
        let middleColorScale = colorScales[colorScale + "-middle"];

        let w: number = 0;
        let h: number = 0;
        let margin = 48;
        if (breakpoints) {
            for (const entry of Object.entries(breakpoints)) {
                if (window.innerWidth >= +entry[0]) {
                    w = entry[1] - margin;
                }
            }
        } else {
            if (width == "responsive") {
                if (window.innerWidth > 600) {
                    w = Math.min(window.innerWidth / 2, 600) - margin;
                } else {
                    w = window.innerWidth - margin;
                }
            } else {
                if (width == "auto") {
                    w = document.getElementById(id)?.parentElement?.offsetWidth || 800;
                } else if (width && width.toString().includes("%")) {
                    w = window.innerHeight * +width.toString().substring(0, width.toString.length - 1) / 100;
                } else {
                    w = +(width || 800);
                }
            }
        }

        h = w;

        setDivWidth(w);

        const radius = Math.min(w, h) / 2;
        const svg = d3.select("#" + id)
            .attr("width", w)
            .attr("height", h)
            .append("g")
            .attr("transform", `translate(${w / 2}, ${h / 2})`);

        let overlay: d3.Selection<SVGGElement, unknown, HTMLElement, any> | null = null;

        const innerIcons = radius < 300;
        const outerIcons = radius < 200;


        const arcInner = d3.arc()
            .innerRadius(radius * 0.3)
            .outerRadius(radius * 0.5);

        const arcMiddle = d3.arc()
            .innerRadius(radius * 0.5)
            .outerRadius(radius * 0.7);

        const arcOuter = d3.arc()
            .innerRadius(radius * 0.70)
            .outerRadius(radius * 0.95);


        const arcLabelInner = d3.arc()
            .innerRadius(radius * 0.4)
            .outerRadius(radius * 0.4);

        const arcLabelMiddle = d3.arc()
            .innerRadius(radius * 0.6)
            .outerRadius(radius * 0.6)
            .startAngle(-Math.PI / 2)
            .endAngle(Math.PI / 2);

        const arcLabelOuter = d3.arc()
            .innerRadius(radius * 0.85)
            .outerRadius(radius * 0.85);


        const pie = d3.pie()
            .sort(null)
            .value(() => 1);


        const arcsInner: any = svg.selectAll(".arc-inner")
            // @ts-ignore
            .data(pie(socialSegments))
            .enter()
            .append("g")
            .attr("class", "arc arc-inner");

        const arcsMiddle: any = svg.selectAll(".arc-middle")
            // @ts-ignore
            .data(pie(governanceSegments))
            .enter()
            .append("g")
            .attr("class", "arc arc-middle");

        const arcsOuter = svg.selectAll(".arc-outer")
            // @ts-ignore
            .data(pie(ecoSegments))
            .enter()
            .append("g")
            .attr("class", "arc arc-outer");


        setupArc(arcsOuter, arcOuter, outerColorScale, () => overlay, w, h, radius, svg, model);
        setupArc(arcsMiddle, arcMiddle, middleColorScale, () => overlay, w, h, radius, svg, model);
        setupArc(arcsInner, arcInner, innerColorScale, () => overlay, w, h, radius, svg, model);
        appendText(arcsInner, arcInner, arcLabelInner, w, h, radius, innerIcons, "inner", model);
        if (governanceSegments) {
            appendText(arcsMiddle, arcMiddle, arcLabelMiddle, w, h, radius, false, "middle", model);
        }
        appendText(arcsOuter, arcOuter, arcLabelOuter, w, h, radius, outerIcons, "outer", model);

        overlay = svg.append("g").attr("class", "overlay");
        setReady(true)
    });

    return ready ? (<div style={{ minWidth: divWidth }} data-testid={dataTestId}>
        <svg id={id} width={width} height={height}
             className={className + " relative drop-shadow-lg"}
             data-testid={dataTestId}/>
    </div>) : (<Image alt="donut skeleton" src={"/skeletons/donut.png"} width={328} height={328} />)  ;
}
