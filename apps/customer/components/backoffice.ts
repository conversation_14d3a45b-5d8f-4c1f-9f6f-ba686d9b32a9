import {SupabaseClient} from "@supabase/supabase-js";

const lookup = {
    "single_url": (response_data: any) => {

    },
}


export function backOfficeRequestListener(supabase: SupabaseClient, requester: string, id: number | null, callback: (status: string, payload: any, error: any, message: string) => void) {
    const channels = supabase.channel('api-queue-channel')
        .on(
            'postgres_changes',
            {
                event: '*',
                schema: 'public',
                table: 'api_queue',
                filter: id ? 'id=eq.' + id : 'requester=eq.' + requester
            },
            async (payload) => {
                console.log('Change received!', payload)
                if ((payload.new as any)['status'] !== 'error') {
                    callback((payload.new as any)['status'], (payload.new as any)['response_data'], null,  (payload.new as any)['message'])
                }
                if ((payload.new as any)['status'] === 'error') {
                    callback((payload.new as any)['status'], null, (payload.new as any)['response_data'], (payload.new as any)['message'])
                }
            }
        )
        .subscribe();
    return channels;
}


export function backOfficeListener(supabase: SupabaseClient, callback: (payload: any, error: any) => void) {
    const channels = supabase.channel('api-queue-channel')
        .on(
            'postgres_changes',
            {event: '*', schema: 'public', table: 'api_queue', filter: 'status=eq.completed'},
            async (payload) => {
                console.log('Change received!', payload)
                if ((payload.new as any)['status'] === 'completed') {
                    callback((payload.new as any), null)
                }
                if ((payload.new as any)['status'] === 'error') {
                    callback(null, (payload.new as any)['message'])
                }
            }
        )
        .subscribe();
    return channels;
}

export function messageListener(supabase: SupabaseClient, userId: string, callback: (payload: any, error: any) => void) {
    const channels = supabase.channel('acc-messages-channel')
        .on(
            'postgres_changes',
            {event: '*', schema: 'public', table: 'acc_messages', filter: 'deleted_at=.is.null,read_at=is.null,recipient=eq.' + userId},
            async (payload:any) => {
                console.log('Change received!', payload);
                callback((payload.new as any), null)
            }
        )
        .subscribe();
    return channels;
}

export async function callBackofficeAsync(supabase: SupabaseClient, apiFunction: string, request: any): Promise<number> {
    return (await supabase
        .from('api_queue')
        .upsert({request_action: apiFunction, request_data: request})
        .select()).data![0]?.id;
}

export function callBackoffice(supabase: SupabaseClient, apiFunction: string, request: any) {
    return new Promise((resolve, reject) => {
        supabase
            .from('api_queue')
            .upsert({request_action: apiFunction, request_data: request})
            .select()
            .then(({data, error}) => {
                if (error) {
                    console.error('error', error)
                    reject(error)
                }

                if (data) {
                    const channels = supabase.channel('api-queue-channel')
                        .on(
                            'postgres_changes',
                            {event: '*', schema: 'public', table: 'api_queue', filter: 'id=eq.' + data[0].id},
                            async (payload) => {
                                try {
                                    await channels.unsubscribe()
                                } catch (e) {
                                    console.error('error unsubscribing', e)
                                    reject(e)
                                }
                                console.log('Change received!', payload)
                                if ((payload.new as any)['status'] === 'completed') {
                                    resolve((payload.new as any)['response_data'])
                                }
                                if ((payload.new as any)['status'] === 'error') {
                                    reject((payload.new as any)['response_data'])
                                }
                            }
                        )
                        .subscribe();
                }
            })
    });

}
