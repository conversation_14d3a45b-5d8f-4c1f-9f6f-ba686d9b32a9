import { Card, CardContent } from '@ui/components/ui/card'
import Link from 'next/link'
// import { StarRating } from '@ui/components/star-rating' // Temporarily disabled per EKO-30
import React from 'react'

export type AuthorType={
    name: string;
    cn: string;
    url: string;
    eko_id: string;
    domains: string[];
    description: string;
    names: string[];
    short_id: string;
}

export type CitationType = {
    doc_page_id: number;
    url: string;
    page: number;
    score: number;
    title: string;
    doc_id: number;
    public_url: string;
    credibility: number;
    doc_name: string;
    year?: number;
    authors: AuthorType[];
  referenced?: boolean; // Added for EkoMarkdown component
}


export type ReducedCitationType = {
    url: string;
    pages: number[];
    score: number;
    title: string;
    doc_id: number;
    public_url: string;
    credibility: number;
    doc_name: string;
    year?: number;
    authors: AuthorType[];
};


export function reduceCitations(citations: CitationType[] | undefined | null): ReducedCitationType[] {
    const reportMap: { [key: string]: ReducedCitationType } = {};
    console.log("Citations:", citations);

    // Check if citations is undefined, null, not an array, or empty
    if (!citations || !Array.isArray(citations) || citations.length === 0) {
        console.log("Invalid citations:", JSON.stringify(citations));
        return [];
    }
    // REMOVED: citations.sort((a, b) => a.title < b.title ? -1 : 1);

    citations.forEach(citation => {
        let key = citation.title+citation.authors;
        if (reportMap[key]) {
            // If the report already exists in the map, add the page to the pages array
            reportMap[key]!.pages.push(citation.page);
            reportMap[key]!.pages = Array.from(new Set(reportMap[key]!.pages));
        } else {
            // If the report is new, create a new entry in the map
            reportMap[key] = {
                url: citation.url,
                pages: [citation.page],
                score: citation.score,
                title: citation.title,
                doc_id: citation.doc_id,
                public_url: citation.public_url,
                credibility: citation.credibility,
                doc_name: citation.doc_name,
                year: citation.year,
                authors: citation.authors || [],
            };
        }
    });

    // Convert the map back to an array
    return Object.values(reportMap).sort((a, b) => a.title < b.title ? -1 : 1);
}


export function citationLink(citation: CitationType, isAdmin: boolean): string {
    return citationLinkForUrl(citation.public_url, citation.page, isAdmin)
}

export function citationLinkForUrl(public_url: string, page: number, isAdmin: boolean): string {
    // Handle empty or null URLs
    if (!public_url || public_url.trim() === '') {
        return '#'
    }

    // If it's just a hash fragment (like #citation-123), return as-is
    if (public_url.startsWith('#')) {
        return public_url
    }

    // Try to parse as a full URL
    try {
        const urlObj = new URL(public_url)
        if (urlObj.pathname.endsWith('.pdf')) {
            return public_url + '#page=' + (page + 1)
        } else {
            return public_url
        }
    } catch (e) {
        // If URL parsing fails, it might be a relative URL or malformed
        // Check if it looks like a relative path or just return as-is
        if (public_url.includes('/') || public_url.includes('.')) {
            // Looks like a relative URL, return as-is
            return public_url
        }

        // Log the error for debugging but don't spam the console
        console.warn('Invalid URL format:', public_url)
        return public_url
    }
}

export  function Citation({admin, data: {doc_id, doc_name, pages, public_url, title, year}}: { data: ReducedCitationType, admin: boolean }) {
    if (doc_name === null) {
        return <p className="text-sm text-muted-foreground">No Source Cited</p>;
    }

    return (
        <Card className="mt-2">
            <CardContent className="p-4">
                { public_url ?
                <Link
                  href={citationLinkForUrl(public_url, pages[0], admin)}
                    className="text-primary hover:underline">
                    {title || doc_name}
                </Link> : title || doc_name}
                {year && (
                    <span className="text-sm text-muted-foreground ml-2">
            [{year}]
          </span>
                )}
                {pages && (
                    <span className="text-sm text-muted-foreground block mt-1">

            Page{pages.length > 1 ? 's' : ''}: {pages.map((i: number) => +i + 1).sort().map((page, index) => (
                public_url ?
                    (<Link key={page+":"+doc_id}
                           href={citationLinkForUrl(public_url, page, admin)}
                            className="text-primary hover:underline">
                            {page}{index < pages.length - 1 ? ', ' : ''}
                        </Link>) : (page+""+(index < pages.length - 1 ? ', ' : ''))
                    ))}
          </span>
                )}
            </CardContent>
        </Card>
    );
}

export  function CompactCitation(props: { data: ReducedCitationType, admin: boolean }) {
    if (props.data.doc_name === null) {
        return <p className="text-sm text-muted-foreground">No Source Cited</p>;
    }

    // Format authors list
    const formatAuthors = (authors: AuthorType[]): string => {
        if (!authors || authors.length === 0) return '';
        const authorNames = authors.map(author => author.name).filter(Boolean);
        if (authorNames.length === 0) return '';
        if (authorNames.length === 1) return authorNames[0];
        if (authorNames.length === 2) return `${authorNames[0]} and ${authorNames[1]}`;
        return `${authorNames.slice(0, -1).join(', ')} and ${authorNames[authorNames.length - 1]}`;
    };

    const authorsText = formatAuthors(props.data.authors);

    return (
        <div className="text-sm">
            {/* Title with link */}
            {props.data.public_url ? (
                <Link
                    href={citationLinkForUrl(props.data.public_url, props.data.pages[0], props.admin)}
                    className="font-medium hover:underline text-primary"
                >
                    {props.data.title || props.data.doc_name}
                </Link>
            ) : (
                <span className="font-medium">{props.data.title || props.data.doc_name}</span>
            )}

            {/* Year */}
            {props.data.year && (
                <span className="text-muted-foreground ml-1">
                    ({props.data.year})
                </span>
            )}

            {/* Authors */}
            {authorsText && (
                <span className="text-muted-foreground ml-1">
                    by {authorsText}
                </span>
            )}

            {/* Pages */}
            {props.data.pages && props.data.pages.length > 0 && (
                <span className="ml-1">
                    p. {props.data.pages.map((i: number) => i + 1).sort((a,b)=>a-b).map((page, index) => (
                        props.data.public_url ? (
                            <Link
                                key={props.data.doc_id + ":" + page}
                                href={citationLinkForUrl(props.data.public_url, page, props.admin)}
                                className="text-primary hover:underline"
                            >
                                {page}{index < props.data.pages.length - 1 ? ', ' : ''}
                            </Link>
                        ) : (
                            <span key={props.data.doc_id + ":" + page}>
                                {page}{index < props.data.pages.length - 1 ? ', ' : ''}
                            </span>
                        )
                    ))}
                </span>
            )}
        </div>
    );
}
