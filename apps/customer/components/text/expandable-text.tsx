'use client'

import * as React from 'react'
import { Button } from '@ui/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@ui/components/ui/collapsible'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useAutoAnimate } from '@formkit/auto-animate/react'
import Markdown from 'react-markdown'

export default function ExpandableText({title, text, image, expanded=true}: { title: string, text: string, image: React.ReactNode , expanded:boolean}) {
    const [isOpen, setIsOpen] = React.useState(false)
    const [parent, enableAnimations] = useAutoAnimate(/* optional config */)

    const fullText = text;
    const splitText = fullText.split('\n')
    console.log(splitText)

    return expanded ? (
        <div className="w-full max-w-2xl mx-auto p-4 space-y-4 prose dark:text-foreground">
            {image}
            <h2 className="text-2xl font-bold">{title}</h2>
            <Markdown>{fullText}</Markdown>
        </div>
    ) : (
        <div className="w-full max-w-2xl mx-auto p-4 space-y-4 prose dark:text-foreground">
            <Collapsible
                open={isOpen}
                onOpenChange={setIsOpen}
                className="space-y-2"
            >
                <div className="text-base leading-relaxed">

                    {image}
                    <h2 className="text-2xl font-bold">{title}</h2>
                    {splitText[0].replaceAll(/\[\^\d+]/g, '')} {isOpen ? "" : "..."}
                </div>
                <CollapsibleContent className="text-base leading-relaxed" ref={parent}>
                    {splitText.slice(1).map((line, index) => (
                        <p key={index}>
                            {line}
                        </p>))}
                </CollapsibleContent>
                <div className="pt-2">
                    <CollapsibleTrigger asChild>
                        <Button variant="outline" size="sm" className="flex items-center">
                            {isOpen ? (
                                <>
                                    Read less
                                    <ChevronUp className="ml-2 h-4 w-4"/>
                                </>
                            ) : (
                                <>
                                    Read more
                                    <ChevronDown className="ml-2 h-4 w-4"/>
                                </>
                            )}
                        </Button>
                    </CollapsibleTrigger>
                </div>
            </Collapsible>
        </div>
    )
}
