import {<PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle} from "@ui/components/ui/card"
import {AlertCircle} from "lucide-react"

export default function NoData({title = "No Data", description = "Sorry we couldn't find any data for this entity.", dataTestId}: {
    title?: string,
    description?: string,
    dataTestId?: string
}) {
    return (
        <main className="flex items-center justify-center min-h-screen bg-background" data-testid={dataTestId || "empty-state"}>
            <Card className="w-full max-w-md">
                <CardHeader className="flex flex-row items-center gap-2">
                    <AlertCircle className="h-6 w-6 text-blue-500"/>
                    <CardTitle>{title}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-center text-muted-foreground" data-testid="no-data-message">
                        {description}
                    </p>
                </CardContent>
            </Card>
        </main>
    )
}
