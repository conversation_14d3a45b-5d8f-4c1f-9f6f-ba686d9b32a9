import React, {useEffect, useState} from "react";
import {useAuth} from "@/components/context/auth/auth-context";
import {createClient} from "@/app/supabase/client";
import {runAsync} from "@utils/react-utils";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@ui/components/ui/select";

type CompanyTuple = { name: string, entity_xid: string };

export function EntitySelector({defaultEntity, onChange}: {
    defaultEntity: string | undefined,
    onChange: (value: string) => void
}) {
    const [companies, setCompanies] = useState<CompanyTuple[]>([]);
    const auth = useAuth();
    const supabase = createClient();

    useEffect(() => {
        runAsync(async () => {
            const userId = auth.user?.id;
            if (!userId) return;
            const {
                data: companies,
                error: error
            } = await supabase.from('view_my_companies').select().eq("profile_id", userId)
            setCompanies(companies as CompanyTuple[]);

        });
    }, []);

    if (!companies) return null;

    return <Select defaultValue={defaultEntity}
                   onValueChange={onChange}
    >
        <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select Company"/>
        </SelectTrigger>
        <SelectContent className="bg-background">
            {companies.map((company) => (
                <SelectItem key={company.entity_xid} value={company.entity_xid}>
                    {company.name}
                </SelectItem>))
            }
        </SelectContent>
    </Select>;
}
