import {SidebarTrigger} from "@ui/components/ui/sidebar";
import {Separator} from "@ui/components/ui/separator";
import React from "react";

export function SimplePageHeader({children}: { children?: React.ReactNode }) {
    return <header className="flex h-16 shrink-0 items-center border-b px-4 text-foreground min-w-full">
        <div className="flex items-center gap-2 flex-grow ">
            <SidebarTrigger className="-ml-1 mr-4"/>
            <Separator orientation="vertical" className="h-6"/>
            {children}
        </div>
    </header>;
}
