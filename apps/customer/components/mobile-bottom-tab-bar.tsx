"use client"

import * as React from "react"
import {Building2Icon, FileTextIcon, Home, User} from 'lucide-react'
import {cn} from "@utils/lib/utils"
import {useIsMobile} from "@ui/components/hooks/use-mobile";
import {DashboardIcon} from "@radix-ui/react-icons";
import {useNav} from "./context/nav/nav-context";

const tabs = [
    {name: "Dashboard", icon: DashboardIcon, href: "/customer/dashboard"},
    {name: "Reports", icon: FileTextIcon, href: "/customer/dashboard/reports"},
    {name: "Home", icon: Home, href: "/customer"},
    {name: "Analyse", icon: Building2Icon, href: "/customer/analysis/companies"},
    {name: "Account", icon: User, href: "/customer/account"},
]

export function MobileBottomTabBar() {
    const [activeTab, setActiveTab] = React.useState("Dashboard")
    const isMobile = useIsMobile();
    const nav = useNav();

    function switchTab(tabName: string) {
        setActiveTab(tabName)
        nav.router.push(tabs.find(tab => tab.name === tabName)!.href)

    }

    return isMobile && (
        <div
            className="fixed bottom-0 left-0 z-50 w-full h-16 bg-white border-t border-zinc-200 dark:bg-zinc-700 dark:border-zinc-600">
            <div className="grid h-full max-w-lg grid-cols-5 mx-auto">
                {tabs.map((tab,index) => (
                    <button
                        key={tab.name}
                        type="button"
                        onClick={() => switchTab(tab.name)}
                        className="inline-flex flex-col items-center justify-center px-5 hover:bg-zinc-50 dark:hover:bg-zinc-800 group"
                    >
                        <tab.icon
                            className={cn(
                                " w-6 h-6 mb-1 mt-1 text-zinc-500 dark:text-zinc-400 group-hover:text-blue-600 dark:group-hover:text-blue-500",
                                {
                                    "text-blue-600 dark:text-blue-500": activeTab === tab.name,
                                    "w-7 h-7 mt-0 text-zinc-800 dark:text-zinc-200": (index == Math.floor(tabs.length/2))
                                }
                            )}
                            aria-hidden="true"
                        />
                        <span
                            className={cn(
                                " text-[11px] sm:text-xs text-zinc-500 dark:text-zinc-400 group-hover:text-blue-600 dark:group-hover:text-blue-500",
                                {
                                    "text-blue-600 dark:text-blue-500": activeTab === tab.name,
                                    " text-[12px] text-zinc-800 dark:text-zinc-200": (index == Math.floor(tabs.length/2))
                                }
                            )}
                        >
              {tab.name}
            </span>
                    </button>
                ))}
            </div>
        </div>
    )
}
