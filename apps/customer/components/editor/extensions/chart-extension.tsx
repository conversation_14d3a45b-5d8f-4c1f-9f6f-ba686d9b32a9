import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Node } from '@tiptap/core'
import { Node<PERSON>iew<PERSON><PERSON>per, ReactNodeViewRenderer } from '@tiptap/react'
import { jsonrepair } from 'jsonrepair'
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Line,
  LineChart,
  Pie,
  PieChart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  RadialBar,
  RadialBar<PERSON>hart,
  XAxis,
  YAxis,
} from 'recharts'
import {
  type ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@ui/components/ui/chart'
// Import eCharts for legacy support
import ReactECharts from 'echarts-for-react'
import { ErrorBoundary } from '@sentry/nextjs'

// Interface for new chart data format
interface ChartData {
  type: 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'radial'
  title?: string
  description?: string
  data: Record<string, any>[]
  config: ChartConfig
}

// Interface for legacy eCharts format
interface LegacyChartData {
  title?: any
  xAxis?: any
  yAxis?: any
  series?: any
  tooltip?: any
  legend?: any
  grid?: any

  [key: string]: any
}

/* ------------------------------------------------------------------
 *  Chart React Component
 * ------------------------------------------------------------------*/
const ChartComponent = React.memo<{ node: any }>(({ node }) => {
  // Get the raw content and create a stable key for memoization
  const encodedJson = node.attrs['data-json']
  const containerRef = useRef<HTMLDivElement>(null)
  const [isContainerReady, setIsContainerReady] = useState(false)

  // Generate unique ID for this chart instance
  const chartId = useMemo(() => `chart-${Math.random().toString(36).substring(2, 11)}`, [])

  // Check if container has proper dimensions
  useEffect(() => {
    const checkDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current
        if (clientWidth > 0 && clientHeight > 0) {
          setIsContainerReady(true)
        }
      }
    }

    const timeoutId = setTimeout(checkDimensions, 100)
    const intervalId = setInterval(checkDimensions, 200)

    // Clean up
    const cleanup = () => {
      clearTimeout(timeoutId)
      clearInterval(intervalId)
    }

    setTimeout(cleanup, 2000) // Stop checking after 2 seconds
    return cleanup
  }, [])

  if (!encodedJson) return (<NodeViewWrapper className="chart-error" data-testid="chart-error">
    <div className="p-4 border border-red-300 rounded bg-red-50 text-red-700">
      <div className="font-medium">Chart Error</div>
      <div className="text-sm mt-1">
        {'No chart data'}
      </div>
    </div>
  </NodeViewWrapper>)

  const { chartData, legacyChartData, error } = useMemo(() => {
    let jsonContent = null

    // If we have encoded JSON data, try to use that first (more reliable)
    if (encodedJson) {
        jsonContent = Buffer.from(encodedJson, 'base64').toString()
    } else {
      return { chartData: null, legacyChartData: null, error: 'No encoded JSON data' }
    }

    if (!jsonContent || jsonContent.trim() === '') {
      return { chartData: null, legacyChartData: null, error: null }
    }

    // First try to parse as-is
    if (typeof jsonContent === 'string') {
      try {
        const repairedJson = jsonrepair(jsonContent)
        const parsed = JSON.parse(repairedJson)

        // Check if it's the new format  (has a type attribute)
        if (!!parsed.type) {
          return { chartData: parsed as ChartData, legacyChartData: null, error: null }
        }

        console.log('Detected legacy eCharts format')

        // Ensure we have a valid eCharts configuration object
        try {
          // Create a safe copy of the parsed data to avoid any React rendering issues
          // Special handling for title object to prevent React rendering errors
          const safeLegacyData = JSON.parse(JSON.stringify(parsed, (key, value) => {
            // If this is a title object with textStyle, ensure it's properly serialized
            if (key === 'title' && value && typeof value === 'object' && value.textStyle) {
              return {
                ...value,
                text: String(value.text || ''),
                textStyle: {
                  ...value.textStyle,
                  // Ensure all textStyle properties are serializable
                  fontSize: value.textStyle?.fontSize || 18,
                  fontWeight: value.textStyle?.fontWeight || 'normal',
                  color: value.textStyle?.color || '#333',
                },
              }
            }
            return value
          }))
          console.log('Safe legacy data created successfully')
          return {
            chartData: null,
            legacyChartData: safeLegacyData as LegacyChartData,
            error: null,
          }
        } catch (serializationError) {
          console.error('Serialization error:', serializationError)
          return {
            chartData: null,
            legacyChartData: null,
            error: 'Legacy chart data contains non-serializable objects',
          }
        }


      } catch (repairError) {
        // Log all parsing errors - no silent failures
        const repairErrorMsg = repairError instanceof Error ? repairError.message : String(repairError)

        console.error('Chart JSON parsing failed:', {
          content: String(jsonContent).substring(0, 100) + (jsonContent.length > 100 ? '...' : ''),
          encodedJson: String(encodedJson),
          repairError: String(repairErrorMsg),
        })

        return {
          chartData: null,
          legacyChartData: null,
          error: `Invalid chart JSON: ${repairErrorMsg}`,
        }
      }

    }

    return {
      chartData: null,
      legacyChartData: null,
      error: 'Chart content must be a valid JSON string',
    }
  }, [encodedJson])

  // Show error only if both formats failed
  if (!chartData && !legacyChartData && error) {
    return (
      <NodeViewWrapper className="chart-error" data-testid="chart-error">
        <div className="p-4 border border-red-300 rounded bg-red-50 text-red-700">
          <div className="font-medium">Chart Error</div>
          <div className="text-sm mt-1">
            {error}
          </div>
        </div>
      </NodeViewWrapper>
    )
  }

  // Handle legacy eCharts format
  if (legacyChartData && !chartData) {
    try {
      return (
        <NodeViewWrapper className="chart-wrapper" data-testid="chart-container">
          <div
            ref={containerRef}
            id={chartId}
            className="w-full"
            style={{ minHeight: '400px' }}
          >
            {/* Let eCharts handle title rendering internally */}
            {isContainerReady && legacyChartData ? (
              <ErrorBoundary>
              <ReactECharts
                data-testid="echarts-chart"
                option={legacyChartData}
                style={{ height: '400px', width: '100%' }}
                opts={{ renderer: 'canvas' }}
              />
              </ErrorBoundary>
            ) : (
              <div className="flex items-center justify-center h-96 text-gray-500">
                Loading eCharts chart...
              </div>
            )}
          </div>
        </NodeViewWrapper>
      )
    } catch (renderError) {
      console.error('Legacy chart rendering error:', renderError)
      return (
        <NodeViewWrapper className="chart-error" data-testid="chart-error">
          <div className="p-4 border border-red-300 rounded bg-red-50 text-red-700">
            <div className="font-medium">Legacy Chart Rendering Error</div>
            <div className="text-sm mt-1">
              Failed to render legacy eCharts
              format: {renderError instanceof Error ? renderError.message : 'Unknown error'}
            </div>
          </div>
        </NodeViewWrapper>
      )
    }
  }

  // If neither format is available, show loading or empty state
  if (!chartData && !legacyChartData) {
    return (
      <NodeViewWrapper className="chart-wrapper" data-testid="chart-container">
        <div className="flex items-center justify-center h-96 text-gray-500">
          No chart data available.
        </div>
      </NodeViewWrapper>
    )
  }

  // Continue with new format rendering below...

  // Render new Recharts format
  const renderChart = () => {
    const { type, data, config, title, description } = chartData

    switch (type) {
      case 'area':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={Object.keys(data[0] || {})[0]} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              {Object.keys(config).map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={`var(--color-${key})`}
                  fill={`var(--color-${key})`}
                  fillOpacity={0.6}
                />
              ))}
            </AreaChart>
          </ChartContainer>
        )

      case 'bar':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={Object.keys(data[0] || {})[0]} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              {Object.keys(config).map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={`var(--color-${key})`}
                  data-testid="chart-bars"
                />
              ))}
            </BarChart>
          </ChartContainer>
        )

      case 'line':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={Object.keys(data[0] || {})[0]} />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              {Object.keys(config).map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={`var(--color-${key})`}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          </ChartContainer>
        )

      case 'pie':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              <Pie
                data={data}
                dataKey={Object.keys(data[0] || {}).find(key => key !== Object.keys(data[0] || {})[0]) || 'value'}
                nameKey={Object.keys(data[0] || {})[0] || 'name'}
                cx="50%"
                cy="50%"
                outerRadius={120}
                label
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={`var(--color-${entry[Object.keys(data[0] || {})[0]]})`}
                  />
                ))}
              </Pie>
            </PieChart>
          </ChartContainer>
        )

      case 'radar':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <RadarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <PolarGrid />
              <PolarAngleAxis dataKey={Object.keys(data[0] || {})[0]} />
              <PolarRadiusAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              {Object.keys(config).map((key, index) => (
                <Radar
                  key={key}
                  name={String(config[key]?.label || key)}
                  dataKey={key}
                  stroke={`var(--color-${key})`}
                  fill={`var(--color-${key})`}
                  fillOpacity={0.6}
                />
              ))}
            </RadarChart>
          </ChartContainer>
        )

      case 'radial':
        return (
          <ChartContainer config={config} className="min-h-[400px] w-full" data-testid="recharts-chart">
            <RadialBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
              <RadialBar
                dataKey={Object.keys(data[0] || {}).find(key => key !== Object.keys(data[0] || {})[0]) || 'value'}
                cornerRadius={10}
                fill={`var(--color-${Object.keys(config)[0]})`}
              />
            </RadialBarChart>
          </ChartContainer>
        )

      default:
        return (
          <div className="p-4 border border-yellow-300 rounded bg-yellow-50 text-yellow-700">
            <div className="font-medium">Unsupported Chart Type</div>
            <div className="text-sm mt-1">
              Chart type "{String(type)}" is not supported. Supported types: area, bar, line, pie, radar, radial.
            </div>
          </div>
        )
    }
  }

  // Render new Recharts format (chartData is guaranteed to exist here)
  return (
    <NodeViewWrapper className="chart-wrapper" data-testid="chart-container">
      <div
        ref={containerRef}
        id={chartId}
        className="w-full"
        style={{ minHeight: '400px' }}
      >
        {chartData.title && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold" data-testid="chart-title">{chartData.title}</h3>
            {chartData.description && <p className="text-sm text-muted-foreground" data-testid="chart-description">{chartData.description}</p>}
          </div>
        )}
        {isContainerReady ? (
          renderChart()
        ) : (
          <div className="flex items-center justify-center h-96 text-gray-500">
            Loading chart...
          </div>
        )}
      </div>
    </NodeViewWrapper>
  )
}, (prevProps, nextProps) => {
  // Only re-render if the JSON string content has actually changed
  const prevContent = prevProps.node.textContent || prevProps.node.attrs.content || ''
  const nextContent = nextProps.node.textContent || nextProps.node.attrs.content || ''
  const prevEncodedJson = prevProps.node.attrs['data-json'] || ''
  const nextEncodedJson = nextProps.node.attrs['data-json'] || ''

  // Trim whitespace to avoid re-renders due to formatting changes
  const prevTrimmed = prevContent.trim()
  const nextTrimmed = nextContent.trim()

  // Check both content and encoded JSON for changes
  return prevTrimmed === nextTrimmed && prevEncodedJson === nextEncodedJson
})

ChartComponent.displayName = 'ChartComponent'

/* ------------------------------------------------------------------
 *  TipTap Chart Extension
 * ------------------------------------------------------------------*/
export const ChartExtension = Node.create({
  name: 'chart',

  group: 'block',

  content: 'text*',
  
  atom: true,

  addAttributes() {
    return {
      'data-json': {
        default: null,
        parseHTML: element => element.getAttribute('data-json'),
        renderHTML: attributes => {
          if (!attributes['data-json']) {
            return {}
          }
          return { 'data-json': attributes['data-json'] }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'chart',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          return {
            'data-json': element.getAttribute('data-json'),
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['chart', { ...HTMLAttributes, 'data-type': 'chart' }, 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ChartComponent)
  },
})
