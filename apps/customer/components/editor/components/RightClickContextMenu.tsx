import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { createPortal } from 'react-dom'
import { ColumnLayout } from '../extensions/ColumnsExtension'
import {
  Co<PERSON>,
  Scissors,
  Clipboard,
  Link,
  MessageSquare,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  BarChart3,
  FileText,
  BookOpen,
  Sparkles,
  MoreHorizontal,
  ChevronRight,
  Table,
  Columns,
  Quote,
  Code,
  Minus,
  Image,
  Calculator,
  FolderOpen,
  Users,
  Layers,
  AlignLeft,
} from 'lucide-react'

interface MenuItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
  disabled?: boolean
  separator?: boolean
  submenu?: MenuItem[]
}

interface RightClickContextMenuProps {
  editor: Editor
  onAddComment?: (position: number) => void
  onInsertChart?: () => void
  onInsertReportSection?: () => void
  onInsertTableOfContents?: () => void
  onAISuggestion?: () => void
}

interface Position {
  x: number
  y: number
}

export function RightClickContextMenu({
  editor,
  onAddComment,
  onInsertChart,
  onInsertReportSection,
  onInsertTableOfContents,
  onAISuggestion,
}: RightClickContextMenuProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 })
  const [contextMenuPosition, setContextMenuPosition] = useState<number>(0)
  const [adjustedPosition, setAdjustedPosition] = useState<Position>({ x: 0, y: 0 })

  // Get the current selection state
  const isTextSelected = !editor.state.selection.empty
  const canPaste = true // We'll assume paste is always available

  const handleCopy = useCallback(() => {
    document.execCommand('copy')
    setIsVisible(false)
  }, [])

  const handleCut = useCallback(() => {
    document.execCommand('cut')
    setIsVisible(false)
  }, [])

  const handlePaste = useCallback(() => {
    document.execCommand('paste')
    setIsVisible(false)
  }, [])

  const handleSelectAll = useCallback(() => {
    editor.chain().focus().selectAll().run()
    setIsVisible(false)
  }, [editor])

  const handleAddLink = useCallback(() => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleAddComment = useCallback(() => {
    if (onAddComment) {
      onAddComment(contextMenuPosition)
    }
    setIsVisible(false)
  }, [onAddComment, contextMenuPosition])

  const handleHeading = useCallback((level: 1 | 2 | 3) => {
    editor.chain().focus().toggleHeading({ level }).run()
    setIsVisible(false)
  }, [editor])

  const handleList = useCallback((ordered: boolean = false) => {
    if (ordered) {
      editor.chain().focus().toggleOrderedList().run()
    } else {
      editor.chain().focus().toggleBulletList().run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertChart = useCallback(() => {
    if (onInsertChart) {
      onInsertChart()
    } else {
      // Default chart insertion using HTML
      try {
        const chartId = `chart-${Date.now()}`
        console.log('Attempting to insert chart with ID:', chartId)
        
        // Check if the extension is available
        const extensionExists = editor.extensionManager.extensions.find(ext => ext.name === 'chart')
        console.log('Chart extension exists:', !!extensionExists)
        
        // Create sample chart data
        const chartData = {
          title: { text: 'New Chart' },
          xAxis: { type: 'category', data: ['A', 'B', 'C'] },
          yAxis: { type: 'value' },
          series: [{ type: 'bar', data: [10, 20, 30] }]
        }
        const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
        
        // Use HTML insertion instead of JSON object
        const htmlContent = `<chart id="${chartId}" data-json="${encodedData}" data-type="chart"></chart>`
        
        const result = editor.chain().focus().insertContent(htmlContent).run()
        
        console.log('Insert result:', result)
      } catch (error) {
        console.error('Chart extension not available:', error)
      }
    }
    setIsVisible(false)
  }, [editor, onInsertChart])

  const handleInsertReportSection = useCallback(() => {
    if (onInsertReportSection) {
      onInsertReportSection()
    } else {
      // Default report section insertion using JSON node structure (same as toolbar)
      try {
        const sectionId = `section-${Date.now()}`
        const title = 'New Report Section'
        console.log('Attempting to insert report section with ID:', sectionId)
        
        // Check if the extension is available
        const extensionExists = editor.extensionManager.extensions.find(ext => ext.name === 'reportSection')
        console.log('ReportSection extension exists:', !!extensionExists)
        
        // Use JSON node structure (same as toolbar implementation)
        const nodeContent = {
          type: 'reportSection',
          attrs: {
            id: sectionId,
            title: title,
            endpoint: '/api/reports/[ENTITY_ID]/[RUN_ID]/data',
            prompt: null,
          },
          content: [
            {
              type: 'heading',
              attrs: { level: 2 },
              content: [
                {
                  type: 'text',
                  text: title
                }
              ]
            }
          ]
        }
        
        console.log('Inserting node content:', nodeContent)
        
        const result = editor.chain().focus().insertContent(nodeContent).run()
        
        console.log('Insert result:', result)
        
        // Fallback to HTML if JSON didn't work
        if (!result) {
          console.log('JSON insertion failed, trying HTML approach...')
          const htmlContent = `<report-section id="${sectionId}" title="${title}" endpoint="/api/reports/[ENTITY_ID]/[RUN_ID]/data" data-type="reportSection"><h2>${title}</h2></report-section>`
          editor.chain().focus().insertContent(htmlContent).run()
        }
      } catch (error) {
        console.error('Error inserting report section:', error)
      }
    }
    setIsVisible(false)
  }, [editor, onInsertReportSection])

  const handleInsertReportSummary = useCallback(() => {
    const summaryId = `summary-${Date.now()}`
    const title = 'New Report Summary'
    
    try {
      const nodeContent = {
        type: 'reportSummary',
        attrs: {
          id: summaryId,
          title: title,
          prompt: null,
          summarize: null,
        },
        content: [
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [
              {
                type: 'text',
                text: title || 'Report Summary'
              }
            ]
          }
        ]
      }
      
      editor.chain().focus().insertContent(nodeContent).run()
    } catch (error) {
      console.error('Error inserting report summary:', error)
      
      // Fallback to HTML insertion
      const content = `<report-summary id="${summaryId}" title="${title}"><h2>${title}</h2></report-summary>`
      editor.chain().focus().insertContent(content).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertReportGroup = useCallback(() => {
    const groupId = `group-${Date.now()}`
    const title = 'New Report Group'
    
    try {
      const nodeContent = {
        type: 'reportGroup',
        attrs: {
          id: groupId,
          title: title,
        },
        content: [
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [
              {
                type: 'text',
                text: title || 'Report Group'
              }
            ]
          }
        ]
      }
      
      editor.chain().focus().insertContent(nodeContent).run()
    } catch (error) {
      console.error('Error inserting report group:', error)
      
      // Fallback to HTML insertion
      const content = `<report-group id="${groupId}" title="${title}"><h2>${title}</h2></report-group>`
      editor.chain().focus().insertContent(content).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertColumns = useCallback((layout: ColumnLayout) => {
    try {
      editor.chain().focus().insertColumns(layout).run()
    } catch (error) {
      console.error('Columns extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertTable = useCallback(() => {
    try {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    } catch (error) {
      console.error('Table extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertReferences = useCallback(() => {
    try {
      editor.chain().focus().insertContent({
        type: 'references',
        attrs: { id: 'references' }
      }).run()
    } catch (error) {
      console.error('References extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertDetails = useCallback(() => {
    try {
      editor.chain().focus().setDetails().run()
    } catch (error) {
      console.error('Details extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertCodeBlock = useCallback(() => {
    try {
      editor.chain().focus().toggleCodeBlock().run()
    } catch (error) {
      console.error('Code block not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertBlockquote = useCallback(() => {
    try {
      editor.chain().focus().toggleBlockquote().run()
    } catch (error) {
      console.error('Blockquote not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertHorizontalRule = useCallback(() => {
    try {
      editor.chain().focus().setHorizontalRule().run()
    } catch (error) {
      console.error('Horizontal rule not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertImage = useCallback(() => {
    const url = window.prompt('Enter image URL:')
    if (url) {
      try {
        editor.chain().focus().setImage({ src: url }).run()
      } catch (error) {
        console.error('Image extension not available:', error)
      }
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertMath = useCallback(() => {
    try {
      editor.chain().focus().setInlineMath('').run()
    } catch (error) {
      console.error('Math extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertTableOfContents = useCallback(() => {
    if (onInsertTableOfContents) {
      onInsertTableOfContents()
    } else {
      // Default TOC insertion - would need a specific extension
      console.log('Table of Contents insertion requested')
    }
    setIsVisible(false)
  }, [onInsertTableOfContents])

  const handleAISuggestion = useCallback(() => {
    if (onAISuggestion) {
      onAISuggestion()
    } else {
      // Default AI suggestion behavior
      console.log('AI suggestion requested')
    }
    setIsVisible(false)
  }, [onAISuggestion])

  // Build the menu items based on context
  const menuItems: MenuItem[] = useMemo(() => {
    const items: MenuItem[] = []

    // Basic clipboard operations
    if (isTextSelected) {
      items.push({
        id: 'copy',
        label: 'Copy',
        icon: Copy,
        action: handleCopy,
      })
      items.push({
        id: 'cut',
        label: 'Cut',
        icon: Scissors,
        action: handleCut,
      })
    }

    if (canPaste) {
      items.push({
        id: 'paste',
        label: 'Paste',
        icon: Clipboard,
        action: handlePaste,
      })
    }

    if (items.length > 0) {
      items.push({
        id: 'separator1',
        label: '',
        icon: Type,
        action: () => {},
        separator: true,
      })
    }

    items.push({
      id: 'selectAll',
      label: 'Select All',
      icon: Type,
      action: handleSelectAll,
    })

    // Text formatting (only show if text is selected)
    if (isTextSelected) {
      items.push({
        id: 'separator2',
        label: '',
        icon: Type,
        action: () => {},
        separator: true,
      })

      items.push({
        id: 'formatting',
        label: 'Format',
        icon: Type,
        action: () => {},
        submenu: [
          {
            id: 'heading1',
            label: 'Heading 1',
            icon: Heading1,
            action: () => handleHeading(1),
          },
          {
            id: 'heading2',
            label: 'Heading 2',
            icon: Heading2,
            action: () => handleHeading(2),
          },
          {
            id: 'heading3',
            label: 'Heading 3',
            icon: Heading3,
            action: () => handleHeading(3),
          },
          {
            id: 'bulletList',
            label: 'Bullet List',
            icon: List,
            action: () => handleList(false),
          },
          {
            id: 'orderedList',
            label: 'Numbered List',
            icon: ListOrdered,
            action: () => handleList(true),
          },
        ],
      })

      items.push({
        id: 'addLink',
        label: 'Add Link',
        icon: Link,
        action: handleAddLink,
      })

      if (onAddComment) {
        items.push({
          id: 'addComment',
          label: 'Add Comment',
          icon: MessageSquare,
          action: handleAddComment,
        })
      }
    }

    // Insert menu (always available)
    items.push({
      id: 'separator3',
      label: '',
      icon: Type,
      action: () => {},
      separator: true,
    })

    items.push({
      id: 'insert',
      label: 'Insert',
      icon: MoreHorizontal,
      action: () => {},
      submenu: [
        {
          id: 'insertTOC',
          label: 'Table of Contents',
          icon: BookOpen,
          action: handleInsertReferences,
        },
        {
          id: 'insertReportSummary',
          label: 'Report Summary',
          icon: FileText,
          action: handleInsertReportSummary,
        },
        {
          id: 'insertReportSection',
          label: 'Report Section',
          icon: Layers,
          action: handleInsertReportSection,
        },
        {
          id: 'insertReportGroup',
          label: 'Report Group',
          icon: Users,
          action: handleInsertReportGroup,
        },
        {
          id: 'insertColumns',
          label: 'Multi-Columns',
          icon: Columns,
          action: () => {},
          submenu: [
            {
              id: 'columns2Equal',
              label: '2 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-2'),
            },
            {
              id: 'columns3Equal',
              label: '3 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-3'),
            },
            {
              id: 'columns4Equal',
              label: '4 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-4'),
            },
            {
              id: 'columns1-2',
              label: '2 Columns (1:2 Ratio)',
              icon: Columns,
              action: () => handleInsertColumns('ratio-1-2'),
            },
            {
              id: 'columns2-1',
              label: '2 Columns (2:1 Ratio)',
              icon: Columns,
              action: () => handleInsertColumns('ratio-2-1'),
            },
            {
              id: 'columnsCentered',
              label: 'Centered Column',
              icon: AlignLeft,
              action: () => handleInsertColumns('centered'),
            },
          ],
        },
        {
          id: 'insertTable',
          label: 'Table',
          icon: Table,
          action: handleInsertTable,
        },
        {
          id: 'insertChart',
          label: 'Chart',
          icon: BarChart3,
          action: handleInsertChart,
        },
        {
          id: 'separator4',
          label: '',
          icon: Type,
          action: () => {},
          separator: true,
        },
        {
          id: 'insertDetails',
          label: 'Collapsible Section',
          icon: FolderOpen,
          action: handleInsertDetails,
        },
        {
          id: 'insertCodeBlock',
          label: 'Code Block',
          icon: Code,
          action: handleInsertCodeBlock,
        },
        {
          id: 'insertBlockquote',
          label: 'Quote',
          icon: Quote,
          action: handleInsertBlockquote,
        },
        {
          id: 'insertMath',
          label: 'Math Expression',
          icon: Calculator,
          action: handleInsertMath,
        },
        {
          id: 'insertHorizontalRule',
          label: 'Horizontal Rule',
          icon: Minus,
          action: handleInsertHorizontalRule,
        },
        {
          id: 'insertImage',
          label: 'Image',
          icon: Image,
          action: handleInsertImage,
        },
      ],
    })

    // AI features
    if (onAISuggestion) {
      items.push({
        id: 'aiSuggestion',
        label: 'AI Suggestions',
        icon: Sparkles,
        action: handleAISuggestion,
      })
    }

    return items
  }, [
    isTextSelected,
    canPaste,
    handleCopy,
    handleCut,
    handlePaste,
    handleSelectAll,
    handleAddLink,
    handleAddComment,
    handleHeading,
    handleList,
    handleInsertChart,
    handleInsertReportSection,
    handleInsertReportSummary,
    handleInsertReportGroup,
    handleInsertColumns,
    handleInsertTable,
    handleInsertReferences,
    handleInsertDetails,
    handleInsertCodeBlock,
    handleInsertBlockquote,
    handleInsertHorizontalRule,
    handleInsertImage,
    handleInsertMath,
    handleInsertTableOfContents,
    handleAISuggestion,
    onAddComment,
    onAISuggestion,
  ])

  // Calculate adjusted position to keep menu within viewport
  useEffect(() => {
    if (!isVisible) return

    const menuWidth = 192 // min-w-48 = 12rem = 192px
    const menuHeight = 500 // Approximate max height
    const padding = 8

    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let adjustedX = position.x
    let adjustedY = position.y

    // Check right boundary
    if (adjustedX + menuWidth + padding > viewportWidth) {
      adjustedX = viewportWidth - menuWidth - padding
    }

    // Check bottom boundary
    if (adjustedY + menuHeight + padding > viewportHeight) {
      adjustedY = viewportHeight - menuHeight - padding
    }

    // Ensure menu doesn't go off the left or top
    adjustedX = Math.max(padding, adjustedX)
    adjustedY = Math.max(padding, adjustedY)

    setAdjustedPosition({ x: adjustedX, y: adjustedY })
  }, [position, isVisible])

  // Handle right-click events
  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      // Only handle right-clicks within the editor
      const editorElement = editor.view.dom
      if (!editorElement.contains(event.target as Node)) {
        return
      }

      event.preventDefault()
      
      // Get the position in the editor where the right-click occurred
      const pos = editor.view.posAtCoords({ left: event.clientX, top: event.clientY })
      if (pos) {
        setContextMenuPosition(pos.pos)
      }

      setPosition({ x: event.clientX, y: event.clientY })
      setIsVisible(true)
    }

    const handleClick = () => {
      setIsVisible(false)
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsVisible(false)
      }
    }

    document.addEventListener('contextmenu', handleContextMenu)
    document.addEventListener('click', handleClick)
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu)
      document.removeEventListener('click', handleClick)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [editor])

  // Render the context menu
  const renderMenuItem = (item: MenuItem, isSubmenu = false) => {
    if (item.separator) {
      return (
        <div key={item.id} className="h-px bg-gray-200 my-1" />
      )
    }

    const Icon = item.icon
    const hasSubmenu = item.submenu && item.submenu.length > 0

    return (
      <div key={item.id} className="relative group">
        <button
          data-testid={`context-menu-${item.id}`}
          onClick={hasSubmenu ? undefined : item.action}
          disabled={item.disabled}
          className={`
            w-full flex items-center gap-2 px-3 py-2 text-sm text-left
            hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed
            ${hasSubmenu ? 'cursor-default' : 'cursor-pointer'}
          `}
        >
          <Icon className="w-4 h-4 text-gray-600" />
          <span className="flex-1">{item.label}</span>
          {hasSubmenu && <ChevronRight className="w-4 h-4 text-gray-400" />}
        </button>

        {hasSubmenu && (
          <div
            data-testid={`context-submenu-${item.id}`}
            className="absolute left-full top-0 ml-1 hidden group-hover:block z-50"
          >
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-48">
              {item.submenu!.map(subItem => renderMenuItem(subItem, true))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (!isVisible || !editor || editor.isDestroyed) {
    return null
  }

  return createPortal(
    <div
      data-testid="right-click-context-menu"
      className="fixed bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-48 z-50"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
      onContextMenu={(e) => e.preventDefault()}
    >
      {menuItems.map(item => renderMenuItem(item))}
    </div>,
    document.body
  )
}
