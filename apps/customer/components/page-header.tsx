"use client";
import React from 'react'
import { SidebarTrigger } from '@ui/components/ui/sidebar'
import { Separator } from '@ui/components/ui/separator'
import { useNav } from '@/components/context/nav/nav-context'


export interface NavigationItem {
    label: React.ReactNode | undefined;
    href?: string | undefined;
}

export function PageHeader({navPath, children, title}: {
    navPath?: NavigationItem[],
    children?: React.ReactNode,
    title?: string | undefined
}) {

    const nav = useNav();

    if (!title) {
        title = nav.title;
    }

    if (!navPath) {
        navPath = nav.path;
    }

    return <header
      className="dashboard-header flex h-16 shrink-0 items-center mx-auto mb-4 text-foreground w-full sticky top-0 z-10">


        <div className="mx-auto flex p-2 px-6 items-center gap-2 glass-effect-subtle-lit rounded-2xl rounded-bl-2xl ">
            <SidebarTrigger className="-ml-1 mr-2"/>
            {children && (<>
                <Separator orientation="vertical" className=" h-6 mx-2"/>
                <div className="items-center gap-2 flex-grow">
                    {children}
                </div>
            </>)
            }
            <Separator orientation="vertical" className="hidden xl:flex flex-shrink h-6 mx-2"/>
            <div className="hidden xl:flex flex-grow align-middle text-center">
                {nav.path.length > 0 && nav.path[nav.path.length - 1].label ?
                    nav.path[nav.path.length - 1].label :
                    (title || (typeof window !== 'undefined' && window.location.pathname))}
            </div>
        </div>
    </header>
        ;
}
