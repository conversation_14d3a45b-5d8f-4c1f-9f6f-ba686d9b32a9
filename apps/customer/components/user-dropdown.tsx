"use client";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@ui/components/ui/dropdown-menu";
import {Button} from "@ui/components/ui/button";
import {UserIcon} from "lucide-react";
import {createClient} from "@/app/supabase/client";
import {toast} from "sonner";
import {useRouter} from "next/navigation";
import {type User} from "@supabase/supabase-js";

export function UserDropdown(props: { user: User | null }) {
    const router = useRouter();
    const supabase = createClient();

    async function signOut() {
        const {error} = await supabase.auth.signOut()
        if (error) {
            toast.error("" + error);
        } else {
            window.location.href = ('/');
        }
    }

    return <DropdownMenu>
        <DropdownMenuTrigger asChild>
            <Button 
                className="rounded-full" 
                size="icon" 
                variant="ghost"
                data-testid="user-dropdown-trigger"
            >
                <UserIcon size={32} fill="white" stroke="white" className="hover:fill-gray-500 hover:stroke-gray-400"/>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" data-testid="user-dropdown-menu">
            {props.user &&
                <DropdownMenuLabel data-testid="user-info">Signed in as {props.user?.email}</DropdownMenuLabel>
            }

            {!props.user &&
                <DropdownMenuLabel className="hover:cursor-pointer" onClick={() => window.location.href = ('/login')}>Sign
                    In</DropdownMenuLabel>
            }
            <DropdownMenuSeparator/>
            <DropdownMenuItem disabled>Profile</DropdownMenuItem>
            <DropdownMenuItem disabled>Settings</DropdownMenuItem>
            <DropdownMenuSeparator/>
            <DropdownMenuItem 
                onClick={signOut} 
                disabled={!supabase.auth.getUser()}
                data-testid="menu-sign-out"
            >
                Logout
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>;
}
