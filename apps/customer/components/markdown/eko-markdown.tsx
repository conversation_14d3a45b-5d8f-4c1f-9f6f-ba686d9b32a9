import React, { useEffect, useMemo, useRef, useState } from 'react'
import { citationLink, CitationType } from '@/components/citation'
import Markdown, { Components } from 'react-markdown'
import { cn } from '@utils/lib/utils'
import rehypeRaw from 'rehype-raw'
import { DomainBadgeCitation } from '@/components/citation/domain-badge-citation'
import ReactECharts from 'echarts-for-react'
import { jsonrepair } from 'jsonrepair'
import remarkGfmTables from '@/components/markdown/remark-tables'

/* ------------------------------------------------------------------
 *  Shared, compiled‑once regexes
 * ------------------------------------------------------------------*/
const singleCitationRegex = /\[\^(\d+)\]/g
const multipleCitationRegex = /\[\^(\d+(?:,\s*\^?\d+)*)\]/g

/* ------------------------------------------------------------------
 *  Chart renderer – heavy work done in useMemo
 * ------------------------------------------------------------------*/
const Chart = React.memo<{ children: string }>(({ children }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isContainerReady, setIsContainerReady] = useState(false)

  const option = useMemo(() => {
    console.log('Rendering chart', children)
    try {
      return typeof children === 'string' ? JSON.parse(jsonrepair(children)) : null
    } catch (err) {
      console.warn('Failed to parse chart JSON', err)
      return null
    }
  }, [children])

  // Check if container has proper dimensions
  useEffect(() => {
    const checkDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current
        if (clientWidth > 0 && clientHeight > 0) {
          setIsContainerReady(true)
        }
      }
    }

    const timeoutId = setTimeout(checkDimensions, 100)
    const intervalId = setInterval(checkDimensions, 200)

    // Clean up
    const cleanup = () => {
      clearTimeout(timeoutId)
      clearInterval(intervalId)
    }

    setTimeout(cleanup, 2000) // Stop checking after 2 seconds
    return cleanup
  }, [])

  if (!option) return null

  return (
    <div ref={containerRef} style={{ minHeight: '400px', width: '100%' }}>
      {isContainerReady ? (
        <ReactECharts
          className="py-8"
          style={{ minHeight: '400px', width: '100%' }}
          option={option}
          opts={{ renderer: 'canvas' }}
        />
      ) : (
        <div className="flex items-center justify-center h-96 text-gray-500">
          Loading chart...
        </div>
      )}
    </div>
  )
});

/* ------------------------------------------------------------------
 *  Types & helpers
 * ------------------------------------------------------------------*/
export type EkoMarkdownProps = {
  citations: CitationType[] | null;
  children: any;
  admin: boolean;
  className?: string;
  citationPrefix?: string;
  inlineCitations?: boolean;
  badgeStyle?: boolean;
  skipCitations?: boolean;
};

const arePropsEqual = (prev: EkoMarkdownProps, next: EkoMarkdownProps) => {
  // bail early if markdown text changed
  if (prev.children !== next.children) return false
  // citations array ref or length changed
  if (prev.citations?.length !== next.citations?.length) return false
  // shallow compare other simple props
  return (
    prev.admin === next.admin &&
    prev.inlineCitations === next.inlineCitations &&
    prev.badgeStyle === next.badgeStyle &&
    prev.skipCitations === next.skipCitations
  )
}

/* ------------------------------------------------------------------
 *  Main component
 * ------------------------------------------------------------------*/
export const EkoMarkdown = React.memo((props: EkoMarkdownProps) => {
  const {
    citations,
    children,
    admin,
    inlineCitations = true,
    badgeStyle = true,
    citationPrefix = '',
    className = '',
    skipCitations = false,
  } = props;

  /* --------------------------------------------------------------
   *  Early short‑circuit when we want raw markdown without citations
   * --------------------------------------------------------------*/
  if (skipCitations) {
    return (
      <div className={cn(className, 'max-w-full prose dark:text-foreground EkoMarkdown')}>
        <Markdown
          rehypePlugins={[rehypeRaw]}
          remarkPlugins={[remarkGfmTables]}
          components={{
            chart: Chart,
          } as Components}
        >{typeof children === 'string' ? children.replaceAll(/\[\^.*]/g, '') : children}</Markdown>
      </div>
    );
  }

  /* --------------------------------------------------------------
   *  Build maps so we can look up citations in O(1)
   * --------------------------------------------------------------*/
  const citationByPageId = useMemo(() => {
    if (!citations) return new Map<number, CitationType>()
    return new Map(citations.map(c => [c.doc_page_id, c]))
  }, [citations])

  /* --------------------------------------------------------------
   *  Process markdown only when its inputs truly change
   * --------------------------------------------------------------*/
  const { content, footnotes } = useMemo(() => {
    /** Ensure we never mutate props.citations */
    const footnotesArr: {
      id: number;
      alt_id: number;
      index: number;
      citation: CitationType;
      year: string | number;
      text: string;
      url: string;
    }[] = []

    if (!children) return { content: '', footnotes: footnotesArr }
    let md = typeof children === 'string' ? children : String(children)

    const allIds: number[] = []

    // grab single citations
    for (const m of md.matchAll(singleCitationRegex)) allIds.push(+m[1])
    // grab multi‑citations
    for (const m of md.matchAll(multipleCitationRegex)) {
      const parts = m[1].split(',').map(p => p.trim().replace('^', ''))
      parts.forEach(id => allIds.push(+id))
    }

    // dedupe & sort
    const uniqueIds = Array.from(new Set(allIds)).sort((a, b) => a - b)

    /** Build footnotes */
    uniqueIds.forEach((id, i) => {
      const c = citationByPageId.get(id) || citations?.find(x => x.doc_id === id)
      if (!c) return
      footnotesArr.push({
        id: c.doc_page_id,
        alt_id: c.doc_id,
        index: i + 1,
        citation: c,
        year: c.year || '',
        text: c.title,
        url: citationLink(c, admin),
      })
    })

    // helper – tiny, fast, no regex in loops
    const replaceSingle = (idStr: string) => {
      const id = +idStr
      const fn = footnotesArr.find(f => f.id === id || f.alt_id === id)
      if (!fn) return ''
      if (inlineCitations) {
        if (badgeStyle)
          return `<domain-badge-citation id="${fn.id}" display="${fn.index}" footnote="${id}"></domain-badge-citation>`
        return `[(${fn.text.replace(/\d{4}$/, '')}, ${fn.year})](${fn.url})`
      }
      return ` [[${citationPrefix}${fn.index}]](${fn.url})`
    }

    // multi‑citations
    md = md.replace(multipleCitationRegex, (_, list: string) => {
      if (!list.includes(',')) return _ // single – leave for next pass
      const ids = list
        .split(',')
        .map(p => p.trim().replace('^', ''))
        .sort((a, b) => +a - +b)
      return ids.map(replaceSingle).join('')
    })

    // single citations
    md = md.replace(singleCitationRegex, (_, id: string) => replaceSingle(id))

    return { content: md, footnotes: footnotesArr }
  }, [children, citations, inlineCitations, badgeStyle, citationPrefix, admin, citationByPageId]);

  /* --------------------------------------------------------------
   *  Helpers that depend on footnotes (stable)
   * --------------------------------------------------------------*/
  const footnoteMap = useMemo(() => {
    const m = new Map<number, typeof footnotes[0]>()
    footnotes.forEach(f => {
      m.set(f.id, f)
      m.set(f.alt_id, f)
    })
    return m
  }, [footnotes])

  const DomainBadge = (el: any) => {
    const fn = footnoteMap.get(+el.footnote)
    if (!fn) return null
    return (
      <DomainBadgeCitation
        key={`citation-${fn.id}`}
        citation={fn.citation}
        admin={admin}
        display={el.display}
        showYear
      />
    )
  }

  const ReportSection = (el: any) => {
    console.log('Rendering ReportSection', el)
    return (
      <span className="report-section">
        <a id={`${el.id}`}>
          <span className="report-section-title block heading-2">{el.title}</span>
        </a>
        {el.children}
      </span>
    )
  }

  const ReportSubSection = (el: any) => {
    console.log('Rendering ReportSection', el)
    return (
      <span className="report-sub-section">
        <a id={`${el.id}`}>
          <span className="report-section-title block heading-3">{el.title}</span>
        </a>
        {el.children}
      </span>
    )
  }

  /* --------------------------------------------------------------
   *  Render
   * --------------------------------------------------------------*/
  console.log('Rendering EkoMarkdown')
  return (
    <div className={cn(className, 'relative max-w-full prose dark:text-foreground EkoMarkdown')}>
      <Markdown
        rehypePlugins={[rehypeRaw]}
        remarkPlugins={[remarkGfmTables]}
        components={{
          'domain-badge-citation': DomainBadge,
          chart: Chart,
          'report-section': ReportSection,
          'report-sub-section': ReportSubSection,
        } as Components}
      >
        {content}
      </Markdown>

      {/* Footnote list when inlineCitations is off */}
      {!inlineCitations && citations && citations.length > 0 && (
        <div className="mt-2">
          <hr className="lg:max-w-96 mt-4" />
          <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-700">References</h2>
          <ul className="mt-1 mb-4 prose dark:text-foreground">
            {citations.map((c, i) => {
              const referenced = footnoteMap.has(c.doc_page_id) || footnoteMap.has(c.doc_id)
              console.log('referenced', c.doc_page_id, referenced)
              return (
                <li key={c.doc_page_id} className={`text-xs ${referenced ? 'font-medium' : 'text-muted-foreground'}`}>
                  {citationPrefix}
                  {i + 1}.{' '}
                  <a
                    id={`footnote-${c.doc_page_id}`}
                    className={`font-light no-underline hover:text-blue-500 ${referenced ? '' : 'text-muted-foreground hover:text-blue-400'}`}
                    href={citationLink(c, admin)}
                  >
                    {c.title}
                  </a>{' '}
                  p. {c.page + 1}
                  {referenced && <span className="ml-2 text-green-600">✓</span>}
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}, arePropsEqual);
