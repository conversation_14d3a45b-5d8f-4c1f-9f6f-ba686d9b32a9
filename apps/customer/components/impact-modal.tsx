"use client"

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@ui/components/ui/dialog'
import { Badge } from '@ui/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Progress } from '@ui/components/ui/progress'
import { Separator } from '@ui/components/ui/separator'
import { Button } from '@ui/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@ui/components/ui/collapsible'
import {
  Heart,
  Users,
  Leaf,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Info,
  Shield,
  Scale,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { EventImpactMeasurement, EventImpactEvaluation, DimensionAssessment, ImpactAssessment } from '@/types'
import { ScaleFactorsDisplay } from './scale-factors'

interface ImpactModalProps {
  isOpen: boolean
  onClose: () => void
  measurement?: EventImpactMeasurement
  evaluation?: EventImpactEvaluation
  flagTitle: string
}

const dimensionIcons = {
  animals: Heart,
  humans: Users,
  environment: Leaf
}

const confidenceColors = {
  high: 'text-green-600 bg-green-50 border-green-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  low: 'text-red-600 bg-red-50 border-red-200'
}

const qualityColors = {
  excellent: 'text-green-600 bg-green-50 border-green-200',
  good: 'text-blue-600 bg-blue-50 border-blue-200',
  fair: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  poor: 'text-red-600 bg-red-50 border-red-200'
}

function DimensionCard({
  dimension,
  assessment,
  type
}: {
  dimension: keyof ImpactAssessment
  assessment: DimensionAssessment
  type: 'harm' | 'benefit'
}) {
  const [showScaleFactors, setShowScaleFactors] = React.useState(false)
  const Icon = dimensionIcons[dimension]
  const isHarm = type === 'harm'
  const scoreColor = isHarm
    ? assessment.score > 0.6 ? 'text-red-600' : assessment.score > 0.3 ? 'text-orange-500' : 'text-gray-600'
    : assessment.score > 0.6 ? 'text-green-600' : assessment.score > 0.3 ? 'text-blue-500' : 'text-gray-600'

  const progressColor = isHarm ? 'bg-red-500' : 'bg-green-500'
  
  return (
    <Card className="glass-card">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Icon className="w-4 h-4" />
          {dimension.charAt(0).toUpperCase() + dimension.slice(1)}
          <Badge 
            variant="outline" 
            className={cn("ml-auto text-xs", confidenceColors[assessment.confidence])}
          >
            {assessment.confidence} confidence
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Impact Score</span>
          <span className={cn("text-lg font-bold", scoreColor)}>
            {(assessment.score * 100).toFixed(0)}%
          </span>
        </div>
        
        <div className="space-y-1">
          <Progress 
            value={assessment.score * 100} 
            className="h-2"
            style={{
              '--progress-background': progressColor
            } as React.CSSProperties}
          />
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p className="line-clamp-3">{assessment.reasoning}</p>
        </div>
        
        <div className="space-y-2 text-xs">
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span className="font-medium">Timeline:</span>
          </div>
          <div className="pl-4 space-y-1 text-muted-foreground">
            <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate}</div>
            <div><strong>Medium-term:</strong> {assessment.temporal_breakdown.medium_term}</div>
            <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term}</div>
          </div>
        </div>

        {/* Scale Factors Section */}
        {assessment.scale_factors && (
          <Collapsible open={showScaleFactors} onOpenChange={setShowScaleFactors}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-between text-xs p-2 h-auto"
              >
                <span className="flex items-center gap-1">
                  <Scale className="w-3 h-3" />
                  Scale Factors
                </span>
                {showScaleFactors ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2">
              <div className="space-y-2 text-xs">
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Directness</div>
                    <div className="flex items-center gap-1">
                      <Progress value={assessment.scale_factors.directness} className="flex-1 h-1" />
                      <span className="text-xs">{assessment.scale_factors.directness}%</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Authenticity</div>
                    <div className="flex items-center gap-1">
                      <Progress value={assessment.scale_factors.authenticity} className="flex-1 h-1" />
                      <span className="text-xs">{assessment.scale_factors.authenticity}%</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Deliberateness</div>
                    <div className="flex items-center gap-1">
                      <Progress value={assessment.scale_factors.deliberateness} className="flex-1 h-1" />
                      <span className="text-xs">{assessment.scale_factors.deliberateness}%</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-muted-foreground">Contribution</div>
                    <div className="flex items-center gap-1">
                      <Progress value={assessment.scale_factors.contribution} className="flex-1 h-1" />
                      <span className="text-xs">{assessment.scale_factors.contribution}%</span>
                    </div>
                  </div>
                </div>
                <div className="pt-1 space-y-1 text-muted-foreground">
                  <div><strong>Scale:</strong> {assessment.scale_factors.scale_of_impact}</div>
                  <div><strong>Duration:</strong> {assessment.scale_factors.duration}</div>
                  <div><strong>Reversibility:</strong> {assessment.scale_factors.reversibility}%</div>
                  <div><strong>Scope:</strong> {assessment.scale_factors.scope}%</div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
  )
}

function NetImpactVisualization({ measurement }: { measurement: EventImpactMeasurement }) {
  const netScore = measurement.net_impact_score
  const isPositive = netScore > 0
  const absScore = Math.abs(netScore)
  
  return (
    <Card className="glass-card bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Scale className="w-5 h-5" />
          Net Impact Assessment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-center">
          <div className={cn(
            "text-6xl font-bold flex items-center gap-2",
            isPositive ? "text-green-600" : "text-red-600"
          )}>
            {isPositive ? <TrendingUp className="w-8 h-8" /> : <TrendingDown className="w-8 h-8" />}
            {(absScore * 100).toFixed(0)}%
          </div>
        </div>
        
        <div className="text-center">
          <p className={cn(
            "text-lg font-medium",
            isPositive ? "text-green-700" : "text-red-700"
          )}>
            {isPositive ? "Net Positive Impact" : "Net Negative Impact"}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Based on comprehensive analysis across all dimensions
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {(measurement.harm_score * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-muted-foreground">Average Harm</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {(measurement.benefit_score * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-muted-foreground">Average Benefit</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function QualityMetrics({ evaluation }: { evaluation: EventImpactEvaluation }) {
  const biasIssues = evaluation.bias_detection.detected_issues
  const consistencyIssues = evaluation.consistency_checks.issues
  
  return (
    <Card className="glass-card">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Assessment Quality
          <Badge 
            variant="outline" 
            className={cn("ml-auto", qualityColors[evaluation.overall_quality])}
          >
            {evaluation.overall_quality}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-muted-foreground">Completeness</div>
            <div className="flex items-center gap-2">
              <Progress value={evaluation.completeness_score * 100} className="flex-1 h-2" />
              <span className="text-sm font-medium">
                {(evaluation.completeness_score * 100).toFixed(0)}%
              </span>
            </div>
          </div>
          
          <div>
            <div className="text-sm text-muted-foreground">Average Confidence</div>
            <div className="flex items-center gap-2">
              <Progress value={evaluation.confidence_analysis.avg_confidence * 100} className="flex-1 h-2" />
              <span className="text-sm font-medium">
                {(evaluation.confidence_analysis.avg_confidence * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <CheckCircle className={cn(
              "w-4 h-4",
              evaluation.consistency_checks.score_alignment ? "text-green-500" : "text-red-500"
            )} />
            <span className="text-sm">Score Alignment</span>
          </div>
          
          <div className="flex items-center gap-2">
            <CheckCircle className={cn(
              "w-4 h-4",
              evaluation.consistency_checks.temporal_consistency ? "text-green-500" : "text-red-500"
            )} />
            <span className="text-sm">Temporal Consistency</span>
          </div>
          
          <div className="flex items-center gap-2">
            <CheckCircle className={cn(
              "w-4 h-4",
              evaluation.consistency_checks.dimensional_balance ? "text-green-500" : "text-red-500"
            )} />
            <span className="text-sm">Dimensional Balance</span>
          </div>
        </div>
        
        {(biasIssues.length > 0 || consistencyIssues.length > 0) && (
          <div className="space-y-2">
            <Separator />
            <div className="flex items-center gap-2 text-amber-600">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm font-medium">Quality Issues</span>
            </div>
            <div className="space-y-1 text-xs text-muted-foreground">
              {consistencyIssues.map((issue, i) => (
                <div key={i}>• {issue}</div>
              ))}
              {biasIssues.map((issue, i) => (
                <div key={i}>• {issue}</div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function ImpactModal({ isOpen, onClose, measurement, evaluation, flagTitle }: ImpactModalProps) {
  if (!measurement) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto glass-modal">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Impact Assessment Unavailable
            </DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center text-muted-foreground">
            <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Detailed impact measurement data is not available for this flag.</p>
            <p className="text-sm mt-2">This feature requires enhanced impact analysis to be enabled.</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto glass-modal">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Scale className="w-5 h-5" />
            Impact Assessment: {flagTitle}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Overview Section */}
          <NetImpactVisualization measurement={measurement} />
          
          {/* Event Summary */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg">Event Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{measurement.event_summary}</p>
            </CardContent>
          </Card>
          
          {/* Harm Assessment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-red-700 dark:text-red-400">
              <TrendingDown className="w-5 h-5" />
              Harm Assessment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <DimensionCard 
                dimension="animals" 
                assessment={measurement.harm_assessment.animals} 
                type="harm" 
              />
              <DimensionCard 
                dimension="humans" 
                assessment={measurement.harm_assessment.humans} 
                type="harm" 
              />
              <DimensionCard 
                dimension="environment" 
                assessment={measurement.harm_assessment.environment} 
                type="harm" 
              />
            </div>
          </div>
          
          {/* Benefit Assessment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-green-700 dark:text-green-400">
              <TrendingUp className="w-5 h-5" />
              Benefit Assessment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <DimensionCard
                dimension="animals"
                assessment={measurement.benefit_assessment.animals}
                type="benefit"
              />
              <DimensionCard
                dimension="humans"
                assessment={measurement.benefit_assessment.humans}
                type="benefit"
              />
              <DimensionCard
                dimension="environment"
                assessment={measurement.benefit_assessment.environment}
                type="benefit"
              />
            </div>
          </div>

          {/* Scale Factors Overview */}
          {(measurement.harm_assessment.animals.scale_factors ||
            measurement.benefit_assessment.animals.scale_factors) && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2 text-slate-700 dark:text-slate-300">
                <Scale className="w-5 h-5" />
                Scale Factors Analysis
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {measurement.harm_assessment.animals.scale_factors && (
                  <ScaleFactorsDisplay
                    scaleFactors={measurement.harm_assessment.animals.scale_factors}
                    className="border-red-200 dark:border-red-800"
                  />
                )}
                {measurement.benefit_assessment.animals.scale_factors && (
                  <ScaleFactorsDisplay
                    scaleFactors={measurement.benefit_assessment.animals.scale_factors}
                    className="border-green-200 dark:border-green-800"
                  />
                )}
              </div>
            </div>
          )}
          
          {/* Quality Metrics */}
          {evaluation && <QualityMetrics evaluation={evaluation} />}
          
          {/* Uncertainties and Ethical Considerations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {measurement.key_uncertainties.length > 0 && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-amber-500" />
                    Key Uncertainties
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    {measurement.key_uncertainties.map((uncertainty, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-amber-500 mt-1">•</span>
                        <span>{uncertainty}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
            
            {measurement.ethical_considerations.length > 0 && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Scale className="w-5 h-5 text-purple-500" />
                    Ethical Considerations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    {measurement.ethical_considerations.map((consideration, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-purple-500 mt-1">•</span>
                        <span>{consideration}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
          
          {/* Metadata */}
          <Card className="glass-card bg-muted/20">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-muted-foreground">
                <div>
                  <div className="font-medium">Model Used</div>
                  <div>{measurement.model_used}</div>
                </div>
                <div>
                  <div className="font-medium">Assessed At</div>
                  <div>{new Date(measurement.assessed_at).toLocaleDateString()}</div>
                </div>
                <div>
                  <div className="font-medium">Prompt Version</div>
                  <div>{measurement.prompt_version}</div>
                </div>
                {evaluation && (
                  <div>
                    <div className="font-medium">Quality Score</div>
                    <div>{(evaluation.overall_quality_score * 100).toFixed(0)}%</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}