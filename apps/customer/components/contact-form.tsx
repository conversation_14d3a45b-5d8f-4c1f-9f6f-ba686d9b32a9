import {useState} from "react";
import {useToast} from "@ui/hooks/use-toast";
import {useAuth} from "@/components/context/auth/auth-context";
import {useParams, useSearchParams} from "next/navigation";
import {sendToSlack} from "@/app/customer/slack";
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@ui/components/ui/card";
import {Textarea} from "@ui/components/ui/textarea";
import {Button} from "@ui/components/ui/button";
import {Send} from "lucide-react";

export function ContactForm(props: {}) {
    const [message, setMessage] = useState('')
    const [isSubmitting, setIsSubmitting] = useState(false)
    const {toast} = useToast()
    const {user} = useAuth()
    const searchParams = useSearchParams();
    const params= useParams();
    const prefix = params.prefix || 'Contact';
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!message.trim()) {
            toast({
                title: "Error",
                description: "Please enter a message before submitting.",
                variant: "destructive",
            })
            return
        }
        setIsSubmitting(true)
        // Simulating an API call
        await sendToSlack(prefix + ": The user " + user?.email + " sent this message:\n ```" + message + "```\n")
        toast({
            title: "Message Sent",
            description: "Thank you for contacting us. We'll get back to you soon!",
        })
        setMessage('')
        setIsSubmitting(false)
    }

    return <Card className="max-w-2xl mx-auto">
        <CardHeader>
            <CardTitle className="text-2xl">Contact Us</CardTitle>
            <CardDescription>We'd love to hear from you. Send us a message and we'll respond as soon as
                possible.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
            <CardContent>
                <div className="space-y-4">
                    <Textarea
                        placeholder="Type your message here..."
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        rows={6}
                        className="resize-none"
                    />
                </div>
            </CardContent>
            <CardFooter>
                <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? (
                        <>Sending...</>
                    ) : (
                        <>
                            Send Message
                            <Send className="ml-2 h-4 w-4"/>
                        </>
                    )}
                </Button>
            </CardFooter>
        </form>
    </Card>;
}
