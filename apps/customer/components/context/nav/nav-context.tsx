// context/UserContext.tsx
'use client';

import React, { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { NavigationItem } from '@/components/page-header'
import { reverseNavigationMap } from '@/app/customer/navigation'

export interface NavigationContextType {
    readonly path: NavigationItem[],
    readonly title?: string,
    readonly router: ReturnType<typeof useRouter>,
    changeTitle: (title: string) => void,
    changeNavPath: (navPath: NavigationItem[]) => void,
}

const NavContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
    children: ReactNode;
}

function removeLastPathSegment(path:string) {
    const segments = path.split('/');
    segments.pop(); // Remove the last segment
    return segments.join('/');
}


export const NavigationProvider = ({children}: NavigationProviderProps) => {
    const pathname = usePathname();
    const [navPath, setNavPath] = useState<NavigationItem[]>([]);
    const [title, setTitle] = useState<string | undefined>(undefined);
    const router = useRouter();


  useEffect(() => {

        let path = pathname;
        while (path && path.startsWith('/') && path.length > 1) {
            let titleFromPath = reverseNavigationMap.get(path)?.title;
            if (titleFromPath) {
                setTitle(titleFromPath);
                break;
            }
            path = removeLastPathSegment(path);
        }
    }, [pathname]);


  const changeTitle = useCallback((newTitle: string) => {
        setTitle(newTitle);
  }, []);

  const changeNavPath = useCallback((newPath: NavigationItem[]) => {
        setNavPath(newPath);
  }, []);

    return (
      <NavContext.Provider value={{ router, title, path: navPath, changeTitle, changeNavPath }}>
            {children}
        </NavContext.Provider>
    );
};

export const useNav = (): NavigationContextType => {
    const context = useContext(NavContext);
    if (context === undefined) {
        throw new Error('useNav must be used within a NavigationProvider');
    }
    return context;
};
