// context/UserContext.tsx
'use client'

import React, { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { useQueryStates, parseAsString, parseAsBoolean, createSerializer } from 'nuqs'
import { CherryTypeV2, FlagTypeV2, ModelSectionType, PromiseTypeV2, RunType, ScoreTypeV2, VagueType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { runAsync } from '@utils/react-utils'
import { convertEntityV2ToEntityV1 } from '@/utils/entity-converter'
import { convertRunV2ToRunV1 } from '@/utils/run-utils'
import {
  CherryDataFetcher,
  <PERSON>laimsDataFetcher,
  FlagsDataFetcher,
  ModelSectionsDataFetcher,
  PromisesDataFetcher,
  ScoreDataFetcher,
  VagueDataFetcher,
} from './data'

export interface EntityContextType {
  entity: string | null;
  entityData: any | null;
  run: string | 'latest';
  model: string | 'ekoIntelligence';
  includeDisclosures: boolean;
  changeParams: (newParams: { key: string, value: string | null }[]) => void;
  toggleDisclosures: (include: boolean) => void;
  queryString: string | null;
  hash: () => string;
  runObject: RunType | null;
  flagsData: FlagTypeV2[] | null;
  isLoadingFlags: boolean;
  promisesData: PromiseTypeV2[] | null;
  isLoadingPromises: boolean;
  cherryData: CherryTypeV2[] | null;
  isLoadingCherry: boolean;
  claimsData: ClaimTypeV2[] | null;
  isLoadingClaims: boolean;
  modelSectionsData: ModelSectionType[] | null;
  isLoadingModelSections: boolean;
  score: number;
  scoreData: ScoreTypeV2 | null;
  isLoadingScore: boolean;
  vagueData: VagueType | null;
  vagueDetailData: VagueType[] | null;
  isLoadingVague: boolean;
  isLoading: () => boolean;
}

const DEFAULT_MODEL = 'sdg'

const EntityContext = createContext<EntityContextType | undefined>(undefined)

interface EntityProviderProps {
  children: ReactNode;
}

export const EntityProvider = ({ children }: EntityProviderProps) => {
  const supabase = createClient()
  
  // Define parsers for entity-relevant parameters
  const entityParsers = {
    entity: parseAsString,
    run: parseAsString.withDefault('latest'),
    model: parseAsString.withDefault(DEFAULT_MODEL),
    disclosures: parseAsBoolean.withDefault(true)
  }
  
  // Use nuqs to manage only entity-relevant URL parameters
  const [urlParams, setUrlParams] = useQueryStates(entityParsers)

  const { entity, run, model, disclosures: includeDisclosures } = urlParams
  
  // Generate queryString containing only entity-relevant parameters
  const queryString = React.useMemo(() => {
    const params = new URLSearchParams()
    
    // Only include entity-relevant parameters
    if (entity) params.set('entity', entity)
    if (run !== 'latest') params.set('run', run)
    if (model !== DEFAULT_MODEL) params.set('model', model)
    if (includeDisclosures === false) params.set('disclosures', 'false')
    
    return params.toString()
  }, [entity, run, model, includeDisclosures])

  const [entityData, setEntityData] = useState<any | null>(null)
  const [runs, setRuns] = useState<RunType[]>([])
  const [runObject, setRunObject] = useState<null | RunType>(null)
  const [flagsData, setFlagsData] = useState<FlagTypeV2[] | null>(null)
  const [isLoadingFlags, setIsLoadingFlags] = useState<boolean>(false)
  const [promisesData, setPromisesData] = useState<PromiseTypeV2[] | null>(null)
  const [isLoadingPromises, setIsLoadingPromises] = useState<boolean>(false)
  const [cherryData, setCherryData] = useState<CherryTypeV2[] | null>(null)
  const [isLoadingCherry, setIsLoadingCherry] = useState<boolean>(false)
  const [claimsData, setClaimsData] = useState<ClaimTypeV2[] | null>(null)
  const [isLoadingClaims, setIsLoadingClaims] = useState<boolean>(false)
  const [modelSectionsData, setModelSectionsData] = useState<ModelSectionType[] | null>(null)
  const [isLoadingModelSections, setIsLoadingModelSections] = useState<boolean>(false)
  const [score, setScore] = useState<number>(0)
  const [scoreData, setScoreData] = useState<ScoreTypeV2 | null>(null)
  const [isLoadingScore, setIsLoadingScore] = useState<boolean>(false)
  const [vagueData, setVagueData] = useState<VagueType | null>(null)
  const [vagueDetailData, setVagueDetailData] = useState<VagueType[] | null>(null)
  const [isLoadingVague, setIsLoadingVague] = useState<boolean>(false)
  const [isBeforeLoading, setIsBeforeLoading] = useState<boolean>(false)

  // Add refs to track current request context and abort controllers per request type
  const currentRequestRefs = useRef<Map<string, string>>(new Map())
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map())

  // Initialize data fetchers
  const flagsDataFetcher = useRef(new FlagsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const promisesDataFetcher = useRef(new PromisesDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const cherryDataFetcher = useRef(new CherryDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const claimsDataFetcher = useRef(new ClaimsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const modelSectionsDataFetcher = useRef(new ModelSectionsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const scoreDataFetcher = useRef(new ScoreDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const vagueDataFetcher = useRef(new VagueDataFetcher(currentRequestRefs.current, abortControllersRef.current))

  function isLoading() {
    return isBeforeLoading || isLoadingFlags || isLoadingPromises || isLoadingCherry ||
      isLoadingClaims || isLoadingModelSections || isLoadingScore || isLoadingVague
  }

  function hash() {
    return entity + ':' + run + ':' + model + ':' + (includeDisclosures ? '1' : '0')
  }

  function toggleDisclosures(include: boolean) {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return
    }
    sessionStorage.setItem('include-disclosures', include ? 'true' : 'false')
    setUrlParams({ disclosures: include })
  }

  // Function to fetch and filter flags based on the current entity, run, and disclosure setting
  async function fetchAndFilterFlags() {
    await flagsDataFetcher.current.fetch(
      { entity, runObject, includeDisclosures },
      setFlagsData,
      setIsLoadingFlags,
    )
  }

  // Function to fetch and filter promises based on the current entity and run
  async function fetchAndFilterPromises() {
    await promisesDataFetcher.current.fetch(
      { entity, runObject },
      setPromisesData,
      setIsLoadingPromises,
    )
    setIsBeforeLoading(false)
  }

  // Function to fetch cherry data
  async function fetchCherryData() {
    await cherryDataFetcher.current.fetch(
      { entity, runObject },
      setCherryData,
      setIsLoadingCherry,
    )
    setIsBeforeLoading(false)
  }

  // Function to fetch claims data
  async function fetchClaimsData() {
    await claimsDataFetcher.current.fetch(
      { entity, runObject },
      setClaimsData,
      setIsLoadingClaims,
    )
    setIsBeforeLoading(false)
  }

  // Function to fetch model sections data
  async function fetchModelSectionsData() {
    await modelSectionsDataFetcher.current.fetch(
      { entity, runObject, model },
      setModelSectionsData,
      setIsLoadingModelSections,
    )
    setIsBeforeLoading(false)
  }

  // Function to fetch score data
  async function fetchScoreData() {
    await scoreDataFetcher.current.fetch(
      { entity, runObject },
      (data) => {
        if (data) {
          setScore(data.score)
          setScoreData(data.scoreData)
        } else {
          setScore(0)
          setScoreData(null)
        }
      },
      setIsLoadingScore,
    )
    setIsBeforeLoading(false)
  }

  // Function to fetch vague data
  async function fetchVagueData() {
    await vagueDataFetcher.current.fetch(
      { entity, runObject },
      (data) => {
        if (data) {
          setVagueData(data.vagueData)
          setVagueDetailData(data.vagueDetailData)
        } else {
          setVagueData(null)
          setVagueDetailData(null)
        }
      },
      setIsLoadingVague,
    )
    setIsBeforeLoading(false)

  }

  function changeParams(newParams: { key: string, value: string | null }[]) {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return
    }
    setIsBeforeLoading(true)
    // Abort all ongoing data retrievals when parameters change
    console.log('Aborting all data retrievals due to parameter change')
    const requestTypes = ['flags', 'promises', 'cherry', 'claims', 'modelSections', 'score', 'vague']
    requestTypes.forEach(requestType => {
      const controller = abortControllersRef.current.get(requestType)
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete(requestType)
        console.log(`Aborted ${requestType} request`)
      }
    })

    // Build update object for nuqs
    const updates: Partial<typeof urlParams> = {}
    let resetRun = false
    
    newParams.forEach(({ key, value }) => {
      if (value !== undefined && value !== null) {
        sessionStorage.setItem('url-param-' + key, value)
        
        // Handle specific parameter updates
        if (key === 'entity') {
          updates.entity = value
          // Reset run to latest when entity changes
          if (value !== entity) {
            resetRun = true
          }
        } else if (key === 'run') {
          updates.run = value
        } else if (key === 'model') {
          updates.model = value
        } else if (key === 'disclosures') {
          updates.disclosures = value === 'true'
        }
      } else {
        // Set to null to remove from URL
        if (key === 'entity') updates.entity = null
        else if (key === 'run') updates.run = 'latest'
        else if (key === 'model') updates.model = DEFAULT_MODEL
        else if (key === 'disclosures') updates.disclosures = true
      }
    })
    
    if (resetRun) {
      updates.run = 'latest'
      sessionStorage.setItem('url-param-run', 'latest')
    }
    
    // Update URL state with nuqs
    setUrlParams(updates)
  }

  // Load entity data when entity changes
  useEffect(() => {
    if (!entity) {
      setEntityData(null)
      return
    }

    let abort = false
    runAsync(async () => {
      try {
        const { data: entityV2Data, error: entityV2Error } = await supabase
          .from('xfer_entities_v2')
          .select('*')
          .eq('entity_xid', entity)
          .single()

        if (abort) return

        if (entityV2Error) {
          console.error('Error loading entity data:', entityV2Error)
          setEntityData(null)
          return
        }

        if (entityV2Data) {
          // Convert the new format to the old format for compatibility
          const entityV1 = convertEntityV2ToEntityV1(entityV2Data)
          setEntityData(entityV1)
        } else {
          setEntityData(null)
        }
      } catch (error) {
        if (!abort) {
          console.error('Error in entity data loading:', error)
          setEntityData(null)
        }
      }
    })

    return () => {
      abort = true
    }
  }, [entity])

  useEffect(() => {
    let abort = false
    runAsync(async () => {
        // Try to get runs from xfer_runs_v2 first
        const { data: specificRunsV2, error: specificRunsV2Error } = await supabase
          .from('xfer_runs_v2')
          .select('*')
          .eq('scope', 'entity')
          .eq('target', entity!)

        const { data: generalRunsV2, error: generalRunsV2Error } = await supabase
          .from('xfer_runs_v2')
          .select('*')
          .eq('scope', 'all')

        // Convert V2 runs to V1 format if available
        let specificRuns: RunType[] = []
        let generalRuns: RunType[] = []

        if (specificRunsV2 && specificRunsV2.length > 0) {
          specificRuns = specificRunsV2.map(runV2 => convertRunV2ToRunV1(runV2))
        }
        if (generalRunsV2 && generalRunsV2.length > 0) {
          generalRuns = generalRunsV2.map(runV2 => convertRunV2ToRunV1(runV2))
        }

        if (abort) return

        if (specificRuns.length > 0 && generalRuns.length > 0) {
          setRuns([...specificRuns, ...generalRuns])
        } else if (specificRuns.length > 0) {
          setRuns(specificRuns)
        } else if (generalRuns.length > 0) {
          setRuns(generalRuns)
        }
        console.log('Set runs', specificRuns, 'for entity', entity)
      },
    )

    return () => {
      abort = true
    }

  }, [entity])

  // Set run object when run or runs change
  useEffect(() => {
    if (!runs.length) return

    runAsync(async () => {
      let defaultRun = null

      if (run !== 'latest') {
        // Try to get run from xfer_runs_v2 first
        const {
          data: defaultRunV2Value,
          error: runV2Error,
        } = await supabase.from('xfer_runs_v2').select('*').eq('id', +run!).single()

        if (!runV2Error && defaultRunV2Value) {
          // Convert the new format to the old format for compatibility
          defaultRun = convertRunV2ToRunV1(defaultRunV2Value)
        }
      } else {
        for (let r of runs) {
          if (r.run_type == 'full' || r.run_type == 'inc') {
            if (defaultRun == null || r.id! > (defaultRun.id || 0)) {
              defaultRun = r
            }
          }
        }
      }
      console.log('Set run object', defaultRun)
      // Make sure defaultRun is a proper RunType before setting it
      if (defaultRun) {
        const typedRun: RunType = {
          id: typeof defaultRun.id === 'number' ? defaultRun.id : 0,
          run_type: String(defaultRun.run_type || ''),
          scope: String(defaultRun.scope || ''),
          target: typeof defaultRun.target === 'string' ? defaultRun.target : null,
          model: defaultRun.model || {},
          completed_at: typeof defaultRun.completed_at === 'string' ? defaultRun.completed_at : null,
        }
        setRunObject(typedRun)
      } else {
        setRunObject(null)
      }
    })
  }, [run, runs])

  // Fetch flags when entity, run, or includeDisclosures changes
  useEffect(() => {
    if (entity && runObject) {
      fetchAndFilterFlags()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('flags')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('flags')
      }
    }
  }, [entity, runObject, includeDisclosures])

  // Fetch promises when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchAndFilterPromises()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('promises')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('promises')
      }
    }
  }, [entity, runObject])

  // Fetch cherry data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchCherryData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('cherry')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('cherry')
      }
    }
  }, [entity, runObject])

  // Fetch claims data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchClaimsData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('claims')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('claims')
      }
    }
  }, [entity, runObject])

  // Fetch model sections data when model changes
  useEffect(() => {
    if (model) {
      fetchModelSectionsData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('modelSections')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('modelSections')
      }
    }
  }, [model])

  // Fetch score data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchScoreData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('score')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('score')
      }
    }
  }, [entity, runObject])

  // Fetch vague data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchVagueData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('vague')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('vague')
      }
    }
  }, [entity, runObject])


  return (
    <EntityContext.Provider value={{
      entity,
      run,
      model,
      includeDisclosures,
      changeParams,
      toggleDisclosures,
      hash,
      queryString,
      entityData,
      runObject,
      flagsData,
      isLoadingFlags,
      promisesData,
      isLoadingPromises,
      cherryData,
      isLoadingCherry,
      claimsData,
      isLoadingClaims,
      modelSectionsData,
      isLoadingModelSections,
      score,
      scoreData,
      isLoadingScore,
      vagueData,
      vagueDetailData,
      isLoadingVague,
      isLoading,
    }}>
      {children}
    </EntityContext.Provider>
  )
}

export const useEntity = (): EntityContextType => {
  const context = useContext(EntityContext)
  if (context === undefined) {
    throw new Error('useEntity must be used within a EntityProvider')
  }
  return context
}
