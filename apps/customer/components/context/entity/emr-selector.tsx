import React, { useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/ui/select'
import { useEntity } from '@/components/context/entity/entity-context'
import { User } from '@supabase/supabase-js'
import { MyCompaniesType, ProfileType, RunType } from '@/types'
import { createClient } from '@/app/supabase/client'
import { runAsync } from '@utils/react-utils'
import { NavigationItem, PageHeader } from '@/components/page-header'
import { conciseDateTime } from '@utils/date-utils'
import { convertEntityV2ToEntityV1 } from '@/utils/entity-converter'
import { convertRunV2ToRunV1 } from '@/utils/run-utils'
import { Switch } from '@ui/components/ui/switch'
import { Label } from '@ui/components/ui/label'


export function EntityModelRunSelector({navPath}: { navPath: NavigationItem[] }) {
    const [runs, setRuns] = React.useState<RunType[]>([]);
    const [defaultRun, setDefaultRun] = React.useState<number | null>(null);
    const [user, setUser] = React.useState<User | null>(null);
    const [profile, setProfile] = React.useState<ProfileType | null>(null);
    const [entity, setEntity] = React.useState<any | null>(null);
    const [companies, setCompanies] = React.useState<MyCompaniesType[]>([]);
    const supabase = createClient();
    const entityData = useEntity();


  useEffect(() => {
    let abortController: AbortController | null = null;

    if (entityData.entity) {
      abortController = new AbortController();
      loadData(entityData.entity, abortController.signal);
    }

    // Cleanup function to cancel request when entity changes
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, [entityData])

  useEffect(() => {
    runAsync(async () => {
      console.log('Loading companies data...')
      await supabase.from('view_my_companies').select('*').then((response: any) => {
        console.log('Companies response:', response.data?.length, 'items')
        setCompanies(response.data || [])
      })

    })

  }, [])


  async function loadData(entityId: string, abortSignal?: AbortSignal) {
        if (typeof window === 'undefined') {
            return;
        }

        try {
            // Try to get entity from xfer_entities_v2 first
            let entityQuery = supabase
                .from('xfer_entities_v2')
                .select('*')
                .eq("entity_xid", entityId);

            if (abortSignal) {
                entityQuery = entityQuery.abortSignal(abortSignal);
            }

            const { data: entityV2Data, error: entityV2Error } = await entityQuery;

            // Check if request was cancelled
            if (abortSignal?.aborted) {
                console.log('Entity data request was cancelled');
                return;
            }

            if (entityV2Data === null)
                return;

            // Convert the new format to the old format for compatibility
            const entityV1 = convertEntityV2ToEntityV1(entityV2Data[0]);
            setEntity(entityV1);

            // Try to get runs from xfer_runs_v2 first
            let runsQuery = supabase
                .from('xfer_runs_v2')
                .select('*')
                .eq("scope", "entity")
                .eq("target", entityId);

            if (abortSignal) {
                runsQuery = runsQuery.abortSignal(abortSignal);
            }

            const { data: runsV2Data, error: runsV2Error } = await runsQuery;

            // Check if request was cancelled
            if (abortSignal?.aborted) {
                console.log('Runs data request was cancelled');
                return;
            }

        console.log("Runs V2", runsV2Data);

        let runsData: RunType[] = [];

        if (runsV2Data && runsV2Data.length > 0) {
            // Convert V2 runs to V1 format
            console.log("Runs V2", runsV2Data);
            runsData = runsV2Data.map((i) => convertRunV2ToRunV1(i));
            // Sort by completed_at
            console.log("Runs V1", runsData);
            runsData.sort((a, b) => {
                if (!a.completed_at) return 1;
                if (!b.completed_at) return -1;
                return new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime();
            });
        }

        console.log('Setting runs data:', runsData.length, 'items', runsData.map(r => `${r.id}:${r.run_type}`))
        setRuns(runsData);
        let runId = entityData.run;

        if (runId === "latest") {
            setDefaultRun(runsData.length > 0 ? runsData[0]?.id : null);
        } else {
            setDefaultRun(runId ? +runId : null);
        }

        if (user) {

            supabase
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single().then((response: any) => {
                let newProfile = response.data;
                if (newProfile && newProfile.avatar_url) {
                    supabase.storage.from('avatars').createSignedUrl(newProfile.avatar_url, 60).then(response => {
                        setProfile({...newProfile, avatar_url: response.data?.signedUrl} as any);
                    });
                }
            });
        }
        } catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                console.log('LoadData request was cancelled');
                return;
            }
            console.error('Error in loadData:', error);
        }
  }

  console.log('Rendering EMR Selector')
    return (<PageHeader navPath={navPath}>
            <div className="flex flex-row gap-4 text-foreground pointer-events-auto  text-sm" data-testid="emr-selector">
                {entityData.entity &&
                    <div className="block">
                        <Select defaultValue={entityData.entity || "JN6ZWej7Rw"}
                                onValueChange={(value) => entityData.changeParams([{
                                    key: 'entity',
                                    value: value
                                }, { key: 'run', value: 'latest' }])}
                                disabled={entityData.isLoading()}
                        >
                            <SelectTrigger className="w-[180px]" data-testid="company-selector">
                                <SelectValue placeholder="Select Company"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="company-dropdown">
                                {companies.length === 0 ? (
                                    <div className="px-2 py-1.5 text-sm text-muted-foreground" data-testid="no-companies-message">
                                        No companies available
                                    </div>
                                ) : (
                                    companies.map((company) => (
                                        <SelectItem key={company.entity_xid} value={company.entity_xid!} data-testid="company-option">
                                            <span data-testid="company-name">{company.name}</span>
                                        </SelectItem>
                                    ))
                                )}
                            </SelectContent>
                        </Select></div>}
                {entityData.run &&
                    <div className="hidden md:block text-xs">
                        <Select defaultValue={defaultRun ? "" + defaultRun : ""}
                                disabled={entityData.isLoading()}
                                onValueChange={(value) => entityData.changeParams([{key: 'run', value: value}])}>
                            <SelectTrigger className="w-40 lg:w-48 xl:w-[20rem]" data-testid="run-selector">
                                <SelectValue placeholder="Select Run"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="run-dropdown">
                                {
                                    runs.map((run) => (
                                        <SelectItem key={run.id} value={"" + run.id} data-testid="run-option">
                                            <span data-testid="run-date">{run.completed_at ? conciseDateTime(new Date(run.completed_at), Date.now(), navigator.language) : 'No date'}</span> : {run.start_year} - {run.end_year || 'now'} ( <span data-testid="run-type">{run.id} {run.run_type}</span>)
                                        </SelectItem>))
                                }
                            </SelectContent>
                        </Select></div>}

                {entityData.model &&
                    <div className="hidden sm:block">
                      <Select disabled={entityData.isLoading()} defaultValue={entityData.model || 'ekoIntelligence'}
                                onValueChange={(value) => entityData.changeParams([{key: 'model', value}])}>
                            <SelectTrigger className="w-[140px]" data-testid="model-selector">
                                <SelectValue placeholder="Select Model"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="model-dropdown">
                              <SelectItem value="eko" data-testid="model-option">ekoIntelligence</SelectItem>
                                <SelectItem value="sdg" data-testid="model-option">SDG</SelectItem>
                                <SelectItem value="doughnut" data-testid="model-option">Doughnut</SelectItem>
                                <SelectItem value="plant_based_treaty" data-testid="model-option">Plant Based Treaty</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                }

                {/* Disclosure toggle */}
                <div className="hidden sm:flex items-center space-x-2 ml-4">
                    <Switch
                        id="include-disclosures-nav"
                        data-testid="disclosure-toggle"
                        disabled={entityData.isLoading()}
                        checked={entityData.includeDisclosures}
                        onCheckedChange={entityData.toggleDisclosures}
                        className="data-[state=checked]:bg-brand"
                    />
                    <Label htmlFor="include-disclosures-nav" className="cursor-pointer text-slate-700 dark:text-slate-200 text-xs">
                        {entityData.includeDisclosures ? 'Include disclosures' : 'Exclude disclosures'}
                    </Label>
                </div>
            </div>
        </PageHeader>);

}
