import { Segment, SegmentData } from '@ui/components/graph/donut/types'
import { DynamicLucideIcon } from '@ui/components/icons/icons'
import React from 'react'
import { FlagTypeV2 } from '@/types'


export type Issue = {
    created_at: string;
    description: string;
    ethical_group: string;
    issue: string;
    section_map: any[];
    summary: string;
    synonyms: string[];
    type: string;
    title: string;
    impact: number;
    impact_weight: number;
}


// This function is deprecated as issues are no longer used
// Model sections are now directly attached to each flag
export function createIssueMap(issues: any[]): Map<string, Issue> {
    // Return an empty map as this function is no longer used
    return new Map<string, Issue>();
}


// This function is deprecated as issues are no longer used
// Model sections are now directly attached to each flag
function createSectionMap(modelSections: any[], issues: any[], model: string) {
    // Return an empty map as this function is no longer used
    return new Map<string, string>();
}

export type DonutSegmentData = {
    governanceSegmentsRed: SegmentData[];
    socialSegmentsRed: SegmentData[];
    ecoSegmentsGreen: SegmentData[];
    governanceSegmentsGreen: SegmentData[];
    ethicalModelSegments: Map<string, Segment>;
    socialSegmentsGreen: SegmentData[];
    ecoSegmentsRed: SegmentData[]
};
//   id: flag.id,
//                         flag_type: parsedFlag.model.flag_type,
//                         model_sections: parsedFlag.model.model_sections,

export function createSegments(model:string,
                               modelSections:any[],
        rows:FlagTypeV2[]): DonutSegmentData {
    let socialSegmentsRed: SegmentData[] = [];
    let socialSegmentsGreen: SegmentData[] = [];
    let governanceSegmentsRed: SegmentData[] = [];
    let ecoSegmentsRed: SegmentData[] = [];
    let ecoSegmentsGreen: SegmentData[] = [];
    let governanceSegmentsGreen: SegmentData[] = [];
    // console.log("MODEL SECTIONS", modelSections)
    for (const segmentmapElement of modelSections!) {
        // Handle both v1 and v2 formats
        const section = segmentmapElement.section!;
        // For v2 format, extract level from data JSON
        const level = segmentmapElement.level ||
                     (segmentmapElement.data && typeof segmentmapElement.data === 'object' ?
                      segmentmapElement.data.level : null);

        if (level === "social") {
            socialSegmentsRed.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
            socialSegmentsGreen.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
        }
        if (level === "ecological") {
            ecoSegmentsRed.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
            ecoSegmentsGreen.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
        }
        if (level === "governance") {
            governanceSegmentsRed.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
            governanceSegmentsGreen.push({
                name: section,
                value: 0,
                detail: "",
                count: 0
            });
        }
    }
    console.log("Model sections are now directly attached to each flag")
    const ethicalModelSegments = new Map<string, Segment>();
    for (const segmentmapElement of modelSections!) {
        const section = segmentmapElement.section!;

        // Extract properties from either direct properties or data JSON
        const title = segmentmapElement.title ||
                     (segmentmapElement.data && typeof segmentmapElement.data === 'object' ?
                      segmentmapElement.data.title : null) || section;

        const description = segmentmapElement.description ||
                           (segmentmapElement.data && typeof segmentmapElement.data === 'object' ?
                            segmentmapElement.data.description : null) || "";

        const level = segmentmapElement.level ||
                     (segmentmapElement.data && typeof segmentmapElement.data === 'object' ?
                      segmentmapElement.data.level : null) || "ecological";

        const icon = segmentmapElement.icon ||
                    (segmentmapElement.data && typeof segmentmapElement.data === 'object' ?
                     segmentmapElement.data.icon : null) || 'Skull';

        ethicalModelSegments.set(section, {
            id: section,
            title: title,
            description: description,
            level: level,
            icon: <DynamicLucideIcon iconName={icon as any || 'Skull'}/>
        });
    }


    let count = 0;
    // console.info("ROWS", rows)
    if (rows) {
        for (const modelSection of modelSections!) {
            const section = modelSection.section!;
            // Filter rows based on model sections directly attached to each flag
            const segmentRows = rows.filter((row) => {
                // Check if the flag has model_sections and if it maps the current model to this section
                const modelSections = row.model?.model_sections || {};
                return modelSections[model] === section;
            });
            console.log("MODEL SECTION", section, segmentRows)
            for (const row of segmentRows) {
                let segments = null;
                // Get the level from either direct property or data JSON
                const level = modelSection.level ||
                             (modelSection.data && typeof modelSection.data === 'object' ?
                              modelSection.data.level : null) || "ecological";

                if (row?.flag_type == "red") {
                    if (level === "ecological") {
                        segments = ecoSegmentsRed
                    } else if(level === "social") {
                        segments = socialSegmentsRed
                    } else {
                        segments = governanceSegmentsRed
                    }
                } else {
                    if (level === "ecological") {
                        segments = ecoSegmentsGreen
                    } else if(level === "social") {
                        segments = socialSegmentsGreen
                    } else {
                        segments = governanceSegmentsGreen
                    }
                }


                const segment = segments.find(segment => (segment.name === modelSection.section))!;
                if (segment) {
                    // segment.detail = `${row?.reason}`;
                    // Use a default impact weight of 1.0 since we no longer have issues
                    const impactWeight = 10.0;
                    let rating = 1.0 * (row.model.confidence!/100) * (row.model.impact! / 100) * (row.model.contribution! / 100) * (row.model.authentic! / 100) * impactWeight;
                    segment.value += rating;
                    segment.count!++;
                    console.log("SEGMENT", segment, rating, row.model.confidence, row.model.impact)
                } else {
                    console.error("Could not find segment", modelSection.section, segments);
                }
            }

        }

    }

    // Process segment values to ensure they're not zero
    for (const segmentCollection of [ecoSegmentsGreen, ecoSegmentsRed, socialSegmentsGreen, socialSegmentsRed, governanceSegmentsGreen, governanceSegmentsRed]) {
        for (const segment of segmentCollection) {
            // If count is zero, set a minimum value to ensure the segment is visible
            if (segment.count === 0) {
                segment.value = 0.1; // Minimum value to ensure visibility
            } else {
                // Apply logarithmic scaling but ensure value is never zero
                segment.value = Math.max(0.1, Math.log10(segment.value / segment.count! + 1));
            }
            console.log(`Segment ${segment.name} has value ${segment.value} and count ${segment.count}`);
        }
    }
    console.log("GOVERNANCE SEGMENTS", governanceSegmentsGreen, governanceSegmentsRed)
    // We still need to return issueMap and sectionMap for backward compatibility
    // but they will be empty maps
    return {socialSegmentsRed, socialSegmentsGreen, ecoSegmentsRed, ecoSegmentsGreen, governanceSegmentsGreen, governanceSegmentsRed, ethicalModelSegments};
}
