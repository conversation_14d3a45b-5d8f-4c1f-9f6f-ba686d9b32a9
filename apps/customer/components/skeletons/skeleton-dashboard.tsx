import {Skeleton} from "@ui/components/ui/skeleton";
import React from "react";
import Image from "next/image";
import TimelineSkeleton from "@/components/skeletons/timeline-skeleton";

export function SkeletonDashboard() {
    return <div
        className="p-4 md:p-6 space-y-6 dashboard-container flex-row flex-grow">
        <div className="min-w-full"/>
        <div className="hidden lg:flex flex-cols mt-8 w-[800px] gap-4">
            <Skeleton className="h-4 w-36"/>
            <Skeleton className="h-4 w-48"/>
            <Skeleton className="h-4 w-40"/>
            <Skeleton className="h-4 w-36"/>
            <Skeleton className="h-4 w-44"/>
            <Skeleton className="h-4 w-40"/>
            <Skeleton className="h-4 w-36"/>
            <Skeleton className="h-4 w-24"/>
        </div>
        <Skeleton className="h-6 w-[25dvw] mb-4"/>
        <div className="flex flex-col mt-8 w-[800px] gap-4">
            <Skeleton className="h-4 w-[calc(100%_-_10px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_52px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_18px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_28px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_14px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_32px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[0px]"/>
            <Skeleton className="h-4 w-[calc(100%_-_78px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_30px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_35px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_56px)]"/>

        </div>
        <div className="gap-4 flex flex-row w-[900px] mt-4 mb-8">
            <Skeleton className="h-4 w-24"/>
            <Skeleton className="h-4 w-36"/>
            <Skeleton className="h-4 w-56"/>
            <Skeleton className="h-4 w-48"/>
            <Skeleton className="h-4 w-72"/>
        </div>
        <div className=" mt-8 grid  grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-4">
            <Skeleton className="h-72 w-full col-span-2"/>
            <Skeleton className="h-72 w-full"/>
            <Skeleton className="h-72 w-full"/>
        </div>
        <div className=" mt-8 grid  grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6">
            <Skeleton className="h-36 w-full"/>
            <Skeleton className="h-36 w-full"/>
            <Skeleton className="h-36 w-full"/>
            <Skeleton className="h-36 w-full"/>
            <Skeleton className="h-36 w-full"/>
            <Skeleton className="h-36 w-full"/>
        </div>

        <div className="grid gap-4   grid-cols-1  lg:grid-cols-1 xl:grid-cols-2">

            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


        </div>

        <Skeleton className="h-8 w-[25dvw] mb-4 mt-12"/>
        <div className="space-y-2 flex-rows">
            <Skeleton className="h-4 w-[calc(100%_-_10px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_52px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_18px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_28px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_14px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_32px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[0px]"/>
            <Skeleton className="h-4 w-[calc(100%_-_78px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_30px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_35px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_12x)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_24px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_64px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_56px)]"/>

        </div>

        <TimelineSkeleton/>


    </div>;
}
