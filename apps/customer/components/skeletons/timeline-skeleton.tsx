import {Skeleton} from "@ui/components/ui/skeleton";
import {motion} from "framer-motion";

export default function TimelineSkeleton() {
    const events = Array(8).fill(0); // Dummy array for 8 skeleton events

    return (
        <div className="w-full max-w-3xl mx-auto p-2 pb-6 hidden lg:block  animate-pulse">
            <div className="relative">
                {/* Vertical line */}
                <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5">
                    {/* Top fade */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 top-0 h-20 w-full bg-gradient-to-b from-transparent to-bg-muted"></div>
                    {/* Solid line */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 top-20 bottom-20 w-full bg-muted "></div>
                    {/* Bottom fade */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 bottom-0 h-20 w-full bg-gradient-to-t to-bg-muted  from-transparent"></div>
                </div>

                <div className="space-y-6">
                    {events.map((_, index) => (
                        <motion.div
                            initial={{ scaleY: 0, opacity: 0 }} // Starts folded and hidden
                            whileInView={{ scaleY: 1, opacity: 1 }} // Animate when the item comes into view
                            viewport={{ once: true, amount: 0.2 }} // Triggers once, 20% in view
                            transition={{
                                duration: 0.5,
                                ease: "easeOut",
                                delay: index * 0.25,
                            }}
                            key={index}
                            className={`flex items-center ${
                                index % 2 === 0 ? "flex-row" : "flex-row-reverse"
                            }`}
                        >
                            {/* Content Skeleton */}
                            <div className="w-5/12">
                                <div className="transform transition-all hover:scale-105 -mb-8 relative">
                                    <div className="p-2 bg-background shadow rounded-md">
                                        <Skeleton className="h-4 w-20 mb-2" />
                                        <Skeleton className="h-3 w-40" />
                                        <Skeleton className="h-3 w-24 mt-2" />
                                        <div className="absolute top-2 right-4">
                                            <Skeleton className="h-4 w-12" />
                                        </div>
                                        <Skeleton className="h-3 w-16 mt-2" />
                                    </div>
                                </div>
                            </div>

                            {/* Center circle Skeleton */}
                            <div className="w-2/12 flex justify-center">
                                <motion.div
                                    initial={{ scale: 0, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1 }}
                                    exit={{ scale: 0, opacity: 0 }}
                                    transition={{
                                        duration: 0.5,
                                        ease: "easeOut",
                                        delay: index * 0.5,
                                    }}
                                    className="w-3 h-3 bg-muted rounded-full"
                                />
                            </div>

                            {/* Empty space for alignment */}
                            <div className="w-5/12" />
                        </motion.div>
                    ))}
                </div>
            </div>
        </div>
    );
}
