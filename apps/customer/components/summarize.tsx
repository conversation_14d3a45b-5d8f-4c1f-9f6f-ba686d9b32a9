"use client";

import React, { useEffect, useState } from 'react'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Skeleton } from '@ui/components/ui/skeleton'
import { cn } from '@utils/lib/utils'
import { CitationType } from '@/components/citation'
import { useLocalStorage } from 'usehooks-ts'
import { RefreshCwIcon } from 'lucide-react'
import { useCompletion } from 'ai/react'


export function Summarize({
    hashId,
    preamble,
    obj,
    className,
    citations = [],
    citationPrefix = "",
    keepCitations = false,
    inlineCitations = false
}: {
    hashId: string,
    preamble: string,
    obj: any,
    className?: string,
    citations?: CitationType[],
    citationPrefix?: string,
    keepCitations?: boolean
    inlineCitations?: boolean
}) {
    const [key, setKey] = useLocalStorage('v2-summary-key-' + hashId, "");
    const [text, setText, removeText] = useLocalStorage('v2-summary-text-' + hashId, "");
    const [streamingMode, setStreamingMode] = useState(false);

    // Use the AI SDK's useCompletion hook for streaming
    const { complete, completion, isLoading } = useCompletion({
        api: '/api/summarize',
        onResponse: () => {
            setStreamingMode(true);
            console.log("Streaming completion")
        },
        onFinish: (prompt, completionStr) => {
            setText(completionStr);
            setStreamingMode(false);
            console.log("Finished completion: "+completionStr);
        },
        onError: (err) => {
            console.error("Completion error:", err);
            // Consider updating state here, e.g., set an error message, set streamingMode to false
            setStreamingMode(false);
        }
    });

    useEffect(() => {
        let cancelled = false;
        if (text) {
        } else {
            if(isLoading) {
              console.error("Loading!!!!!!")
            } else {
              // Use streaming mode with AI SDK
              console.log("Starting completion")
              complete(JSON.stringify({preamble, keepCitations, obj, key}));
            }
        }
        return () => {
            cancelled = true;
        }
    }, [hashId, key, obj]);

    function reload() {
        const newKey = Math.random() + ":" + Date.now();
        setKey(newKey);
        removeText();
        setStreamingMode(true);
        complete(JSON.stringify({preamble, keepCitations, obj, key: newKey}));
    }

    // Show the cached text if available, otherwise show the streaming completion
    const displayText = text || completion;

    return (displayText && !isLoading) ? (
        <div className={cn(className, "mt-5 relative group")}>
            <EkoMarkdown
                className={className}
                citationPrefix={citationPrefix}
                citations={citations}
                inlineCitations={inlineCitations}
                admin={false}
            >
                {displayText || "Loading..."}
            </EkoMarkdown>
            <div className="absolute -top-3 md:-top-5 right-4">
                <RefreshCwIcon
                    className={cn(
                        "h-4 w-4 opacity-10 transition-opacity text-muted-foreground hover:text-foreground group-hover:opacity-100 group-hover:zoom-in-50 cursor-pointer",
                        (isLoading) && "animate-spin"
                    )}
                    onClick={reload}
                />
            </div>
        </div>
    ) : (
        <div className={cn(className, "space-y-2 flex-col")}>
            <Skeleton className="h-4 w-[calc(100%_-_10px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_52px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_18px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_28px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_14px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_32px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[0px]"/>
            <Skeleton className="h-4 w-[calc(100%_-_78px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_30px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_35px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_12x)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_24px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_64px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_40px)]"/>
            <Skeleton className="h-4 w-[calc(100%_-_56px)]"/>
        </div>
    )
}
