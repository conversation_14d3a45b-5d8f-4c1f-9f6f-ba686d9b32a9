import {createClient} from "@/app/supabase/client";
import {AccountMessageEnumType, AccountMessageType} from "@/types";
import {CheckCircle, CreditCard, Info, MessageCircleWarningIcon, Trash2} from "lucide-react";
import {conciseDateTime} from "@utils/date-utils";
import {Button} from "@ui/components/ui/button";


export function AccountMessage({notification, onRead, onDelete}: {
    notification: AccountMessageType,
    onRead: (id: number) => void,
    onDelete: (id: number) => void
}) {

    const supabase = createClient();

    async function markAsRead(id: number) {
        await supabase.from('acc_messages').update({read_at: new Date().toISOString()}).eq('id', id)
        onRead(id)
    }

    async function deleteNotification(id: number) {
        await supabase.from('acc_messages').update({deleted_at: new Date().toISOString()}).eq('id', id)
        onDelete(id)
    }

    /*
      | "info"
        | "success"
        | "billing"
        | "quota"
        | "feature"
        | "error"
     */

    const getIcon = (type: AccountMessageEnumType) => {
        switch (type) {
            case 'info':
                return <Info className="h-8 w-8 text-blue-500 inline-block mr-3"/>
            case 'success':
                return <CheckCircle className="h-8 w-8 text-blue-500 inline-block mr-3"/>
            case 'billing':
                return <CreditCard className="h-8 w-8 text-blue-500 inline-block mr-3"/>
            case 'quota':
                return <CreditCard className="h-8 w-8 text-blue-500 inline-block mr-3"/>
            case 'feature':
                return <CreditCard className="h-8 w-8 text-blue-500 inline-block mr-3"/>
            case 'error':
                return <MessageCircleWarningIcon className="h-8 w-8 text-blue-500 inline-block mr-3"/>
        }
    }
    return <div
                className={`flex flex-col relative border-2 m-4 border-muted-foreground w-[300px]  lg:w-[600px]  items-start gap-4 p-4 rounded-lg ${notification.read_at ? 'bg-secondary' : 'bg-secondary/50'}`}>

        <div className="flex-grow">
            <p className={`${notification.read_at ? 'text-muted-foreground' : 'font-medium'} text-xl mb-4`}>{getIcon(notification.type!)}
                <span className="mt-2">{notification.title}</span></p>
            <p className={`${notification.read_at ? 'text-muted-foreground' : 'font-light'} mb-1`}>{notification.message}</p>
            <p className="text-sm text-muted-foreground">{conciseDateTime(new Date(notification.created_at), Date.now(), navigator.language)}</p>
        </div>
        <div className="flex items-center gap-2 w-full">
            {!notification.read_at && (
                <Button variant="outline" size="sm" className="absolute bottom-2 right-2"
                        onClick={() => markAsRead(notification.id)}>
                    Mark as read
                </Button>
            )}
            <Button variant="ghost" size="icon" className="absolute top-2 right-2"
                    onClick={() => deleteNotification(notification.id)}>
                <Trash2 className="h-4 w-4"/>
            </Button>
        </div>
    </div>;
}
