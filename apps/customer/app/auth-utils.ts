import {createClient} from "./supabase/server";
import {redirect} from "next/navigation";

export async function checkAuth(redirectUrl: string): Promise<boolean> {
    const supabase = await createClient();
    const {
        data: {user},
    } = await supabase.auth.getUser();

    if (!user) {
        redirect("/login?next=" + encodeURIComponent(redirectUrl));
    } else {
        return false;
    }
}

export async function checkAdmin(): Promise<boolean> {
    const supabase = await createClient()
    const user = (await supabase.auth.getUser()).data
    return !!user.user?.email?.endsWith("@ekointelligence.com");
}
