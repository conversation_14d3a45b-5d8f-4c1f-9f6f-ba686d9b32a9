"use client";
import {motion, useAnimation} from "framer-motion";
import {useEffect} from "react";

export function AnimatedSymbol(props: { size: number, className?: string, color: string }) {

    const controls = useAnimation();

    useEffect(() => {
        const sequence = async () => {
            controls.start({pathLength: 1, transition: {duration: 1}});
            controls.start({stroke: props.color, strokeOpacity: 1, transition: {duration: 0.3}});
            controls.start({fill: props.color, fillOpacity: 1, transition: {duration: 1, delay: 1.1}});
            controls.start({strokeWidth: props.size / 100, transition: {duration: 10, delay: 1.6}});
        };

        sequence();
    }, [controls]);

    return <motion.svg version="1.1" className={props.className} xmlns="http://www.w3.org/2000/svg" x="0"
                       y="0" width={props.size} height={props.size} viewBox="-10, 0, 430, 209.286">
        <motion.path
            initial={{
                pathLength: 0,
                stroke: props.color,
                strokeOpacity: 0,
                fillOpacity: 0,
                strokeWidth: props.size / 50
            }}
            animate={controls}
            d="M119.543,178.696 C134.859,171.483 148.34,162.2 159.987,149.609 C171.739,136.904 183.073,123.8 192.465,108.88 C204.417,89.892 217.367,71.857 232.105,55.46 C250.535,34.957 271.53,18.814 297.487,12.111 C336.513,2.033 375.533,18.435 394.045,61.528 C399.24,73.62 402.807,86.697 401.935,100.122 C400.733,118.632 396.835,136.263 386.364,151.587 C377.241,164.939 366.34,175.484 352.141,181.155 C344.368,184.26 336.498,187.859 327.813,187.378 C322.749,187.098 318.449,189.309 314.175,192.144 C303.091,199.496 292.587,207.963 278.653,208.706 C261.628,209.614 247.657,202.801 236.557,189.114 C221.308,170.311 213.304,147.932 211.845,122.845 C211.408,115.338 212.895,113.896 219.617,115.59 C229.66,118.121 239.529,118.157 249.776,116.538 C263.233,114.412 276.897,112.945 290.376,118.755 C306.455,125.686 314.604,139.096 317.846,156.98 C318.503,160.603 318.04,164.12 314.052,164.401 C310.012,164.686 305.376,164.932 303.993,158.917 C302.684,153.224 301.07,147.773 297.829,142.947 C290.77,132.437 280.284,130.683 269.99,130.603 C255.994,130.494 242.148,134.15 227.461,132.286 C231.559,152.004 237.835,169.441 250.889,182.863 C262.467,194.768 284.966,197.123 295.796,185.29 C285.794,181.708 276.031,178.192 267.095,172.39 C262.314,169.285 257.983,165.647 254.397,160.99 C253.17,159.396 252.084,157.355 253.202,155.267 C254.46,152.92 256.674,153.915 258.309,154.544 C267.214,157.966 275.985,161.84 284.928,165.124 C306.239,172.952 327.982,175.555 348.763,165.56 C371.054,154.84 383.52,135.187 386.496,107.862 C389.043,84.465 382.134,64.649 368.134,47.951 C354.634,31.849 337.137,24.488 317.291,25.247 C295.814,26.069 277.128,34.628 259.859,48.491 C240.637,63.923 226.308,84.009 212.374,104.734 C201.124,121.466 189.434,137.82 176.417,152.943 C162.67,168.916 147.032,182.351 128.802,191.257 C110.515,200.191 90.984,204.353 70.786,200.507 C46.361,195.855 27.417,181.071 14.085,158.198 C3.267,139.638 -0.577,118.386 2.554,97.152 C7.043,66.703 21.93,43.634 47.973,30.565 C57.1,25.984 66.539,22.722 76.666,23.15 C85.222,23.511 91.735,18.71 98.206,13.966 C110.089,5.253 122.726,-0.265 137.337,2.015 C154.688,4.723 167.496,15.68 177.209,31.175 C184.525,42.848 189.344,55.758 192.305,69.738 C193.678,76.219 195.134,82.552 195.343,89.159 C195.539,95.376 193.733,97.347 188.294,95.898 C174.053,92.104 160.036,93.868 145.811,95.68 C128.904,97.834 112.864,94.913 99.953,80.435 C93.402,73.088 90.812,63.83 88.467,54.405 C87.646,51.101 87.565,47.404 91.263,46.044 C94.663,44.794 97.96,45.738 100.596,48.63 C102.267,50.464 103.065,52.899 103.369,55.383 C105.063,69.227 115.641,77.237 127.122,79.357 C137.06,81.192 146.951,80.135 156.766,78.111 C162.319,76.965 167.903,76.414 173.533,78.096 C175.975,78.825 178.54,78.705 177.896,74.858 C175.34,59.582 169.721,45.648 160.746,33.645 C154.328,25.06 146.073,18.897 135.847,17.629 C126.608,16.483 117.659,17.802 109.297,24.997 C116.01,27.511 122.004,29.522 127.841,31.999 C136.191,35.543 143.828,40.486 150.301,47.484 C151.534,48.816 152.734,50.118 153.354,51.934 C154.568,55.491 153.642,57.47 150.092,56.461 C145.909,55.273 141.704,53.936 137.738,52.057 C120.621,43.95 103.043,37.755 84.385,37.561 C72.904,37.442 61.738,40.209 51.471,46.169 C37.008,54.565 26.706,67.23 20.957,84.094 C13.264,106.661 14.892,128.431 26.699,149.457 C35.551,165.221 47.748,176.479 63.362,182.075 C80.711,188.293 98.259,186.06 115.388,180.221 C116.638,179.795 117.873,179.314 119.543,178.696 z"
            fillOpacity={0}
            strokeWidth={props.size / 50}

        />
    </motion.svg>;
}
