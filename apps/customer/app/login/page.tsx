import Link from "next/link";
import {createClient} from "@/app/supabase/server";
import {redirect} from "next/navigation";
import {Label} from "@ui/components/ui/label";
import {Input} from "@ui/components/ui/input";
import {Checkbox} from "@ui/components/ui/checkbox";
import {SubmitButton} from "@/app/login/submit-button";
import {AuroraBackground} from "@ui/components/ui/aurora-background";
import {EkoLogoText} from "@utils/images";
import {AnimatedSymbol} from "@/app/login/animated-symbol";

export default async function Login(
    props: {
        searchParams: Promise<{ message: string, next?: string }>;
    }
) {
    const searchParams = await props.searchParams;

    const signIn = async (formData: FormData) => {
        "use server";

        const supabase = await createClient();
        const email = formData.get("email") as string;
        const password = formData.get("password") as string;
        ;

        const {data: user, error} = await supabase.auth.signInWithPassword({
            email,
            password,
        });

        console.log("Signed In", user)

        if (error) {
            console.error(error);
            return redirect(`/login?message=Could+not+authenticate+user&next=${searchParams?.next || "/"}`);
        }

        return redirect((searchParams?.next || "/"));
    };

    const supabase = await createClient();
    const {data: user, error} = await supabase.auth.getUser();

    if (!error && user) {
        return redirect(searchParams?.next || "/");
    }


    return (
        <div className="flex min-h-[100dvh] bg-background dark:bg-foreground dark:text-background text-background">
            <AuroraBackground
                className="flex flex-1 flex-shrink items-center justify-center bg-muted bg-zinc-200 min-w-[100dvw] lg:min-w-[350px] p-6 sm:p-10 lg:p-12 xl:p-16 bg-brand dark:bg-brand-dark text-zinc-50 dark:text-zinc-50">

                <AnimatedSymbol size={280} className={"mx-auto md:hidden"} color={"#ffffff"}/>
                <div className="mx-auto max-w-md space-y-8 pointer-events-auto">
                    <div>

                        {/*<span>{error?.message}</span>*/}
                        <h2 className="mt-6 text-3xl font-bold tracking-tight ">Sign in to
                            your
                            account</h2>
                        <p className="mt-2 text-sm">
                            Or{" "}
                            <Link href="/signup" className="font-medium  hover:text-primary/90"
                                  prefetch={false} data-testid="sign-up-link">
                                register for a new account
                            </Link>
                        </p>


                    </div>
                    <form className="space-y-6" data-testid="login-form">
                        <div>
                            <Label htmlFor="email" className="block text-sm font-medium">
                                Email
                            </Label>
                            <div className="mt-1">
                                <Input
                                    name="email"
                                    id="email"
                                    type="text"
                                    autoComplete="email"
                                    required
                                    data-testid="email-input"
                                    className="block w-full appearance-none rounded-md border border-input text-foreground bg-background px-3 py-2 placeholder-muted-foreground dark:placeholder-gray-200 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="password" className="block text-sm font-medium">
                                Password
                            </Label>
                            <div className="mt-1">
                                <Input
                                    id="password"
                                    name="password"
                                    type="password"
                                    autoComplete="current-password"
                                    required
                                    data-testid="password-input"
                                    className="block w-full appearance-none rounded-md border border-input text-foreground bg-background dark:bg-background px-3 py-2 placeholder-muted-foreground dark:placeholder-gray-200 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <Checkbox id="remember-me" name="remember-me" className="h-4 w-4 rounded"/>
                                <Label htmlFor="remember-me" className="ml-2 block text-sm opacity-80">
                                    Remember me
                                </Label>
                            </div>
                            <div className="text-sm">
                                <Link href="/resetpassword" className="font-medium opacity-80"
                                      prefetch={false} data-testid="forgot-password-link">
                                    Forgot your password?
                                </Link>
                            </div>
                        </div>
                        <div>
                            <SubmitButton
                                formAction={signIn}
                                className="rounded-md text-foreground mb-2"
                                pendingText="Signing In..."
                                data-testid="login-button"
                            >Sign In
                            </SubmitButton>
                        </div>
                        {searchParams?.message && (
                            <p className="mt-4 p-4 bg-foreground/10 text-foreground text-center" data-testid="login-error">
                                {searchParams.message}
                            </p>
                        )}
                    </form>
                </div>

            </AuroraBackground>
            <div
                className="hidden md:flex flex-grow bg-background text-foreground dark:text-foreground p-6 sm:p-10 lg:p-12 xl:p-16 flex-col items-start">
                <div className="mx-auto h-full max-w-md space-y-8 flex-col justify-center">
                    <Link href="/">
                        <AnimatedSymbol size={300} className={"mx-auto hidden md:block"} color="hsl(145, 27%, 45%)"/>
                        <EkoLogoText height={70} className="mx-auto mb-8 dark:hidden" ekoColor="#1f1f1f"
                                     intelligenceColor="#818181"/>
                        <EkoLogoText height={70} className="mx-auto mb-8 hidden dark:block" ekoColor="#f0f0f0"
                                     intelligenceColor="#a0a0a0"/>

                        <h2 className="mt-6 text-3xl font-bold tracking-tight ">Welcome</h2>
                        <p className="mt-2 text-sm ">
                            ekoIntelligence provides a comprehensive suite of tools to help you assess the
                            sustainability and
                            fairness of companies and funds.
                        </p>
                    </Link>
                    <div>
                        <h3 className="mt-6 text-xl font-bold tracking-tight text-foreground">Get Started</h3>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Sign in to your account or register a new one to start using our platform and unlock a world
                            of
                            possibilities.
                        </p>
                        <div className="mt-4 flex gap-2">
                            {/*<Button*/}
                            {/*    variant="secondary"*/}
                            {/*    className="flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"*/}
                            {/*>*/}
                            {/*    Sign in with Google*/}
                            {/*</Button>*/}
                            {/*<Link*/}
                            {/*    href="#"*/}
                            {/*    className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm transition-colors hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"*/}
                            {/*    prefetch={false}*/}
                            {/*>*/}
                            {/*    Register*/}
                            {/*</Link>*/}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
