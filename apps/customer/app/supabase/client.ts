import {createBrowserClient} from "@supabase/ssr";
import {Database} from "../../database.types";
import {SupabaseClient} from "@supabase/supabase-js";
import {GenericSchema} from "@supabase/supabase-js/dist/main/lib/types";

export const createClient: () => SupabaseClient<Database, "public" extends keyof Database ? "public" : (string & keyof Database), Database["public"] extends GenericSchema ? Database["public"] : any> = () =>
    createBrowserClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    );
