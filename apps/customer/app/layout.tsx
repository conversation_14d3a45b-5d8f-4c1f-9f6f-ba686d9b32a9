import {GeistSans} from "geist/font/sans";
import "./globals.css";
import {Inter} from 'next/font/google'
import {AuthProvider} from "@/components/context/auth/auth-context";
import {NavigationProvider} from "@/components/context/nav/nav-context";
import React from "react";
import {MobileBottomTabBar} from "@/components/mobile-bottom-tab-bar";
import { NuqsAdapter } from 'nuqs/adapters/next/app'

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
})
const defaultUrl = process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : "http://localhost:3000";

export const metadata = {
    metadataBase: new URL(defaultUrl),
    title: "ekoIntelligence - shining a light on investments",
    description: "ekoIntelligence provides actionable analytics of companie in a more nuanced and transparent way to help you assess their sustainability and fairness.",
};

export default async function RootLayout({
                                             children,
                                         }: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" className={GeistSans.className}>
        <body>
        <NuqsAdapter>
            <NavigationProvider>
                    {children}
                <MobileBottomTabBar />
            </NavigationProvider>
        </NuqsAdapter>
        </body>
        </html>
    );
}
