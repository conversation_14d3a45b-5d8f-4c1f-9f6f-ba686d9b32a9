import "./globals.css";
import {createClient} from "@/app/supabase/server";
import {redirect} from "next/navigation";

export default async function Index() {
    const supabase =  await createClient();
    const {
        data: {user},
    } = await supabase.auth.getUser();

    if (!user) {
        redirect("/login?next="+encodeURIComponent("/customer"));
    } else {
        redirect("/customer");
    }

    return null;
}
