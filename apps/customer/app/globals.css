@tailwind base;
@tailwind components;
@tailwind utilities;

/* Subtle background animation */
@keyframes subtle-shift {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 1% 1%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.animate-subtle-shift {
  animation: subtle-shift 20s ease-in-out infinite;
}

@import './firefox-fix.css';


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 5%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 5%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 5%;
    --primary: 145 27% 45%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 11%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 47%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 91%;
    --input: 0 0% 91%;
    --ring: 145 27% 45%;
    --radius: 1rem;
    --brand-primary: 145 27% 45%;
    --brand-primary-dark: 145 27% 30%;
    --brand-contrast: 4 27% 51%;
    --success: 142 71% 45%;
    --warning: 38 92% 50%;
    --error: 0 84% 60%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark, [data-theme='dark'] {
    --background: 0 0% 5%;
    --foreground: 0 0% 95%;
    --card: 0 0% 5%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 95%;
    --primary: 145 27% 30%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 17%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 17%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 17%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 17%;
    --input: 0 0% 17%;
    --ring: 145 27% 30%;
    --brand-primary: 145 27% 30%;
    --brand-primary-dark: 145 27% 20%;
    --brand-contrast: 4 27% 51%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}


@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/*@layer base {*/
/*    * {*/
/*        @apply border-foreground/20;*/
/*    }*/
/*}*/

.animate-in {
    animation: animateIn 0.3s ease 0.15s both;
}

@keyframes animateIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


.arc-inner {
    animation: rotate-center 1s 1 ease-out, fade-in 0.5s 1 ease-out;
    position: absolute;
}

.arc-middle {
    animation: rotate-center 1.5s 1 ease-out, fade-in 0.5s 1 ease-out;
    position: absolute;
}


.arc path {
    /*-webkit-filter: drop-shadow( 3px 3px 2px rgba(0, 0, 0, .7));*/
    filter: drop-shadow(0px 0px 4px rgba(0, 0, 0, 0.1));
    overflow: visible;
}

.arc-outer {
    animation: rotate-center-reverse 2s 1 ease-out, fade-in 0.5s 1 ease-out;
    position: absolute;
}

@supports (-webkit-backdrop-filter: blur(1px)) {
    .pie-icon svg {
        /*animation: fade-in  8s 1 ease-out;*/
    }
}


@keyframes fade-in {
    0% {
        opacity: 0;
    }
    70% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}


@keyframes rotate-center {
    0% {
        transform: rotate(-90deg);
    }
    100% {
        transform: rotate(0deg);
    }
}


@keyframes rotate-center-reverse {
    0% {
        transform: rotate(180deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

.dashboard-container {
    @apply mx-4 max-w-[100dvw] sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] xl:max-w-[1024px]   2xl:max-w-[1280px] w-full md:mx-auto;
}

.prose-container {
    @apply prose dark:prose-headings:text-foreground dark:prose-a:text-muted-foreground dark:prose-strong:text-foreground  dark:text-foreground  px-4 max-w-[calc(100dvw_-_2rem)] md:max-w-[calc(100%_-_2rem)] lg:w-[800px] lg:max-w-[800px]  w-full lg:mx-auto;
}

@layer base {
  * {
    @apply border-border;

    }
  body {
    @apply bg-background text-foreground;

    }

}


.page-heading-title {
    @apply text-xl md:text-2xl lg:text-3xl font-bold tracking-tight ml-2 text-foreground;
}


@media all {
    .page-break {
        display: none;
    }
}



@page {
    size: A4 portrait;
    margin: 25mm 20mm 20mm 20mm; /* top right bottom left */

    @top-center {
        content: '';
    }

    @bottom-center {
        content: counter(page);
        font-size: 10pt;
        font-family: Helvetica, "Helvetica Neue", Arial, sans-serif;
    }
}

/* TipTap Table Styling */
.ProseMirror table {
  @apply border-collapse border border-slate-300 dark:border-slate-600 w-full my-4;
  table-layout: auto;
}

.ProseMirror table td,
.ProseMirror table th {
  @apply border border-slate-300 dark:border-slate-600 px-3 py-2 text-left;
  min-width: 1em;
  vertical-align: top;
  width: auto;
}

.ProseMirror table th {
  @apply bg-slate-100 dark:bg-slate-800 font-semibold;
}

.ProseMirror table .selectedCell {
  @apply bg-blue-100 dark:bg-blue-900;
}

.ProseMirror table .column-resize-handle {
  @apply absolute top-0 right-0 bottom-0 w-1 bg-blue-500 pointer-events-none;
}

.ProseMirror table p {
  @apply m-0;
}

/* Prevent excessive colgroup generation */
.ProseMirror table colgroup col {
  min-width: auto !important;
  width: auto !important;
}

/* Custom table cell styling */
.ProseMirror .eko-table-cell {
  min-width: 1em;
  width: auto;
}

/* EkoEdit, EkoMarkdown, and report-content table styling */
.EkoEdit table,
.EkoMarkdown table,
.report-content table {
  @apply border-collapse border border-slate-300 dark:border-slate-600 w-full my-4;
}

.EkoEdit table td,
.EkoEdit table th,
.EkoMarkdown table td,
.EkoMarkdown table th,
.report-content table td,
.report-content table th {
  @apply border border-slate-300 dark:border-slate-600 px-3 py-2 text-left;
}

.EkoEdit table th,
.EkoMarkdown table th,
.report-content table th {
  @apply bg-slate-100 dark:bg-slate-800 font-semibold;
}

/* Remove margins from paragraphs inside table cells */
.EkoEdit table p,
.EkoMarkdown table p,
.report-content table p {
  @apply m-0;
}

/* Document-style formatting for reports */
.document-container {
  @apply max-w-[100dvw] md:max-w-[800px] mx-auto my-8 bg-white dark:bg-slate-900 text-slate-900 dark:text-slate-100;
  font-family: var(--font-geist-sans);
  line-height: 1.6;
  counter-reset: page-counter;
}

.document-content {
  @apply px-6 py-8 md:px-12 md:py-10 lg:px-16 lg:py-12;
  max-width: 66rem;
  margin: 0 auto;
}

.document-header {
  @apply mb-12 text-center border-b pb-6 border-slate-200 dark:border-slate-800;
}

.document-title {
  @apply text-3xl md:text-4xl font-bold mb-2 tracking-tight;
}

.document-subtitle {
  @apply text-xl md:text-2xl font-semibold mb-4 text-slate-700 dark:text-slate-300;
}

.document-metadata {
  @apply text-sm text-slate-500 dark:text-slate-400 flex flex-col gap-1 mt-4;
}

.document-toc {
  @apply my-8 p-6 border border-slate-200 dark:border-slate-800 rounded-lg;
}

.document-toc h2 {
  @apply text-xl font-semibold mb-4;
}

.toc-list {
  @apply list-decimal pl-5 space-y-2;
}

.toc-list ol {
  @apply list-decimal pl-5 mt-2 mb-2 space-y-1;
}

.toc-list a {
  @apply text-slate-700 dark:text-slate-300 hover:text-primary hover:underline;
  text-decoration: none;
}

.document-section {
  @apply mb-12;
}

.section-title {
  @apply text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-800;
}

.subsection {
  @apply mb-8;
}

.subsection-title {
  @apply text-xl font-semibold mb-4;
}

.section-content, .subsection-content {
  @apply space-y-4;
}

.section-text, .summary-text {
  @apply text-base leading-relaxed;
}

.summary-content {
  @apply mb-6;
}

.summary-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6 mb-6;
}

.summary-column {
  @apply space-y-2;
}

.summary-column h3, .summary-column h4 {
  @apply text-lg font-semibold mb-2 text-slate-800 dark:text-slate-200;
}

.detailed-sections {
  @apply mt-8 space-y-6;
}

.detailed-section {
  @apply mb-6 pb-4 border-b border-slate-100 dark:border-slate-800 last:border-0;
}

.detailed-section h5 {
  @apply text-lg font-medium mb-2 text-slate-800 dark:text-slate-200;
}

.reliability-sections, .transparency-section {
  @apply mt-6 space-y-6;
}

.reliability-section {
  @apply mb-8;
}

.reliability-section h3, .transparency-section h3 {
  @apply text-xl font-semibold mb-3;
}

.references-list {
  @apply space-y-3 mt-4;
}

.reference-item {
  @apply flex gap-2 text-sm border-b border-slate-100 dark:border-slate-800 pb-2;
}

.reference-number {
  @apply font-mono text-slate-500 dark:text-slate-400 min-w-[2rem];
}

.document-footer {
  @apply mt-16 pt-4 border-t border-slate-200 dark:border-slate-800 text-sm text-slate-500 dark:text-slate-400 flex flex-col md:flex-row justify-between items-center gap-2;
}

.page-number {
  @apply text-right;
}

.page-counter::after {
  counter-increment: page-counter;
  content: counter(page-counter);
}





.no-re-render {
    contain: style layout paint;
    transform: translateZ(0);
    will-change: transform;
}

/* Print Mode Styles - Apply print styling without @media print */
.eko-print-mode {
    background-color: white !important;
    background: white !important;
    color: black !important;
  font-family: Helvetica, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
}

.eko-print-mode .report-section,
.eko-print-mode .report-summary,
.eko-print-mode .report-group {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

/* Hide empty report sections in print mode */
.eko-print-mode .report-section:empty,
.eko-print-mode .report-summary:empty,
.eko-print-mode .report-group:empty {
  display: none !important;
}

/* Hide report sections that only contain controls/headers but no content */
.eko-print-mode .report-section:not(:has(.content:not(:empty))),
.eko-print-mode .report-summary:not(:has(.content:not(:empty))),
.eko-print-mode .report-group:not(:has(.content:not(:empty))) {
  display: none !important;
}

.eko-print-mode .report-section .absolute,
.eko-print-mode .report-summary .absolute,
.eko-print-mode .report-group .absolute,
.eko-print-mode .report-section button,
.eko-print-mode .report-summary button,
.eko-print-mode .report-group button {
  display: none !important;
}

/* Hide "Summarizes:" text in print mode */
.eko-print-mode .text-purple-500 {
  display: none !important;
}

/* Hide visual decorations in print mode */
.eko-print-mode .glass-card,
.eko-print-mode .backdrop-blur,
.eko-print-mode .bg-gradient-to-r,
.eko-print-mode .bg-gradient-to-br,
.eko-print-mode .shadow-lg,
.eko-print-mode .shadow-md,
.eko-print-mode .shadow-sm {
  background: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

/* Ensure text is readable in print mode */
.eko-print-mode h1,
.eko-print-mode h2,
.eko-print-mode h3,
.eko-print-mode h4,
.eko-print-mode h5,
.eko-print-mode h6 {
  @apply text-black;
}

.eko-print-mode p,
.eko-print-mode li,
.eko-print-mode span {
  @apply text-black;
}

/* Ensure the entire print mode container has white background */
.eko-print-mode,
.eko-print-mode *:not(.report-section):not(.report-summary):not(.report-group) {
    background-color: white !important;
    background: white !important;
}

/* Override any inherited background colors in print mode */
.eko-print-mode .prose,
.eko-print-mode .ProseMirror {
    background-color: white !important;
    background: white !important;
    color: black !important;
}

/*Drag Handles*/
.drag-handle {
    position: fixed;
    opacity: 1;
    transition: opacity ease-in 0.2s;
    border-radius: 0.25rem;

    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);
    background-repeat: no-repeat;
    background-position: center;
    width: 1.2rem;
    height: 1.5rem;
    z-index: 50;
    cursor: grab;

    &:hover {
        background-color: var(--novel-stone-100);
        transition: background-color 0.2s;
    }

    &:active {
        background-color: var(--novel-stone-200);
        transition: background-color 0.2s;
        cursor: grabbing;
    }

    &.hide {
        opacity: 0;
        pointer-events: none;
    }

    @media screen and (max-width: 600px) {
        display: none;
        pointer-events: none;
    }
}

.dark .drag-handle {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
}
