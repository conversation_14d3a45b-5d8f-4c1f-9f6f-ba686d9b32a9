import {useState} from "react";
import {FileInput, FileUploader, FileUploaderContent, FileUploaderItem} from "@ui/components/extension/file-uploader";
import {Button} from "@ui/components/ui/button";
import {DropzoneOptions} from "react-dropzone";
import {Paperclip} from "lucide-react";
import presignedUrl from "./upload-action";
import {runAsync} from "@utils/react-utils";
import {createClient} from "@/app/supabase/client";
import {useAuth} from "@/components/context/auth/auth-context";
import {useToast} from "@ui/hooks/use-toast";

;

const dropzone = {
    accept: {
        "application/pdf": [".pdf"],
    },
    multiple: true,
    maxFiles: 100,
    maxSize: 100 * 1024 * 1024,
} satisfies DropzoneOptions;


const FileSvgDraw = () => {
    return (
        <>
            <svg
                className="w-8 h-8 mb-3 text-zinc-500 dark:text-zinc-400"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 16"
            >
                <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                />
            </svg>
            <p className="mb-1 text-sm text-zinc-500 dark:text-zinc-400">
                <span className="font-semibold">Click to upload</span>
                &nbsp; or drag and drop
            </p>
            <p className="text-xs text-zinc-500 dark:text-zinc-400">
                SVG, PNG, JPG or GIF
            </p>
        </>
    );
};

export function FileUpload({onComplete}:{onComplete: (response: string[]) => any}) {
    const [files, setFiles] = useState<File[] | null>([]);
    const supabase = createClient();
    const [uploadStatus, setUploadStatus] = useState<string | null>("Upload Files");
    const auth= useAuth();
    const {toast} = useToast();


    async function uploadFiles() {
        // upload files
        if (!files) {
            toast({description:"No files selected", variant: 'destructive'});
            return;
        }


        runAsync(async () => {
            setUploadStatus("Uploading...");

            const urls = []
            for (const file of files) {
                const filename = Date.now() + "-" + file.name;
                const url = await presignedUrl(filename, file.type, auth.user?.id!);
                console.log(url);
                const uploadResponse = await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': file.type,
                    },
                    body: file,
                });
                urls.push({
                    filename: filename,
                    type: file.type,
                    url: `https://s3.eu-west-2.amazonaws.com/eko-analysis-cus-document-upload/${auth.user?.id!}/${filename}`
                })
            }
            try {

                toast({description: "Files uploaded successfully"});
                onComplete(urls.map((url) => url.url));
            } catch (error) {
                toast({description: "Failed to upload file", variant: "destructive"});
                console.error(error);
            } finally {
                setUploadStatus("Upload Files");
            }
            setFiles([]);
        });


    }

    if(!auth) return null;

    return (<div className="my-8">
        <FileUploader
            value={files}
            onValueChange={setFiles}
            dropzoneOptions={dropzone}
            className="relative min-w-full"
        >
            <FileInput className="border border-dashed border-zinc-500">
                <div className="flex items-center justify-center flex-col pt-3 pb-4 w-full ">
                    <FileSvgDraw/>
                </div>
            </FileInput>
            <FileUploaderContent className=" min-w-full">
                {files &&
                    files.length > 0 &&
                    files.map((file, i) => (
                        <FileUploaderItem key={i} index={i}>
                            <Paperclip className="h-4 w-4 stroke-current"/>
                            <span>{file.name}</span>
                        </FileUploaderItem>
                    ))}
                <div className="mt-4">
                    {((files && files.length > 0) || uploadStatus?.startsWith("Uploading")) &&
                        <Button onClick={() => uploadFiles()}
                                disabled={uploadStatus?.startsWith("Uploading")}>{uploadStatus}</Button>}
                </div>
            </FileUploaderContent>
        </FileUploader>

    </div>);
}
