"use server"
// api/generate-presigned-url.js
import {PutObjectCommand} from '@aws-sdk/client-s3';
import {getSignedUrl} from "@aws-sdk/s3-request-presigner";
import {s3} from "@utils/aws/aws";


export default async function presignedUrl(fileName:string, fileType:string, userId:string) {


    const params = {
        Bucket: "eko-analysis-cus-document-upload",
        Key: userId+"/"+fileName,
        ContentType: fileType,
    };

     // URL expires in 1 hour
    let url = await getSignedUrl(s3 as any, new PutObjectCommand(params) as any, {expiresIn: 3600});
    console.log(url);
    return url;

}
