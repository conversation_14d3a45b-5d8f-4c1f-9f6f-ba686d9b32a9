"use client";
import React, {useEffect, useRef, useState} from "react";
import {Button} from "@ui/components/ui/button";
import {Input} from "@ui/components/ui/input";
import {createClient} from "@/app/supabase/client";
import {runAsync} from "@utils/react-utils";
import {useSearchParams} from "next/navigation";
import {backOfficeRequestListener, callBackofficeAsync} from "@/components/backoffice";
import {useToast} from "@ui/hooks/use-toast";
import {Link} from "lucide-react";
import {FileUpload} from "@/app/customer/files/file-upload";
import {Card, CardContent, CardHeader, CardTitle} from "@ui/components/ui/card";
import {APIQueueType} from "@/types";
import {conciseDateTime} from "@utils/date-utils";
import {useAuth} from "@/components/context/auth/auth-context";
import {EntitySelector} from "@/components/entity-selector";

export function DocumentUpload({onComplete, onChange}: {
    onComplete: (response: any) => any,
    onChange?: ((status: string, payload: any, error: any) => void) | undefined
}) {
    const [files, setFiles] = useState<File[] | null>([]);
    const supabase = createClient();
    const [uploadStatus, setUploadStatus] = useState<string | null>("Upload Files");
    const urlRef = useRef<HTMLInputElement>(null);
    const entityRef = useRef<HTMLInputElement>(null);
    const queryParams = useSearchParams();
    const [listeners, setListeners] = useState<any[]>([]);
    const [company, setCompany] = useState<string | undefined>(queryParams.get("entity") || undefined);
    const {toast} = useToast();
    const [apiQueue, setApiQueue] = useState<APIQueueType[]>([]);
    const auth= useAuth();

    async function updateQueue(userId: string) {
        const {
            data: queue,
            error: queueError
        } = await supabase.from('api_queue').select().eq("requester", userId).eq("request_action", "single_url").order("created_at", {ascending: false}).limit(5)
        setApiQueue(queue as APIQueueType[]);
    }

    useEffect(() => {
        runAsync(async () => {
            const userId = auth.user?.id;
            if (!userId) return;
            await updateQueue(userId);

        });
        return () => {
            listeners.forEach((listener) => listener.unsubscribe());
        }
    }, []);


    async function uploadUrl(url: string) {
        const id = await callBackofficeAsync(supabase, "single_url", {
            "url": url,
            "entity": company
        });

        const listener = backOfficeRequestListener(supabase, auth.user?.id!, id, (status, payload, error, message) => {
            updateQueue(auth.user?.id!);
            if (onChange) {
                onChange(status, payload, error);
            }
            if (status === 'completed') {
                toast({description: `Document processed successfully`});
                listener.unsubscribe();
                setListeners(listeners.filter((l) => l !== listener));
                onComplete(payload);
            } else if (status === 'error') {
                toast({description: "Failed to upload file", variant: "destructive"});
                listener.unsubscribe();
                setListeners(listeners.filter((l) => l !== listener));
            } else if (status === 'processing') {
                toast({description: "Processing file now, this may take a few minutes"});
            }
        });
        await updateQueue(auth.user?.id!);

        setListeners([...listeners, listener]);
        toast({description: `URL submitted successfully`});
    }

    async function clickHandler() {

        try {
            if (urlRef?.current?.value && company) {
                const url= urlRef?.current?.value;

            } else {
                toast({description: "Please select a company and enter a URL", variant: "destructive"});
                return;
            }
        } catch (error) {
            toast({description: "Failed to upload file", variant: "destructive"});
            console.error(error);
        } finally {
            setUploadStatus("Upload Files");
        }


    }


    return (<div className=" ">
        <EntitySelector defaultEntity={queryParams.get("entity") || undefined} onChange={(value) => setCompany(value)}/>
        <div className="flex flex-row mt-4">
            <Input ref={urlRef} placeholder="Enter the URL of a report to upload"/><Button
            onClick={clickHandler}> <Link className="h-4 w-4 mr-2"/> Upload</Button>
        </div>

        <FileUpload onComplete={(urls: string[]) => urls.forEach(url => uploadUrl(url))}/>


        <Card>
            <CardHeader>
                <CardTitle>Analysis Queue</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="border-b">
                        <tr className="bg-zinc-50">
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">URL</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Entity</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Status</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Message</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Date</th>
                        </tr>
                        </thead>
                        <tbody className="divide-y">
                        {apiQueue.map((analysis) => (
                                <tr key={analysis.id} className="hover:bg-zinc-50">
                                    <td className="px-4 py-2">{(analysis.request_data as any)?.url}</td>
                                    <td className="px-4 py-2">{(analysis.request_data as any)?.entity}</td>
                                    <td className="px-4 py-2">{analysis.status}</td>
                                    <td className="px-4 py-2">{!!(analysis.response_data as any)?.error ? (analysis.response_data as any)["error"] : ""}</td>
                                    <td className="px-4 py-2">{conciseDateTime(new Date(analysis.created_at!), Date.now())}</td>

                                </tr>
                            )
                        )}
                        </tbody>
                    </table>
                </div>
            </CardContent>
        </Card>
    </div>);
}
