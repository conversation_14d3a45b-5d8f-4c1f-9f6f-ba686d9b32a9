'use client'

import React, { useEffect, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { Button } from '@ui/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Input } from '@ui/components/ui/input'
import {
  Clock,
  Edit3,
  FileText,
  FolderOpen,
  Loader2,
  MoreHorizontal,
  Plus,
  Search,
  Share2,
  Trash2,
  User,
} from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { useRouter } from 'next/navigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogTitle } from '@ui/components/ui/dialog'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { DocumentTemplates } from '@/components/editor/templates/DocumentTemplates'
import { formatTimeAgo } from '@utils/date-utils'

interface Document {
  id: string
  title: string
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    email?: string
    avatar?: string
  }
  isOwner: boolean
  metadata?: any
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [showShared, setShowShared] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [isCreatingDocument, setIsCreatingDocument] = useState(false)

  const { toast } = useToast()
  const router = useRouter()
  const supabase = createClient()

  // Set page title
  useEffect(() => {
    document.title = 'Documents - ekoIntelligence'
  }, [])

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  // Load documents from Supabase
  const loadDocuments = async () => {
    setLoading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      let query = supabase
        .from('collaborative_documents')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          created_by,
          updated_by,
          metadata
        `)
        .order('updated_at', { ascending: false })

      // Filter based on showShared
      if (!showShared) {
        // Only show documents created by current user
        query = query.eq('created_by', user.id)
      }
      // If showShared is true, RLS will handle showing accessible documents

      const { data: documents, error } = await query

      if (error) {
        throw error
      }

      // Transform documents to match expected format
      const transformedDocuments = documents?.map(doc => ({
        id: doc.id,
        title: doc.title || 'Untitled Document',
        createdAt: new Date(doc.created_at || new Date()),
        updatedAt: new Date(doc.updated_at || new Date()),
        author: {
          id: doc.created_by || 'unknown',
          name: doc.created_by === user.id
            ? (user.user_metadata?.name || user.email || 'You')
            : 'Unknown User',
          email: doc.created_by === user.id ? user.email : '<EMAIL>',
          avatar: doc.created_by === user.id ? user.user_metadata?.avatar_url : undefined
        },
        isOwner: doc.created_by === user.id,
        metadata: doc.metadata || {}
      })) || []

      setDocuments(transformedDocuments)
    } catch (error) {
      console.error('Error loading documents:', error)
      toast({
        title: 'Error',
        description: 'Failed to load documents',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Load documents on mount and set up real-time subscription
  useEffect(() => {
    loadDocuments()

    // Set up real-time subscription for documents
    const channel = supabase
      .channel('collaborative_documents')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'collaborative_documents'
        },
        () => {
          // Reload documents when changes occur
          loadDocuments()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [showShared, supabase])

  // Filter documents based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDocuments(documents)
    } else {
      const filtered = documents.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDocuments(filtered)
    }
  }, [documents, searchQuery])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }


  const handleCreateDocument = () => {
    setIsCreatingDocument(true)
    setShowTemplateDialog(true)
  }

  const handleSelectTemplate = async (template: any, entityId?: string, runId?: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      // Generate a unique document ID
      const documentId = crypto.randomUUID()

      // Process template content to handle table of contents marker
      let processedContent = template.content || ''
      let processedData = template.data || null

      // For templates with markdown content, replace TABLE_OF_CONTENTS marker
      if (processedContent && processedContent.includes('<!-- TABLE_OF_CONTENTS -->')) {
        // Replace the marker with a placeholder that will be processed by the editor
        processedContent = processedContent.replace(
          '<!-- TABLE_OF_CONTENTS -->',
          '\n<div data-type="table-of-contents" id="table-of-contents"></div>\n'
        )
      }

      const { data: document, error } = await supabase
        .from('collaborative_documents')
        .insert({
          id: documentId,
          title: template.name === 'Blank Document' ? 'Untitled Document' : template.name,
          content: processedContent,
          initial_content: processedContent,
          data: processedData,
          created_by: user.id,
          updated_by: user.id,
          metadata: {
            template_id: template.id,
            entity_id: entityId,
            run_id: runId
          }
        })
        .select()
        .single()

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document created successfully'
      })

      // Close template dialog and navigate to the new document
      setShowTemplateDialog(false)
      setIsCreatingDocument(false)
      router.push(`/customer/documents/${documentId}`)
    } catch (error) {
      console.error('Error creating document:', error)
      toast({
        title: 'Error',
        description: 'Failed to create document',
        variant: 'destructive'
      })
      // Reset state on error
      setIsCreatingDocument(false)
      setShowTemplateDialog(false)
    }
  }

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('collaborative_documents')
        .delete()
        .eq('id', documentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document deleted successfully'
      })

      // Reload documents
      loadDocuments()
    } catch (error) {
      console.error('Error deleting document:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete document',
        variant: 'destructive'
      })
    }
  }

  const handleDocumentClick = (documentId: string) => {
    router.push(`/customer/documents/${documentId}`)
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Documents</h1>
            <p className="text-muted-foreground mt-1">
              Create and manage your documents
            </p>
          </div>
          <Button onClick={handleCreateDocument} data-testid="new-document-button">
            <Plus className="w-4 h-4 mr-2" />
            New Document
          </Button>
        </div>

        {/* Search and Filter */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={!showShared ? 'default' : 'outline'}
                  onClick={() => setShowShared(false)}
                >
                  My Documents
                </Button>
                <Button
                  size="sm"
                  variant={showShared ? 'default' : 'outline'}
                  onClick={() => setShowShared(true)}
                >
                  Shared
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents Grid */}
        {isCreatingDocument ? (
          <div className="text-center py-12" data-testid="document-creation-loading">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Creating document...</p>
            <p className="text-sm text-muted-foreground mt-2">Please select a template to continue</p>
          </div>
        ) : loading ? (
          <div className="text-center py-12">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Loading documents...</p>
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-12">
            <FolderOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? 'No documents found' : showShared ? 'No shared documents' : 'No documents yet'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {!searchQuery && !showShared && 'Create your first document to get started'}
            </p>
            {!searchQuery && !showShared && (
              <Button onClick={handleCreateDocument}>
                <Plus className="w-4 h-4 mr-2" />
                Create Document
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" data-testid="documents-list">
            {filteredDocuments.map((document) => (
              <Card
                key={document.id}
                className="group cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
                onClick={() => handleDocumentClick(document.id)}
                data-testid="document-card"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <FileText className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      <CardTitle className="text-sm font-medium truncate">
                        {document.title}
                      </CardTitle>
                      {!document.isOwner && (
                        <Share2 className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleDocumentClick(document.id)}>
                          <Edit3 className="w-3 h-3 mr-2" />
                          Open
                        </DropdownMenuItem>
                        {document.isOwner && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteDocument(document.id)
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="w-3 h-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <User className="w-3 h-3" />
                      <span className="truncate">{document.author.name}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>Updated {formatTimeAgo(document.updatedAt)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Template Selection Dialog */}
      <Dialog 
        open={showTemplateDialog} 
        onOpenChange={(open) => {
          setShowTemplateDialog(open)
          if (!open) {
            // Reset creating state when dialog is closed
            setIsCreatingDocument(false)
          }
        }}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] p-0">
          <VisuallyHidden>
            <DialogTitle>Choose Document Template</DialogTitle>
          </VisuallyHidden>
          <DocumentTemplates
            onSelectTemplate={handleSelectTemplate}
            onClose={() => {
              setShowTemplateDialog(false)
              setIsCreatingDocument(false)
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
