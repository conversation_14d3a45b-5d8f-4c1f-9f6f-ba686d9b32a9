'use client'

import {useEffect, useState} from 'react'
import {Bell} from 'lucide-react'
import {Card, CardContent, CardHeader, CardTitle} from "@ui/components/ui/card"
import {createClient} from "@/app/supabase/client";
import {AccountMessageType} from "@/types";
import {AccountMessage} from "@/components/account-messages";
import {PageHeader} from "@/components/page-header";


export default function NotificationsPage() {
    const [notifications, setNotifications] = useState<AccountMessageType[]>([])
    const supabase = createClient();
    useEffect(() => {
        const fetchNotifications = async () => {
            const {
                data: db_notifications,
                error
            } = await supabase.from('acc_messages').select('*').is("deleted_at", null)
            if (error) {
                console.error(error)
                return
            }
            setNotifications(db_notifications);
        }
        fetchNotifications()
    }, []);


    return (
        <><PageHeader/>
            <div className="container min-w-full mx-auto py-10" data-testid="notifications-page">
                <Card className="max-w-4xl mx-auto">
                    <CardHeader>
                        <CardTitle className="text-2xl flex items-center gap-2">
                            <Bell className="h-6 w-6"/>
                            Notifications
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {notifications.length === 0 ? (
                            <p className="text-center text-muted-foreground">No notifications</p>
                        ) : (
                            <div className="space-y-4">
                                {notifications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).map((notification) =>
                                    <AccountMessage key={notification.id}
                                                    onRead={(id) => setNotifications(notifications.map(notif => notif.id === id ? {
                                                        ...notif,
                                                        read: true
                                                    } : notif))}
                                                    onDelete={(id) => setNotifications(notifications.filter(notif => notif.id !== id))}

                                                    notification={notification}/>)}

                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </>
    )
}
