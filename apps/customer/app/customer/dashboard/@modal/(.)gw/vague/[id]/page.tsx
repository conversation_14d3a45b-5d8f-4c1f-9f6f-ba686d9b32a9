"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { VagueType } from '@/types'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/context/auth/auth-context'
import { VagueTerm } from '@/app/customer/dashboard/gw/vague/vague-term'
import { SimpleModal } from '@/components/simple-modal'
import { convertVagueV2ToVagueV1 } from '@/utils/vague-utils'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);

    const {
        id
    } = params;

    const [vagueData, setVagueData] = useState<VagueType | null>(null);
    const router = useRouter();
    const auth = useAuth();
    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            // Try to get vague term from xfer_gw_vague_v2 first
            const {
                data: vagueV2Data,
                error: vagueV2Error
            } = await supabase
                .from("xfer_gw_vague_v2")
                .select("*")
                .eq("id", +id)
                .single();

            if (vagueV2Data) {
                // Convert V2 vague term to V1 format
                const convertedVague = convertVagueV2ToVagueV1(vagueV2Data);
                setVagueData(convertedVague);
            } else {
                // No need to fallback anymore
                console.log("Vague term not found in V2 table");
            }
        };

        fetchData();
    }, [id]);

    if (!vagueData) return null;

    return (
      <SimpleModal>
                    <VagueTerm item={vagueData} admin={auth.admin}/>
      </SimpleModal>
    );
}
