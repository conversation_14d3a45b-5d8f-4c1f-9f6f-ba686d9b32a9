"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { PromiseTypeV2 } from '@/types'
import { useAuth } from '@/components/context/auth/auth-context'
import { SimpleModal } from '@/components/simple-modal'
import { PromiseCardV2 } from '@/app/customer/dashboard/gw/promises/promise'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const {id} = params;

    const [data, setData] = useState<PromiseTypeV2 | null>(null);
    const auth = useAuth();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            const {
                data: promiseData,
                error
            } = await supabase.from("xfer_gw_promises_v2").select("*").eq("id", +id).single();

            if (error) {
                console.error("Error fetching promise:", error);
            } else {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setData(promiseData as unknown as PromiseTypeV2);
            }
        };

        fetchData();
    }, [id]);

    return data && (
        <SimpleModal>
            <PromiseCardV2 item={data!} admin={auth.admin}/>
        </SimpleModal>
    );
}
