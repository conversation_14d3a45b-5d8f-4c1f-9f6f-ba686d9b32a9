import { Issue } from '@/components/issues'
import React, { useEffect, useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { CheckCircle, FlagIcon, Flame, PieChartIcon, ShieldCheck } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { impactFlagColorMap, severityColor } from '@utils/lib/colors'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { Badge } from '@ui/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Citation, CitationType, reduceCitations } from '@/components/citation'
import { FlagTypeV2, ModelSectionTypeV2 } from '@/types'
import { getModelSection } from '@/utils/model-section-loader'
import {
  getCitations,
  getFlagSummary,
  getFlagText,
  getFlagTitle,
  getFlagType,
  getModelSections,
  getReason,
} from '@/utils/flag-converter'

export function FlagExpandedDetail(props: {
    flag: FlagTypeV2,
    issueMap: Map<string, Issue>,
    admin: boolean
}) {
    const compareFn = (a: any, b: any) => b.score - a.score;
    const [modelSections, setModelSections] = useState<Record<string, ModelSectionTypeV2 | null>>({});

    // Load model sections for this flag
    useEffect(() => {
        async function loadModelSections() {
            const sections: Record<string, ModelSectionTypeV2 | null> = {};
            const modelSectionsMap = getModelSections(props.flag);

            // Load each model section
            for (const [modelName, sectionId] of Object.entries(modelSectionsMap)) {
                const section = await getModelSection(modelName, sectionId);
                sections[modelName] = section;
            }

            setModelSections(sections);
        }

        loadModelSections();
    }, [props.flag]);

    const flagType = getFlagType(props.flag);
    const flagTitle = getFlagTitle(props.flag);
    const flagSummary = getFlagSummary(props.flag);
    const flagText = getFlagText(props.flag);
    const reason = getReason(props.flag);
    const citations = getCitations(props.flag);
    const modelData = props.flag.model;

    return <Card className="mb-8" data-testid="flag-detail-modal">
        <CardHeader>
            <CardTitle className="text-base dark:text-foreground">
                <div className="space-y-3">
                    {/* Title row with icon and text */}
                    <div className="flex items-start gap-2">
                        <FlagIcon className={cn(`w-4 h-4 mt-1 flex-shrink-0`)}
                                  fill={"var(--" + impactFlagColorMap[flagType + "-" + modelData.impact] + ")"}
                        />
                        <h3 className="dark:text-foreground prose flex-1 min-w-0 break-words leading-tight" data-testid="modal-flag-title">
                            {props.issueMap.get(props.flag.issue!)?.title || flagTitle}
                        </h3>
                    </div>

                    {/* Badges row */}
                    <div className="flex flex-wrap gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <CheckCircle className="w-3 h-3"
                                                     stroke={"var(--" + severityColor(modelData.confidence) + ")"}
                                        />
                                        {modelData.confidence}%
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Confidence: {modelData.confidence}% - The confidence our
                                        systems have in this evaluation.</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <Flame className="w-3 h-3"
                                               stroke={"var(--" + severityColor(flagType === "green" ? modelData.impact : (100 - modelData.impact)) + ")"}
                                        />
                                        {modelData.impact > 50 ? "High" : "Low"}
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Impact: {modelData.impact}% - How great an impact does
                                        this action have on people, animals and the
                                        ecosystem?</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <ShieldCheck className="w-3 h-3"
                                                     stroke={"var(--" + severityColor(modelData.authentic) + ")"}
                                        />
                                        Auth
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Authenticity: {modelData.authentic}%
                                        - {flagType === "red" ? "Was this an intentional action?" : "How authentic is this action? Is it for marketing or is it for good?"}</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <PieChartIcon className="w-3 h-3"
                                                      stroke={"var(--" + severityColor(flagType == "green" ? modelData.contribution : (100 - modelData.contribution)) + ")"}
                                        />
                                        Contrib
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Contribution: {modelData.contribution}% - How much did
                                        this entities actions contribute to the issue.</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>

                    {/* Reason text */}
                    {reason && <p className="text-sm text-muted-foreground" data-testid="modal-flag-description">{reason}</p>}
                </div>
            </CardTitle>
        </CardHeader>
        <CardContent>
            {/* Display model sections if available */}
            {Object.keys(modelSections).length > 0 && (
                <div className="mb-4">
                    <h4 className="text-sm font-semibold mb-2">Model Classifications</h4>
                    <div className="flex flex-wrap gap-2">
                        {Object.entries(modelSections).map(([modelName, section]) => (
                            section && (
                                <Badge key={modelName} variant="outline" className="flex items-center gap-1">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <span className="flex items-center">
                                                    <span className="font-semibold">{modelName}:</span>
                                                    <span className="ml-1">{section.title || section.section}</span>
                                                </span>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="max-w-xs">{section.description || "No description available"}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </Badge>
                            )
                        ))}
                    </div>
                </div>
            )}

            <div data-testid="modal-flag-analysis">
                <EkoMarkdown
                    citations={citations as CitationType[]}
                    admin={props.admin}>{flagText?.replaceAll(/\n/g, "\n\n")}</EkoMarkdown>
            </div>

            {reduceCitations(citations as CitationType[])
                .sort(compareFn)
                .map((data, j) => <Citation key={j} data={data} admin={props.admin}/>)}
        </CardContent>
    </Card>;
}
