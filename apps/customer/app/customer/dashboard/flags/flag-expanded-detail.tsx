import { Issue } from '@/components/issues'
import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import {
    AlertTriangle,
    CheckCircle,
    FlagIcon,
    Flame,
    Heart,
    Leaf,
    Scale,
    Shield,
    TrendingDown,
    TrendingUp,
    Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { impactFlagColorMap, impactText, severityColor } from '@utils/lib/colors'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { Badge } from '@ui/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Citation, CitationType, reduceCitations } from '@/components/citation'
import { FlagTypeV2, ModelSectionTypeV2 } from '@/types'
import { getModelSection } from '@/utils/model-section-loader'
import {
    getCitations,
    getFlagSummary,
    getFlagText,
    getFlagTitle,
    getFlagType,
    getModelSections,
    getReason,
} from '@/utils/flag-converter'

export function FlagExpandedDetail(props: {
    flag: FlagTypeV2,
    issueMap: Map<string, Issue>,
    admin: boolean
}) {
    const compareFn = (a: any, b: any) => b.score - a.score;
    const [modelSections, setModelSections] = useState<Record<string, ModelSectionTypeV2 | null>>({});

    // Load model sections for this flag
    useEffect(() => {
        async function loadModelSections() {
            const sections: Record<string, ModelSectionTypeV2 | null> = {};
            const modelSectionsMap = getModelSections(props.flag);

            // Load each model section
            for (const [modelName, sectionId] of Object.entries(modelSectionsMap)) {
                const section = await getModelSection(modelName, sectionId);
                sections[modelName] = section;
            }

            setModelSections(sections);
        }

        loadModelSections();
    }, [props.flag]);

    const flagType = getFlagType(props.flag);
    const flagTitle = getFlagTitle(props.flag);
    const flagSummary = getFlagSummary(props.flag);
    const flagText = getFlagText(props.flag);
    const reason = getReason(props.flag);
    const citations = getCitations(props.flag);
    const modelData = props.flag.model;

    return <Card className="mb-8" data-testid="flag-detail-modal">
        <CardHeader>
            <CardTitle className="text-base dark:text-foreground">
                <div className="space-y-3">
                    {/* Title row with icon and text */}
                    <div className="flex items-start gap-2">
                        <FlagIcon className={cn(`w-4 h-4 mt-1 flex-shrink-0`)}
                                  fill={"var(--" + impactFlagColorMap[flagType + "-" + modelData.impact] + ")"}
                        />
                        <h3 className="dark:text-foreground prose flex-1 min-w-0 break-words leading-tight" data-testid="modal-flag-title">
                            {props.issueMap.get(props.flag.issue!)?.title || flagTitle}
                        </h3>
                    </div>

                    {/* Badges row */}
                    <div className="flex flex-wrap gap-2">
                        {/* Enhanced Impact Badge - Made prominent and clickable */}
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge 
                                        variant="outline" 
                                        className={cn(
                                            "flex items-center gap-2 px-3 py-2 text-base font-semibold border-2",
                                            "bg-gradient-to-r from-white/90 to-white/70 dark:from-slate-800/90 dark:to-slate-700/70 backdrop-blur-sm",
                                            modelData.impact > 50 
                                                ? "border-orange-400 text-orange-700 dark:text-orange-300" 
                                                : "border-blue-400 text-blue-700 dark:text-blue-300"
                                        )}
                                        data-testid="impact-badge"
                                    >
                                        <Flame className="w-5 h-5"
                                               stroke={"var(--" + severityColor(flagType === "green" ? modelData.impact : (100 - modelData.impact)) + ")"}
                                        />
                                        <span className="font-bold">
                                            {impactText(modelData.impact)} Impact
                                        </span>
                                        <span className="text-sm opacity-80">
                                            {modelData.impact}%
                                        </span>
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Impact: {modelData.impact}% - How great an impact does
                                        this action have on people, animals and the
                                        ecosystem?</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <CheckCircle className="w-3 h-3"
                                                     stroke={"var(--" + severityColor(modelData.confidence) + ")"}
                                        />
                                        {modelData.confidence}%
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Confidence: {modelData.confidence}% - The confidence our
                                        systems have in this evaluation.</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                    </div>

                    {/* Reason text */}
                    {reason && <p className="text-sm text-muted-foreground" data-testid="modal-flag-description">{reason}</p>}
                </div>
            </CardTitle>
        </CardHeader>
        <CardContent>
            {/* Display model sections if available */}
            {Object.keys(modelSections).length > 0 && (
                <div className="mb-4">
                    <h4 className="text-sm font-semibold mb-2">Model Classifications</h4>
                    <div className="flex flex-wrap gap-2">
                        {Object.entries(modelSections).map(([modelName, section]) => (
                            section && (
                                <Badge key={modelName} variant="outline" className="flex items-center gap-1">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <span className="flex items-center">
                                                    <span className="font-semibold">{modelName}:</span>
                                                    <span className="ml-1">{section.title || section.section}</span>
                                                </span>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="max-w-xs">{section.description || "No description available"}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </Badge>
                            )
                        ))}
                    </div>
                </div>
            )}

            {/* Impact Assessment Details */}
            {modelData.impact_value_analysis?.impact_measurement && (
                <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Scale className="w-5 h-5" />
                        Detailed Impact Assessment
                    </h4>
                    
                    {/* Net Impact Overview */}
                    <Card className="glass-card mb-4">
                        <CardContent className="pt-4">
                            <div className="flex items-center justify-between mb-4">
                                <div>
                                    <div className="text-sm text-muted-foreground">Net Impact Score</div>
                                    <div className={cn(
                                        "text-2xl font-bold flex items-center gap-2",
                                        modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? "text-green-600" : "text-red-600"
                                    )}>
                                        {modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? 
                                            <TrendingUp className="w-6 h-6" /> : 
                                            <TrendingDown className="w-6 h-6" />
                                        }
                                        {(Math.abs(modelData.impact_value_analysis.impact_measurement.net_impact_score) * 100).toFixed(0)}%
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                        {modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? "Net Positive" : "Net Negative"}
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4 text-center">
                                    <div>
                                        <div className="text-lg font-bold text-red-600">
                                            {(modelData.impact_value_analysis.impact_measurement.harm_score * 100).toFixed(0)}%
                                        </div>
                                        <div className="text-xs text-muted-foreground">Avg Harm</div>
                                    </div>
                                    <div>
                                        <div className="text-lg font-bold text-green-600">
                                            {(modelData.impact_value_analysis.impact_measurement.benefit_score * 100).toFixed(0)}%
                                        </div>
                                        <div className="text-xs text-muted-foreground">Avg Benefit</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="text-sm text-muted-foreground">
                                <strong>Event:</strong> {modelData.impact_value_analysis.impact_measurement.event_summary}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Harm and Benefit Assessments */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        {/* Harm Assessment */}
                        <Card className="glass-card">
                            <CardHeader>
                                <CardTitle className="text-base flex items-center gap-2 text-red-700 dark:text-red-400">
                                    <TrendingDown className="w-4 h-4" />
                                    Harm Assessment
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {[
                                    { key: 'animals', icon: Heart, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.animals },
                                    { key: 'humans', icon: Users, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.humans },
                                    { key: 'environment', icon: Leaf, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.environment }
                                ].map(({ key, icon: Icon, assessment }) => (
                                    <div key={key} className="border-l-2 border-red-200 pl-3">
                                        <div className="flex items-center justify-between mb-1">
                                            <div className="flex items-center gap-2 text-sm font-medium">
                                                <Icon className="w-4 h-4" />
                                                {key.charAt(0).toUpperCase() + key.slice(1)}
                                            </div>
                                            <Badge variant="outline" className={cn(
                                                "text-xs",
                                                assessment.confidence === 'high' ? 'text-green-600 bg-green-50' :
                                                assessment.confidence === 'medium' ? 'text-yellow-600 bg-yellow-50' :
                                                'text-red-600 bg-red-50'
                                            )}>
                                                {assessment.confidence} confidence
                                            </Badge>
                                        </div>
                                        <div className="text-lg font-bold text-red-600 mb-1">
                                            {(assessment.score * 100).toFixed(0)}%
                                        </div>
                                        <div className="text-xs text-muted-foreground mb-2">
                                            {assessment.reasoning.substring(0, 120)}...
                                        </div>
                                        <div className="text-xs space-y-1">
                                            <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate.substring(0, 80)}...</div>
                                            <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term.substring(0, 80)}...</div>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Benefit Assessment */}
                        <Card className="glass-card">
                            <CardHeader>
                                <CardTitle className="text-base flex items-center gap-2 text-green-700 dark:text-green-400">
                                    <TrendingUp className="w-4 h-4" />
                                    Benefit Assessment
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {[
                                    { key: 'animals', icon: Heart, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.animals },
                                    { key: 'humans', icon: Users, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.humans },
                                    { key: 'environment', icon: Leaf, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.environment }
                                ].map(({ key, icon: Icon, assessment }) => (
                                    <div key={key} className="border-l-2 border-green-200 pl-3">
                                        <div className="flex items-center justify-between mb-1">
                                            <div className="flex items-center gap-2 text-sm font-medium">
                                                <Icon className="w-4 h-4" />
                                                {key.charAt(0).toUpperCase() + key.slice(1)}
                                            </div>
                                            <Badge variant="outline" className={cn(
                                                "text-xs",
                                                assessment.confidence === 'high' ? 'text-green-600 bg-green-50' :
                                                assessment.confidence === 'medium' ? 'text-yellow-600 bg-yellow-50' :
                                                'text-red-600 bg-red-50'
                                            )}>
                                                {assessment.confidence} confidence
                                            </Badge>
                                        </div>
                                        <div className="text-lg font-bold text-green-600 mb-1">
                                            {(assessment.score * 100).toFixed(0)}%
                                        </div>
                                        <div className="text-xs text-muted-foreground mb-2">
                                            {assessment.reasoning.substring(0, 120)}...
                                        </div>
                                        <div className="text-xs space-y-1">
                                            <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate.substring(0, 80)}...</div>
                                            <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term.substring(0, 80)}...</div>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Uncertainties and Quality */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        {modelData.impact_value_analysis.impact_measurement.key_uncertainties.length > 0 && (
                            <Card className="glass-card">
                                <CardHeader>
                                    <CardTitle className="text-base flex items-center gap-2">
                                        <AlertTriangle className="w-4 h-4 text-amber-500" />
                                        Key Uncertainties
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-1 text-sm text-muted-foreground">
                                        {modelData.impact_value_analysis.impact_measurement.key_uncertainties.slice(0, 3).map((uncertainty, i) => (
                                            <li key={i} className="flex items-start gap-2">
                                                <span className="text-amber-500 mt-1">•</span>
                                                <span>{uncertainty}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                            </Card>
                        )}

                        {modelData.impact_value_analysis.impact_evaluation && (
                            <Card className="glass-card">
                                <CardHeader>
                                    <CardTitle className="text-base flex items-center gap-2">
                                        <Shield className="w-4 h-4" />
                                        Assessment Quality
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Overall Quality:</span>
                                            <Badge variant="outline" className={cn(
                                                modelData.impact_value_analysis.impact_evaluation.overall_quality === 'excellent' ? 'text-green-600 bg-green-50' :
                                                modelData.impact_value_analysis.impact_evaluation.overall_quality === 'good' ? 'text-blue-600 bg-blue-50' :
                                                modelData.impact_value_analysis.impact_evaluation.overall_quality === 'fair' ? 'text-yellow-600 bg-yellow-50' :
                                                'text-red-600 bg-red-50'
                                            )}>
                                                {modelData.impact_value_analysis.impact_evaluation.overall_quality}
                                            </Badge>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Completeness:</span>
                                            <span>{(modelData.impact_value_analysis.impact_evaluation.completeness_score * 100).toFixed(0)}%</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Avg Confidence:</span>
                                            <span>{(modelData.impact_value_analysis.impact_evaluation.confidence_analysis.avg_confidence * 100).toFixed(0)}%</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            )}

            <div data-testid="modal-flag-analysis">
                <EkoMarkdown
                    citations={citations as CitationType[]}
                    admin={props.admin}>{flagText?.replaceAll(/\n/g, "\n\n")}</EkoMarkdown>
            </div>

            {reduceCitations(citations as CitationType[])
                .sort(compareFn)
                .map((data, j) => <Citation key={j} data={data} admin={props.admin}/>)}
        </CardContent>
        
    </Card>;
}
