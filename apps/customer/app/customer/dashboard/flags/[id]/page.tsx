"use client";
import { createClient } from '@/app/supabase/client'
import React, { useEffect, useState } from 'react'
import { FlagExpandedDetail } from '@/app/customer/dashboard/flags/flag-expanded-detail'
import { runAsync } from '@utils/react-utils'
import { useNav } from '@/components/context/nav/nav-context'
import { useParams } from 'next/navigation'
import { useToast } from '@ui/hooks/use-toast'
import { FlagTypeV2 } from '@/types'
import { ensureModelParsed, getFlagShortTitle, getFlagTitle } from '@/utils/flag-converter'

export default function Page() {
    const supabase = createClient();
    const id= useParams().id;
    const [flagData, setFlagData] = useState<FlagTypeV2 | null>(null);
    const [issueMap, setIssueMap] = useState<any>(null);
    const nav = useNav();
    const admin = false;
    const {toast} = useToast();

    useEffect(() => {
        runAsync(async () => {
            if (id) {
                // Model sections are now directly attached to each flag
                // No need to query xfer_issues table
                setIssueMap(new Map());

                // Get flag from the xfer_flags_v2 table
                const {data: flagDataV2, error: flagErrorV2} = await supabase
                    .from('xfer_flags_v2')
                    .select('*')
                    .eq("id", +id)
                    .single();

                if (flagDataV2) {
                    // First convert to unknown, then to FlagTypeV2 to avoid type errors
                    // This is safe because ensureModelParsed will properly parse the model
                    const parsedFlag = ensureModelParsed(flagDataV2 as unknown as FlagTypeV2);
                    setFlagData(parsedFlag);

                    // Update navigation
                    const flagTitle = getFlagTitle(parsedFlag);
                    const flagShortTitle = getFlagShortTitle(parsedFlag);

                    nav.changeTitle(flagTitle || "Flag Details");
                    nav.changeNavPath([
                        {href: "/customer/dashboard", label: "Dashboard"},
                        {href: "/customer/dashboard/flags", label: "Flags"},
                        {href: "/customer/dashboard/flags/" + id, label: flagShortTitle || "Flag " + id}
                    ]);
                } else {
                    toast({description: "Flag not found", variant: "destructive"});
                }
            }
        });
    },[]);
    if(!flagData) {
        return <div>Loading...</div>
    }
    return (
        <FlagExpandedDetail flag={flagData} issueMap={issueMap} admin={admin}/>
    )
}
