import Image from "next/image";
import {Skeleton} from "@ui/components/ui/skeleton";

export function FlagsSkeleton() {
    return (<>
        <div className="grid gap-4   grid-cols-1  lg:grid-cols-1 xl:grid-cols-2">

            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


        </div>
    </>)
}
