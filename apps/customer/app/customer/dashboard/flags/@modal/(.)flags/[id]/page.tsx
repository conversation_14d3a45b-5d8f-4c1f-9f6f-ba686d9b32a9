"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { createIssueMap } from '@/components/issues'
import { useAuth } from '@/components/context/auth/auth-context'
import { useRouter } from 'next/navigation'
import { useToast } from '@ui/hooks/use-toast'
import { FlagExpandedDetail } from '@/app/customer/dashboard/flags/flag-expanded-detail'
import { SimpleModal } from '@/components/simple-modal'
import { FlagTypeV2 } from '@/types'
import { ensureModelParsed } from '@/utils/flag-converter'

export default function Page(
    props: {
        params: Promise<{ id: string }>,
        searchParams: Promise<{ entity: string, run: string, model: string }>
    }
) {
    const searchParams = use(props.searchParams);

    const {
        entity,
        run,
        model
    } = searchParams;

    const params = use(props.params);

    const {
        id
    } = params;

    const supabase = createClient();
    const [open, setOpen] = useState(true);
    const [issuesData, setIssuesData] = useState<any>(null);
    const [flagData, setFlagData] = useState<FlagTypeV2 | null>(null);
    const [issuesError, setIssuesError] = useState<any>(null);
    const [flagError, setFlagError] = useState<any>(null);
    const {toast} = useToast();
    const auth = useAuth();
    const router = useRouter();

    useEffect(() => {
        async function fetchData() {
            try {
                // Issues are no longer used, but we'll keep this for backward compatibility
                let { data: issues, error: issuesErr } = await supabase.from('xfer_issues').select('*');
                setIssuesData(issues!);
                setIssuesError(issuesErr!);

                // Get flag from the xfer_flags_v2 table
                let { data: flagV2, error: flagErrV2 } = await supabase
                    .from('xfer_flags_v2')
                    .select('*')
                    .eq("id", +id)
                    .single();

                if (flagV2) {
                    // First convert to unknown, then to FlagTypeV2 to avoid type errors
                    // This is safe because ensureModelParsed will properly parse the model
                    const parsedFlag = ensureModelParsed(flagV2 as unknown as FlagTypeV2);
                    setFlagData(parsedFlag);
                    setFlagError(null);
                } else {
                    setFlagError(flagErrV2);
                    if(flagErrV2) {
                        toast({title: "Error", description: "Flag not found", variant: "destructive"})
                    }
                }
            } catch (error) {
                console.error(error);
                toast({title: "Error", description: "An error occurred while fetching data", variant: "destructive"})
            }
        }
        fetchData();
    }, [id, supabase]);

    useEffect(() => { if(open !== null && open === false) router.back()},[open]);

    const issueMap = issuesData && createIssueMap(issuesData);

    return issueMap && flagData && (
        <SimpleModal>
                <FlagExpandedDetail flag={flagData} issueMap={issueMap}
                                    admin={auth.admin}/>
        </SimpleModal>
    );
}
