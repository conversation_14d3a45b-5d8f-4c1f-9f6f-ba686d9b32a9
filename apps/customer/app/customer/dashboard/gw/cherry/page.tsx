"use client";

import React, { useEffect } from 'react'
import { CherryTypeV2 } from '@/types'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useAuth } from '@/components/context/auth/auth-context'
import SelectiveHighlightingList from './selective-highlighting-list'
import { EkoPageTitle } from '@/components/page-title'
import { GlassCard } from '@/components/ui/glass-card'
import { Skeleton } from '@ui/components/ui/skeleton'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();

    useEffect(() => {
        nav.changeNavPath([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Cherry Picking", href: "/customer/dashboard/gw/cherry" }
        ]);
    }, []);

    // Show loading state if cherry data is still loading
    if (entityContext.isLoadingCherry) {
        return (
            <div className="dashboard-container">
                <EkoPageTitle title="Cherry Picking" />
                <div className="space-y-6">
                    {/* Loading skeleton */}
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-8 w-64" />
                            <Skeleton className="h-4 w-32" />
                        </div>
                    </GlassCard>
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-3/4" />
                        </div>
                    </GlassCard>
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-6 w-56" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-2/3" />
                        </div>
                    </GlassCard>
                </div>
            </div>
        );
    }

    return entityContext.cherryData && entityContext.cherryData.length > 0 ? (
        <div className="dashboard-container">
            <EkoPageTitle title="Cherry Picking" />
            <SelectiveHighlightingList data={entityContext.cherryData} admin={auth.admin} />
        </div>
    ) : (
        <NoData title="No Cherry Picking Data Found" description="No cherry picking or flooding instances found for this entity" />
    );
}
