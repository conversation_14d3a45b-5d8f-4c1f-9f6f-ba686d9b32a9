"use client"
import React, {useEffect, useState} from 'react';
import {createClient} from "@/app/supabase/client";
import {useParams} from "next/navigation";
import {Database} from "@/database.types";
import {EkoMarkdown} from "@/components/markdown/eko-markdown";
import {useAuth} from "@/components/context/auth/auth-context";

type SingleDocType = Database["public"]["Tables"]["xfer_gw_single_doc"]["Row"];
export default function Page() {
    const supabase = createClient();
    const params= useParams();
    const id = params.id!;
    const {user, admin} = useAuth();
    const [singleDocAnalysis, setSingleDocAnalysis] = useState<any>();
    useEffect(() => {
        supabase.from("xfer_gw_single_doc").select("*").eq("id", +id).single().then(({data, error}) => {
            console.log(data, error)
            setSingleDocAnalysis(data?.analysis_json)
        });
    }, []);

    if (!singleDocAnalysis) return <div>Loading...</div>
    return (
        <div
            className="container mx-auto p-4 prose dark:prose-headings:text-foreground dark:prose-a:text-muted-foreground dark:text-foreground ">
            <h1>Single Doc Analysis</h1>
            <h2>Vague Terms</h2>
            {singleDocAnalysis.summary.summary}
            {/*<EkoMarkdown admin={admin} citations={singleDocAnalysis.summary.citations}>{singleDocAnalysis.summary.analysis}</EkoMarkdown>*/}
            <h2>Claim Verdicts</h2>
            {}
            {singleDocAnalysis.claim_verdicts.filter((claim: any) => claim.confidence > 50).map((claim: any) => (
                <div key={claim.id}>
                    <h3>{claim.claim.text}</h3>
                    <EkoMarkdown admin={admin} citations={claim.citations}>{claim.verdict}</EkoMarkdown>
                </div>
            ))}

            {singleDocAnalysis.vague_analyses.filter((analysis: any) => analysis.score > 50).map((analysis: any) => (
                <div key={analysis.id}>
                    <h3>{analysis.phrase}</h3>
                    <EkoMarkdown admin={admin} citations={analysis.citations}>{analysis.summary}</EkoMarkdown>
                </div>
            ))}

            <code>
                <pre>
                    {singleDocAnalysis && JSON.stringify(singleDocAnalysis, null, 2)}
                </pre>
            </code>
        </div>
    );
}
