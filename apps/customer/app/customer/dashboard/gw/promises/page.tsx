'use client'
import React, { useEffect, useState, useMemo } from 'react'
import { PromisesListV2 } from './promises-list'
import { EkoPageTitle } from '@/components/page-title'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/ui/select'
import { Badge } from '@ui/components/ui/badge'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();
    const [statusFilter, setStatusFilter] = useState<'all' | 'kept' | 'broken'>('all');
    const [sortBy, setSortBy] = useState<'date' | 'relevance' | 'status'>('relevance');

    useEffect(() => {
        nav.changeNavPath([{label: "Dashboard", href: "/customer/dashboard"}, {
            label: "Promises",
            href: "/customer/dashboard/gw/promises"
        }]);
    }, []);

    // Filter and sort promises data
    const filteredAndSortedPromises = useMemo(() => {
        if (!entityContext.promisesData) return [];

        let filtered = [...entityContext.promisesData];

        // Apply status filter
        if (statusFilter === 'kept') {
            filtered = filtered.filter(promise => promise.kept === true);
        } else if (statusFilter === 'broken') {
            filtered = filtered.filter(promise => promise.kept === false);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            if (sortBy === 'date') {
                const yearA = a.model.promise_doc_year || new Date().getFullYear();
                const yearB = b.model.promise_doc_year || new Date().getFullYear();
                return yearB - yearA; // Most recent first
            } else if (sortBy === 'status') {
                // Sort by kept status (kept first, then uncertain, then broken)
                if (a.kept === b.kept) return 0;
                if (a.kept === true) return -1;
                if (b.kept === true) return 1;
                if (a.kept === false) return 1;
                if (b.kept === false) return -1;
                return 0;
            } else {
                // Sort by relevance (confidence)
                return b.model.confidence - a.model.confidence;
            }
        });

        return filtered;
    }, [entityContext.promisesData, statusFilter, sortBy]);

    // Calculate counts for summary
    const keptCount = entityContext.promisesData?.filter(p => p.kept === true).length || 0;
    const brokenCount = entityContext.promisesData?.filter(p => p.kept === false).length || 0;
    const uncertainCount = entityContext.promisesData?.filter(p => p.kept === null).length || 0;

    // Show loading state while promises are being fetched
    if (entityContext.isLoadingPromises) {
        return <div className="container mx-auto p-4" data-testid="loading-promises">Loading promises...</div>;
    }

    return entityContext.promisesData && entityContext.promisesData.length > 0 ? (
        <div className="container mx-auto p-4" data-testid="promises-page-content">
            <EkoPageTitle title="Promises"/>
            
            {/* Filters Section */}
            <div className="mb-6 p-4 bg-muted/50 rounded-lg" data-testid="promises-filters">
                <div className="flex flex-wrap gap-4 items-center">
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">Status:</label>
                        <Select value={statusFilter} onValueChange={(value: 'all' | 'kept' | 'broken') => setStatusFilter(value)} data-testid="promise-status-filter">
                            <SelectTrigger className="w-[180px]">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all" data-testid="filter-option-all-promises">All Promises</SelectItem>
                                <SelectItem value="kept" data-testid="filter-option-kept-promises">Kept Promises</SelectItem>
                                <SelectItem value="broken" data-testid="filter-option-broken-promises">Broken Promises</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">Sort by:</label>
                        <Select value={sortBy} onValueChange={(value: 'date' | 'relevance' | 'status') => setSortBy(value)} data-testid="promises-sort">
                            <SelectTrigger className="w-[180px]">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="relevance" data-testid="sort-by-relevance">Relevance</SelectItem>
                                <SelectItem value="date" data-testid="sort-by-date">Date</SelectItem>
                                <SelectItem value="status" data-testid="sort-by-status">Status</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>

            {/* Summary Section */}
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-summary">
                <h3 className="text-lg font-semibold mb-3">Promise Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="text-3xl font-bold text-green-600 dark:text-green-400" data-testid="kept-promises-count">{keptCount}</div>
                        <div className="text-sm text-muted-foreground">Kept Promises</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div className="text-3xl font-bold text-red-600 dark:text-red-400" data-testid="broken-promises-count">{brokenCount}</div>
                        <div className="text-sm text-muted-foreground">Broken Promises</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{uncertainCount}</div>
                        <div className="text-sm text-muted-foreground">Uncertain</div>
                    </div>
                </div>
            </div>

            <PromisesListV2 promisesData={filteredAndSortedPromises} admin={auth.admin}/>
        </div>
    ) : (
        <div data-testid="promises-page-content">
            <NoData title="No Promises" description="No promises found for this entity" dataTestId="no-promises-message" />
        </div>
    )
}
