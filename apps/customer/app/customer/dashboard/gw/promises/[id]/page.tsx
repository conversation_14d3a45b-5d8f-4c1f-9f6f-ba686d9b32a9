"use client";

import { createClient } from '@/app/supabase/client'
import { PromiseCardV2 } from '../promise'
import { useEffect, useState } from 'react'
import { useAuth } from '@/components/context/auth/auth-context'
import { PromiseTypeV2 } from '@/types'
import { useParams } from 'next/navigation'
import { useNav } from '@/components/context/nav/nav-context'

export default function Page() {
    const [data, setData] = useState<PromiseTypeV2 | null>(null);
    const auth = useAuth();
    const id = useParams().id!;
    const nav = useNav();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            // Get promise from xfer_gw_promises_v2
            const {
                data: promiseV2Data,
                error: promiseV2Error
            } = await supabase
                .from("xfer_gw_promises_v2")
                .select("*")
                .eq("id", +id)
                .single();

            if (promiseV2Error) {
                console.error("Error fetching promise:", promiseV2Error);
                return;
            }

            if (promiseV2Data) {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setData(promiseV2Data as unknown as PromiseTypeV2);
                nav.changeTitle("Promise (" + promiseV2Data.id + ")");
                nav.changeNavPath([
                    {label: "Dashboard", href: "/customer/dashboard"},
                    {label: "Promises", href: "/customer/dashboard/gw/promises"},
                    {label: ""+promiseV2Data.id}
                ]);
            }
        };

        fetchData();
    }, [id]);

    return data ? <PromiseCardV2 item={data} admin={auth.admin}/> : <p>Loading...</p>;
}
