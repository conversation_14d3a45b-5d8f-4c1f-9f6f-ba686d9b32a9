import { PromiseTypeV2 } from '@/types'
import { Card, CardContent } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react'
import React from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { useEntity } from '@/components/context/entity/entity-context'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import Markdown from 'react-markdown'

const variants = {
    hidden: {opacity: 0, y: 20},
    visible: {opacity: 1, y: 0},
};


// New component that works directly with PromiseTypeV2
export function PromisesListV2({promisesData, admin}: { promisesData: PromiseTypeV2[] | undefined | null, admin: boolean }) {
    const {queryString} = useEntity();

    if (!promisesData || promisesData.length === 0) {
        return <p className="text-sm text-muted-foreground text-center py-4" data-testid="no-promises-message">No promises found</p>;
    }

    return <div data-testid="promises-list">
        {promisesData.map((item, j) => {
            const model = item.model;

            // Determine the promise status icon and color
            let statusIcon;
            let statusColor;
            let statusText;

            if (item.kept === true) {
                statusIcon = <CheckCircle2 className="w-5 h-5 inline-block"/>;
                statusColor = "bg-green-50 border-green-200 text-green-700 dark:bg-green-950 dark:border-green-800 dark:text-green-400";
                statusText = "Kept";
            } else if (item.kept === false) {
                statusIcon = <CircleXIcon className="w-5 h-5 inline-block"/>;
                statusColor = "bg-red-50 border-red-200 text-red-700 dark:bg-red-950 dark:border-red-800 dark:text-red-400";
                statusText = "Not Kept";
            } else {
                statusIcon = <HelpCircle className="w-5 h-5 inline-block"/>;
                statusColor = "bg-yellow-50 border-yellow-200 text-yellow-700 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-400";
                statusText = "Uncertain";
            }

            // Get the document year from the first citation or the model
            const firstCitation = model.citations && model.citations.length > 0 ? model.citations[0] : null;
            const docYear = firstCitation?.year || model.promise_doc_year || new Date().getFullYear();

            // Truncate text if too long
            const truncatedText = model.summary.length > 120 ? model.summary.substring(0, 90) + '...' : model.summary

            return (
                <motion.div
                    initial="hidden"
                    key={j}
                    exit="hidden"
                    whileInView="visible"
                    variants={variants}
                    viewport={{once: true, amount: 0.2}} // Triggers once, 20% in view
                    transition={{duration: 0.3, ease: 'easeOut', delay: 0.2}}
                    className="px-4"
                >
                    <Link href={"/customer/dashboard/gw/promises/" + item.id+"?"+queryString} passHref>
                        <Card className="mb-4 hover:shadow-md transition-shadow duration-200 overflow-hidden" data-testid="promise-item">
                            {/* Status indicator */}
                            <div className="w-full h-1.5 bg-slate-100 dark:bg-slate-800">
                                <div
                                    className={`h-full ${item.kept === true ? 'bg-green-500' : item.kept === false ? 'bg-red-500' : 'bg-yellow-500'}`}
                                    style={{width: `${model.confidence}%`}}
                                />
                            </div>

                            <CardContent className="pt-4 pb-4">
                                <div className="flex justify-between items-start mb-3">
                                    <Badge className={`${statusColor} flex items-center gap-1`} data-testid="promise-status">
                                        {statusIcon}
                                        {statusText}
                                    </Badge>
                                    <div className="flex items-center gap-2">
                                        <Badge variant="outline" data-testid="promise-date">{docYear}</Badge>
                                        {model.esg_promise && <Badge variant="secondary">ESG</Badge>}
                                    </div>
                                </div>

                                <div className="mb-3">
                                    <p className="font-medium text-lg" data-testid="promise-title">
                                        <Markdown children={model.statement_text || model.text || truncatedText} />
                                    </p>
                                </div>

                                <div className="flex flex-col gap-2">
                                    <div className="text-sm text-muted-foreground line-clamp-6" data-testid="promise-content">
                                        <EkoMarkdown
                                          citations={[]}
                                          admin={admin}
                                          skipCitations={true}
                                        >
                                        {model.summary}
                                        </EkoMarkdown>
                                    </div>

                                    <div className="flex justify-between items-center mt-2">
                                        <div className="text-xs text-muted-foreground">
                                            ID: {item.id}
                                        </div>
                                        <Badge variant="secondary" className="ml-auto" data-testid="promise-confidence">
                                            Confidence: {model.confidence}%
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </Link>
                </motion.div>
            );
        })}
    </div>;
}
