"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import ClaimDetailV2 from '../claim-detail-v2'
import { useNav } from '@/components/context/nav/nav-context'
import { runAsync } from '@utils/react-utils'
import { useAuth } from '@/components/context/auth/auth-context'
import { ClaimTypeV2 } from '@/types/claim'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const { id } = params;

    const supabase = createClient();
    const auth = useAuth();
    const nav = useNav();
    const [claim, setClaim] = useState<ClaimTypeV2 | null>(null);

    useEffect(() => {
        runAsync(async () => {
            if (id) {
                // Get claim from xfer_gw_claims_v2
                const {
                    data: claimV2Data,
                    error: claimV2Error
                } = await supabase
                    .from("xfer_gw_claims_v2")
                    .select("*")
                    .eq("id", +id)
                    .single();

                if (claimV2Error) {
                    console.error("Error fetching claim:", claimV2Error);
                    return;
                }

                if (claimV2Data) {
                    // Cast to unknown first to avoid TypeScript errors with Json type
                    setClaim(claimV2Data as unknown as ClaimTypeV2);
                    nav.changeTitle("Claim (" + claimV2Data.id + ")");

                    nav.changeNavPath([
                        { href: "/customer/dashboard", label: "Dashboard" },
                        { href: "/customer/dashboard/gw/claims", label: "Claims" },
                        { href: "/customer/dashboard/gw/claims/" + id, label: id }
                    ]);
                }
            }
        });
    }, [id]);

    if (!claim) {
        return null;
    }

    return (
        <div className="mt-4">
            <ClaimDetailV2 claim={claim} admin={auth.admin} />
        </div>
    );
}
