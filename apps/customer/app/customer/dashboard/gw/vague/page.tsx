"use client";
import React, { useEffect } from 'react'
import { VagueTermsList } from './vague-terms-list'
import NoData from '@/components/no-data'
import { useAuth } from '@/components/context/auth/auth-context'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { Summarize } from '@/components/summarize'

const example = `
# Analysis of Environmental Terms Usage in [Example Company] Corporate Communications
## Overview of Key Findings
A detailed examination of [Example Company]'s corporate communications reveals consistent patterns in the use of vague environmental terminology across multiple reports and statements. The analysis identified significant concerns with the company's usage of 14 key environmental terms, highlighting potential greenwashing through imprecise language and inadequate substantiation.

## Primary Areas of Concern
### Clean Energy and Environmental Claims
In "How we're working towards 100% renewable energy by 2030", [Example Company] uses terms like 'clean energy' without providing specific definitions or metrics. The company discusses various energy initiatives but fails to establish clear criteria for what constitutes "clean" energy or how it's verified.

### Manufacturing and Innovation Claims
The term "sustainable manufacturing" appears in [Example Company]'s Modern Slavery Statement 2023, primarily focusing on social governance rather than comprehensive sustainability measures. Similarly, their use of "responsible innovation" in the "[Example Company] Sustainability Report" lacks clear definition and verification mechanisms.

### Product and Material Claims
In their product communications, terms like 'biodegradable' and 'naturally derived' are used without specific certifications or standards. For instance, in the "Purpose-led, future-fit [Example Company] Integrated Annual Report 2021-22", claims about "energy efficient" equipment lack quantifiable metrics or industry standard references.

### Climate and Environmental Impact
The company's "Climate Transition Action Plan" acknowledges previous use of terms like 'climate neutral' in consumer-facing claims, noting a shift away from such terminology. This suggests growing awareness of the problematic nature of vague environmental claims.

## Systematic Issues Identified
### Lack of Verification

 * Consistent absence of third-party certification
 * Limited independent verification of claims
 * Insufficient documentation of environmental benefits

### Metric Deficiency

 * Few quantifiable measurements
 * Absence of specific performance indicators
 * Limited comparative data against industry standards

### Definition Gaps

 * Terms used without clear definitions
 * Ambiguous application of environmental terminology
 * Inconsistent usage across different communications


## Notable Improvements

In their recent communications, [Example Company] shows signs of recognizing these issues. The "[Example Company] Annual Report on Form 20-F 2021" indicates a shift toward more specific environmental claims and metrics, though significant room for improvement remains.

##Conclusion
The analysis suggests that while [Example Company] is making efforts to improve its environmental communications, significant work remains to avoid potential greenwashing through vague terminology. The company's recent acknowledgment of these issues and shifts in policy indicate awareness of the need for more precise and verifiable environmental claims.
`;

export default function Page() {
    const auth = useAuth();
    const entityContext = useEntity();
    const nav = useNav();

    useEffect(() => {
        nav.changeNavPath([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Vague Terms", href: "/customer/dashboard/gw/vague" }
        ]);
    }, []);

    // Show loading state if vague data is still loading
    if (entityContext.isLoadingVague) {
        return (
            <div className="prose-container">
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg">Loading vague terms...</div>
                </div>
            </div>
        );
    }

    // Check if we have vague data from EntityContext
    const vague = entityContext.vagueData;
    const detail = entityContext.vagueDetailData || [];

    if (vague === null || detail.length === 0) {
        return <NoData title="No Vague Terms" description="No vague terms found for this entity" />;
    }


    return (<div className="prose-container">
        <div>
            <div className="text-2xl font-bold my-4 ">Summary</div>
            <Summarize hashId={"v1.1:gw-vague-summary-long-" + entityContext.hash()}
                       preamble={"Please keep the summary to about 2000 words. Do not include recommendations. An example for [Example Company] is shown here, use this example as a guide to the format and style required: <example>"+example+"</example>\n\n"}
                       obj={{
                           claims: detail,
                           summary: vague ? vague.model.summary : "",
                           version:"1.0"
                       }}/>
        </div>
        <div>
            <div className="text-2xl font-bold my-4 ">Details</div>
            <VagueTermsList vagueDetailData={detail!} admin={auth.admin}/>
        </div>

    </div>)
        ;
}
