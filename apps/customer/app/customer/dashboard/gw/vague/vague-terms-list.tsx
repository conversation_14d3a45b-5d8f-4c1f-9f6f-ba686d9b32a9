"use client";
import { VagueType } from '@/types'
import { Badge } from '@ui/components/ui/badge'
import { Info } from 'lucide-react'
import React from 'react'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import { useRouter } from 'next/navigation'
import { useEntity } from '@/components/context/entity/entity-context'

export function VagueTermsList({vagueDetailData, admin}: { vagueDetailData: VagueType[] | undefined | null, admin: boolean }) {
    const router = useRouter();
    const {queryString} = useEntity();

    if (!vagueDetailData || vagueDetailData.length === 0) {
        return <p className="text-sm text-muted-foreground text-center py-4">No vague terms found</p>;
    }

    return <>
        {vagueDetailData.map((item) => (
            <div key={item.id} className="lg:p-2 lg:m-4 border-0 lg:border my-2 hover:cursor-pointer"
                 onClick={() => router.push("/customer/dashboard/gw/vague/" + item.id + "?" + queryString)}>
                <div className="flex items-center gap-2 mt-2 mb-2 text-sm">
                    <Badge variant="secondary">
                        <Info className="w-3 h-3 mr-1"/>
                        Score: {item.model.score}%
                    </Badge>
                    <span className="font-bold text-md">'{item.phrase}'</span>
                </div>
                <EkoMarkdown className="text-sm font-light" citations={item.model.citations as CitationType[]}
                             admin={admin}>{item.model.summary}</EkoMarkdown>
            </div>

        ))}
    </>;
}
