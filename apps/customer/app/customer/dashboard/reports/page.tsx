"use client";
import React, { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'

import { CitationType } from '@/components/citation'
import { useEntity } from '@/components/context/entity/entity-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'
import { Skeleton } from '@ui/components/ui/skeleton'
import { Button } from '@ui/components/ui/button'
import { useReactToPrint } from 'react-to-print'
import {
    StreamingHierarchicalReport as HierarchicalReport,
} from '@/components/report/streaming/streaming-hierarchical-report'
import { BackToTop } from '@/components/back-to-top'
import { getFlagType, processFlags } from '@/utils/flag-converter'
import { createClient } from '@/app/supabase/client'
import { toast } from 'sonner'
import { Edit3 } from 'lucide-react'

async function runAsync(f: () => Promise<void>) {
    await f();
}

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();
    const router = useRouter();
    const [isCreatingDocument, setIsCreatingDocument] = useState(false);

    const contentRef = useRef<HTMLDivElement>(null);
    const reactToPrintFn = useReactToPrint({contentRef: contentRef as any, bodyClass: "font-sans"});
    const supabase = createClient();

    useEffect(() => {
        nav.changeNavPath([
            {label: "Dashboard", href: "/customer/dashboard"},
            {label: "Reports", href: "/customer/dashboard/reports"}
        ]);
    }, []);

    // Show loading state if any data is still loading OR if we don't have basic entity info
    const isDataLoading = entityContext.isLoadingFlags || !entityContext.flagsData ||
      entityContext.isLoadingPromises || entityContext.isLoadingCherry ||
      entityContext.isLoadingClaims || entityContext.isLoadingModelSections ||
      entityContext.isLoadingScore;

    const hasBasicEntityInfo = entityContext.entity && entityContext.runObject && entityContext.entityData;

    if (isDataLoading || !hasBasicEntityInfo) {
        return (
          <div className="prose-container mt-8 xl:max-w-[800px] space-y-4 flex-col">
              <Skeleton className="h-8 w-[180px]" />
              <Skeleton className="h-4 w-[calc(100%_-_10px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_52px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_18px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_28px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_14px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_32px)]" />
              <Skeleton className="h-4 w-[calc(100%_-_40px)]" />
              <Skeleton className="h-4 w-[0px]" />
              {/* More skeleton elements... */}
          </div>
        )
    }

    // Collect all citations from different data sources
    const allCitations: CitationType[] = []

    // Add citations from flags - use entityContext.flagsData instead of data.flagsData
    // Process flags to ensure models are properly parsed
    const processedFlags = entityContext.flagsData ? processFlags(entityContext.flagsData) : []

    // Use getFlagType helper to ensure proper flag type extraction
    const greenFlags = processedFlags.filter(item => getFlagType(item) === 'green') || []
    const redFlags = processedFlags.filter(item => getFlagType(item) === 'red') || []

    if (redFlags) {
        allCitations.push(...redFlags.map((item) => item.model?.citations || []).flat())
    }
    if (greenFlags) {
        allCitations.push(...greenFlags.map((item) => item.model?.citations || []).flat())
    }

    // Add citations from cherry picking data
    if (entityContext.cherryData) {
        allCitations.push(...entityContext.cherryData.map((item) => item.model?.citations || []).flat())
    }

    // Add citations from claims data
    if (entityContext.claimsData) {
        allCitations.push(...entityContext.claimsData.map((item) => {
            if ('model' in item && item.model && typeof item.model === 'object') {
                return (item.model as any).citations || []
            } else {
                return (item as any).citations || []
            }
        }).flat())
    }

    // Add citations from promises data
    if (entityContext.promisesData) {
        allCitations.push(...entityContext.promisesData.map((item) => {
            if ('model' in item && item.model && typeof item.model === 'object') {
                return (item.model as any).citations || []
            } else {
                return (item as any).citations || []
            }
        }).flat())
    }

    // Add citations from vague data
    if (entityContext.vagueDetailData) {
        allCitations.push(...entityContext.vagueDetailData.map((item) => {
            if ('model' in item && item.model && typeof item.model === 'object') {
                return (item.model as any).citations || []
            } else {
                return (item as any).citations || []
            }
        }).flat())
    }

    if (entityContext.vagueData) {
        if ('model' in entityContext.vagueData && entityContext.vagueData.model && typeof entityContext.vagueData.model === 'object') {
            allCitations.push(...((entityContext.vagueData.model as any).citations || []) as CitationType[])
        } else {
            allCitations.push(...((entityContext.vagueData as any).citations || []) as CitationType[])
        }
    }

    const sortedAllCitations = allCitations.filter((i, index, self) =>
        index === self.findIndex((t) => (
          t.title === i.title && t.year === i.year
        )),
    ).sort((a, b) => a.title < b.title ? -1 : 1)

    // Function to create editable document from current report content
    const handleEditReport = async (reportContent: string) => {
        console.log('handleEditReport called with content length:', reportContent?.length)
        console.log('Auth user:', auth.user?.id)
        console.log('Entity:', entityContext.entity)
        console.log('Run object:', entityContext.runObject?.id)

        if (!auth.user || !entityContext.entity || !entityContext.runObject) {
            console.error('Missing required data:', {
                user: !!auth.user,
                entity: !!entityContext.entity,
                runObject: !!entityContext.runObject
            })
            toast.error('Unable to create document: missing required data')
            return
        }

        if (!reportContent || reportContent.trim().length === 0) {
            console.error('No report content available')
            toast.error('No report content available. Please wait for the report to finish generating.')
            return
        }

        try {
            console.log('Starting document creation...')
            setIsCreatingDocument(true)

            // Prepare report data
            const reportData = {
                entityName: entityContext.entityData?.name || entityContext.entity,
                entityDescription: entityContext.entityData?.description || '',
                modelName: entityContext.model!,
                runId: entityContext.runObject.id
            }

            // Create a new document in Supabase with the generated content
            const documentId = crypto.randomUUID()
            console.log('Creating document with ID:', documentId)
            console.log('User ID:', auth.user.id)
            console.log('Report content length:', reportContent.length)
            console.log('Citations count:', sortedAllCitations.length)

            const { data: document, error } = await supabase
                .from('collaborative_documents')
                .insert({
                    id: documentId,
                    title: `${reportData.entityName} ESG Report`,
                    initial_content: reportContent, // Store HTML content in initial_content
                    content: '', // Keep content empty initially
                    data: null, // TipTap JSON data will be stored here
                    created_by: auth.user.id,
                    metadata: {
                        type: 'report',
                        description: `ESG analysis report for ${reportData.entityName} using ${reportData.modelName} model`,
                        citations: sortedAllCitations.map(c => ({
                            doc_page_id: c.doc_page_id,
                            doc_id: c.doc_id,
                            title: c.title,
                            year: c.year,
                            url: c.url,
                            doc_name: c.doc_name
                        })),
                        reportData: {
                            entityName: reportData.entityName,
                            entityDescription: reportData.entityDescription,
                            modelName: reportData.modelName,
                            runId: reportData.runId
                        }
                    } as any
                })
                .select()
                .single()

            if (error) {
                throw error
            }

            toast.success('Document created successfully with report content')
            router.push(`/customer/documents/${documentId}`)

        } catch (error) {
            console.error('Error creating document:', error)
            console.error('Error details:', JSON.stringify(error, null, 2))
            if (error && typeof error === 'object' && 'message' in error) {
                toast.error(`Failed to create document: ${error.message}`)
            } else {
                toast.error('Failed to create document')
            }
        } finally {
            setIsCreatingDocument(false)
        }
    }

    // Add debug logging to help diagnose flag issues
    console.log("Before render - Flag counts:", {
        entityContextFlagsCount: entityContext.flagsData?.length || 0,
        redFlagsCount: redFlags.length,
        greenFlagsCount: greenFlags.length,
        isLoadingFlags: entityContext.isLoadingFlags
    });


    return entityContext.isLoading() ? <></> : (
        <>
            <div ref={contentRef}>
                <HierarchicalReport
                    entityName={entityContext.entityData?.name || entityContext.entity!}
                    entityDescription={entityContext.entityData?.description || ''}
                    modelName={entityContext.model!}
                    flags={[...redFlags, ...greenFlags]}
                    modelSections={entityContext.modelSectionsData || []}
                    claims={entityContext.claimsData?.filter((item) => item.verified) || []}
                    promises={entityContext.promisesData || []}
                    cherryData={entityContext.cherryData || []}
                    allCitations={sortedAllCitations}
                    runId={entityContext.runObject!.id}
                    onPrint={(e) => reactToPrintFn(e as any)}
                    onEditReport={handleEditReport}
                    isCreatingDocument={isCreatingDocument}
                />
            </div>

            <BackToTop />
        </>
    );
}
