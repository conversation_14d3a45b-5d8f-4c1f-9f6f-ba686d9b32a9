"use client";
import React from 'react'
import { EntityModelRunSelector } from '@/components/context/entity/emr-selector'


export default function Layout({children, modal}: { children: any, modal: any }) {

    const navPath = [
        {
            href: "/customer/dashboard/",
            label: "Dashboard",
        }
    ]

    return (
        <>
            <div className="relative dashboard-container flex flex-col justify-center">
                <EntityModelRunSelector navPath={navPath}/>
                {children}
            </div>
            {modal}
            <div id="simple-modal-root"></div>
        </>);
}
