import {
    BarChart2Icon,
    BookOpen,
    Building2Icon,
    CherryIcon,
    DonutIcon,
    FilePlusIcon,
    FileTextIcon,
    FlagIcon,
    Frame,
    GlobeIcon,
    LayoutDashboardIcon,
    LifeBuoy,
    List,
    LucideIcon,
    MapIcon,
    MessageCircleX,
    PieChart,
    Send,
    Unlink2Icon,
    VeganIcon,
} from 'lucide-react'
import { ffDocumentAnalysis } from '../feature-flags'

type NavigationItem = {
    title: string,
    url: string,
    icon?: LucideIcon,
    isActive?: boolean,
    items?: NavigationItem[]
    hidden?: boolean
    requires?: string // Feature flag name required to show this item
}

export const navigationTree:{[key:string]:NavigationItem[]} = {
    navMain: [
        {
            title: "Dashboard",
            url: "/customer/dashboard",
            icon: LayoutDashboardIcon,
            isActive: true,
            items: [
                {
                    title: "Dashboard",
                    url: "/customer/dashboard",
                    icon: LayoutDashboardIcon
                },

                {
                    title: "Flags",
                    url: "/customer/dashboard/flags",
                    icon: FlagIcon,
                    requires: "dashboard.flags"

                },
                {
                    title: "Cherry Picking",
                    url: "/customer/dashboard/gw/cherry",
                    icon: CherryIcon,
                    requires: "dashboard.greenwashing"

                },
                {
                    title: "Claims",
                    url: "/customer/dashboard/gw/claims",
                    icon: MessageCircleX,
                    requires: "dashboard.greenwashing"

                },
                {
                    title: "Promises",
                    url: "/customer/dashboard/gw/promises",
                    icon: Unlink2Icon,
                    requires: "dashboard.greenwashing"
                },
                // {
                //     title: 'Prediction',
                //     url: "/customer/dashboard/prediction-v2",
                //     icon: LineChartIcon
                // },
                // {
                //     title: "Vague Terms",
                //     url: "/customer/dashboard/gw/vague",
                //     icon: MessageSquareDashedIcon
                //
                // },
            ],
        },
        {
            title: 'Documents',
            url: '/customer/documents',
            icon: FileTextIcon,
            items: [{
                title: 'Create',
                url: '/customer/documents/new',
                icon: FilePlusIcon,
                items: [],
                requires: "document.create"

            },
                {
                    title: 'View',
                    url: '/customer/documents',
                    icon: List,
                    items: [],
                    requires: "document.view"

                }],

        },

        {
            title: "Analyse",
            url: "",
            icon: PieChart,
            items: [
                {
                    title: "Company Analysis",
                    url: "/customer/analysis/companies",
                    icon: Building2Icon,
                },
                {
                    title: "Document Analysis",
                    url: "/customer/analysis/documents",
                    icon: FileTextIcon,
                    hidden: !ffDocumentAnalysis(),
                },
                {
                    title: "Usage",
                    url: "/customer/analysis/usage",
                    icon: BarChart2Icon,
                },
            ],
        },
        {
            title: "Models",
            url: "#",
            icon: DonutIcon,
            items: [
                {
                    title: "Doughnut Economics",
                    url: "/customer/models/doughnut",
                    icon: DonutIcon,
                },
                {
                    title: "UN SDG",
                    url: "/customer/models/sdg",
                    icon: GlobeIcon,
                },
                {
                    title: "Plant Based Treaty",
                    url: "/customer/models/plant_based_treaty",
                    icon: VeganIcon
                },
            ],
        },
        {
            title: "Documentation",
            url: "#",
            icon: BookOpen,
            items: [
                {
                    title: "Introduction",
                    url: "#",
                },
                {
                    title: "Get Started",
                    url: "#",

                },
                {
                    title: "Tutorials",
                    url: "#",

                },
                {
                    title: "Changelog",
                    url: "#",

                },
            ],
        },
        // {
        //     title: "Settings",
        //     url: "#",
        //     icon: Settings2,
        //     items: [
        //         {
        //             title: "General",
        //             url: "#",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Team",
        //             url: "#",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Usage",
        //             url: "/customer/analysis/usage",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Limits",
        //             url: "#",
        //             icon: null
        //
        //         },
        //     ],
        // },
    ],
    navSecondary: [
        {
            title: "Support",
            url: "/customer/account/contact/support",
            icon: LifeBuoy,
        },
        {
            title: "Feedback",
            url: "/customer/account/contact/feedback",
            icon: Send,
        },
    ],
    navAccount: [
        {
            title: "Billing",
            url: "/customer/account/billing",
            icon: LifeBuoy,
        },
        {
            title: "Contact",
            url: "/customer/account/contact/contact",
            icon: Send,
        },
        {
            title: "Notifications",
            url: "/customer/account/notifications",
            icon: Send,
        },
    ],
    projects: [
        {
            title: "Design Engineering",
            url: "#",
            icon: Frame,
        },
        {
            title: "Sales & Marketing",
            url: "#",
            icon: PieChart,
        },
        {
            title: "Travel",
            url: "#",
            icon: MapIcon,
        },
    ],
}

export const navigationMap: Map<string, NavigationItem> = new Map<string, NavigationItem>();
navigationTree.navMain.flatMap(item=>item.items).filter(i=>!!i).forEach(item => navigationMap.set(item!.title, item!));
navigationTree.navSecondary.forEach(item => navigationMap.set(item.title, item!));
navigationTree.navAccount.forEach(item => navigationMap.set(item.title, item!));
navigationTree.projects.forEach(item => navigationMap.set(item.title, item!));
export const reverseNavigationMap: Map<string, NavigationItem> = new Map<string, NavigationItem>();
navigationTree.navMain.flatMap(item=>item.items).forEach(item => reverseNavigationMap.set(item!.url, item!));
navigationTree.navSecondary.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
navigationTree.navAccount.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
navigationTree.projects.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
