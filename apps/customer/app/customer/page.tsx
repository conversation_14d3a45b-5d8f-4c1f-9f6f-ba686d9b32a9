"use client";
import React, {useEffect, useState} from "react";
import {SimplePageHeader} from "@/components/simple-page-header";
import {Button} from "@ui/components/ui/button";
import {useAuth} from "@/components/context/auth/auth-context";
import Link from "next/link";
import {useToast} from "@ui/hooks/use-toast";
import {EntityAnalysesRunType, QuotaUsedType, SingleDocAnalysesType} from "@/types";
import {createClient} from "@/app/supabase/client";
import {runAsync} from "@utils/react-utils";
import {DocQuotaCard, EntityAnalysisQuotaCard, EntityQuotaCard} from "@/app/customer/analysis/quotas";
import {Card, CardContent, CardHeader, CardTitle} from "@ui/components/ui/card";
import {ffDocumentAnalysis} from "../feature-flags";
import {useRouter, useSearchParams} from "next/navigation";

export default function Page() {

    const [activeTab, setActiveTab] = useState("companies");
    const auth = useAuth();
    const {toast} = useToast();
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const supabase = createClient();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();


    async function updateCompanies() {
        const {data: analyses, error: docAnalysesError} = await supabase
            .from("view_entity_analysis_runs")
            .select("*")
            .eq("run_by", auth.user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: "destructive"});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

        const userId = auth.user?.id;
        if (!userId) return;
        const {data: analysisQuota, error: analysisQuotaError} = await supabase
            .from("view_quota_used")
            .select()
            .eq("profile_id", userId)
            .eq("type", "entity-analysis")
            .single();
        if (analysisQuotaError) {
            toast({description: analysisQuotaError.message, variant: "destructive"});
        } else {
            setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
        }

        const {data: companyQuota, error: companyQuotaError} = await supabase
            .from("view_quota_used")
            .select()
            .eq("profile_id", userId)
            .eq("type", "entity")
            .single();
        if (companyQuotaError) {
            toast({description: companyQuotaError.message, variant: "destructive"});
        } else {
            setEntityQuotaInfo(companyQuota as QuotaUsedType);
        }
    }

    async function updateRuns() {
        const userId = auth.user?.id!;
        const {data: analyses, error: docAnalysesError} = await supabase
            .from("view_single_doc_runs")
            .select("*")
            .eq("run_by", userId);

        const {data: quota, error: quotaError} = await supabase
            .from("view_quota_used")
            .select("*")
            .eq("profile_id", userId);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: "destructive"});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: "destructive"});
        } else {
            setQuotaInfo(quota.filter((i) => i.type === "document-analysis")[0] as QuotaUsedType);
        }
    }
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        runAsync(async () => {
            if(!searchParams.get("entity")) {
                await supabase.from('view_my_companies').select('*').then((response: any) => {
                    const params = new URLSearchParams();
                    params.append("entity", response.data[0].entity_xid);
                    router.push(`?${params.toString()}`);
                });
            }

        });
    }, [searchParams]);

    useEffect(() => {
        runAsync(async () => {
            if (!auth.user) return;
            await updateRuns();
            await updateCompanies();
        });
    }, [auth]);


    return (
        <>
            <SimplePageHeader>
                <h1 className="page-heading-title">Welcome</h1>
            </SimplePageHeader>
            <div className="min-h-screen  dashboard-container bg-muted flex flex-col">
                {/* Header */}
                <header className="bg-background shadow m-4">
                    <div className="w-full mx-auto py-6 px-4">
                        <h1 className="text-2xl font-semibold ">
                            Welcome Back {auth.profile?.full_name?.split(" ")[0] || "Valued Customer"}!
                        </h1>
                    </div>
                </header>

                {/* Main Content */}
                <main className="flex-grow mx-auto px-4 py-8 w-full">
                    <div className="flex flex-col lg:flex-row gap-6">
                        {/* Column 1 */}
                        <div className="flex flex-col space-y-6 w-full lg:w-1/3">
                            {/* Quick Actions */}
                            <Card>
                                <CardHeader><CardTitle>Quick Actions</CardTitle></CardHeader>
                                <CardContent>
                                    <ul className="mt-4 space-y-3">
                                        <li>
                                            <Link href={"/customer/dashboard"}>
                                                <Button variant="secondary" className="w-full">
                                                    View Dashboard
                                                </Button>
                                            </Link>
                                        </li>
                                        <li>
                                            <Link href={"/customer/analysis/companies"}>
                                                <Button variant="secondary" className="w-full">
                                                    Analyse a Company
                                                </Button>
                                            </Link>
                                        </li>
                                        {ffDocumentAnalysis() && (
                                            <li>
                                                <Link href={"/customer/analysis/documents"}>
                                                    <Button variant="secondary" className="w-full">
                                                        Analyse a Document
                                                    </Button>
                                                </Link>
                                            </li>)}
                                        <li>
                                            <Link href={"/customer/account"}>
                                                <Button variant="secondary" className="w-full">
                                                    View Account
                                                </Button>
                                            </Link>
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Onboarding or Resources */}
                            <Card className="">
                                <CardHeader><CardTitle>Resources</CardTitle>
                                </CardHeader>
                                <CardContent>

                                    <p className="mt-2 text-sm">
                                        Learn how to get more from ekoIntelligence.
                                    </p>
                                    <Link href={"/help"}>
                                        <Button variant="secondary" className="mt-4">
                                            Explore Resources
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Column 2 */}
                        <div className="flex flex-col space-y-6 w-full lg:w-2/3">
                            {quotaInfo && ffDocumentAnalysis() && <DocQuotaCard quotaInfo={quotaInfo}/>}
                            {entityAnalysisQuotaInfo && (
                                <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>
                            )}
                            {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
}
