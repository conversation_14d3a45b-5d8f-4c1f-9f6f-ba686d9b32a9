"use server"

export async function sendToSlack(message: string) {
    const webhookUrl = process.env.SLACK_WEBHOOK_URL|| "*********************************************************************************";
    if (!webhookUrl) {
        throw new Error("Slack webhook URL is not defined in environment variables");
    }

    const payload = {
        text: message,
    };

    const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
    });

    if (!response.ok) {
        const errorMessage = await response.text();
        throw new Error(`Slack webhook error: ${errorMessage}`);
    }

    return { success: true };
}
