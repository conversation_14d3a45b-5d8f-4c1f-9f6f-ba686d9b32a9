import {checkAuth} from "@/app/auth-utils";
import {Card, CardContent} from "@ui/components/ui/card";
import React from "react";
import ExpandableText from "@/components/text/expandable-text";
import {modelDesc} from "./model-desc";
import {PageHeader} from "@/components/page-header";

export default async function Page(props:{params: Promise<{model:string}>}) {
    const params = await props.params;

    const {
        model
    } = params;

    const redirect = await checkAuth(`/customer`);
    console.log(modelDesc[model]);
    return (<>
            <PageHeader  title={modelDesc[model].title}></PageHeader>
            <Card className="mt-8 col-span-4 row-span-2">
                <CardContent className="text-left mx-auto">
                    <ExpandableText image={modelDesc[model].image} title={modelDesc[model].title}
                                    text={modelDesc[model].text} expanded={true}/>
                </CardContent>
            </Card>
        </>

    );
}
