"use client"
import React, {useEffect, useRef, useState} from 'react';
import {<PERSON>, CardContent, CardHeader, CardTitle} from '@ui/components/ui/card';
import {Button} from '@ui/components/ui/button';
import {Input} from '@ui/components/ui/input';
import {Building2Icon, FilePieChartIcon, RabbitIcon} from 'lucide-react';
import {sendToSlack} from "@/app/customer/slack";
import {useToast} from "@ui/hooks/use-toast";
import {EntitySelector} from "@/components/entity-selector";
import {useSearchParams} from "next/navigation";
import {useAuth} from "@/components/context/auth/auth-context";
import {APIQueueType, EntityAnalysesRunType, QuotaUsedType} from "@/types";
import {createClient} from "@/app/supabase/client";
import {conciseDateTime} from "@utils/date-utils";
import {runAsync} from "@utils/react-utils";
import {backOfficeRequestListener, callBackofficeAsync} from "@/components/backoffice";
import {EntityAnalysisHistory} from "@/app/customer/analysis/entity-ana-history";
import {EntityAnalysisQuotaCard, EntityQuotaCard} from "@/app/customer/analysis/quotas";
import {Instructions} from "@/components/instructions";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@ui/components/ui/table";
import {PageHeader} from "@/components/page-header";
import {Badge} from '@ui/components/ui/badge';
import {cn} from '@utils/lib/utils';

const AnalysisDashboard = () => {
    const [activeTab, setActiveTab] = useState('companies');
    const [company, setCompany] = useState<string | undefined>();
    const {toast} = useToast();
    const queryParams = useSearchParams();
    const supabase = createClient();
    const [apiQueue, setApiQueue] = useState<APIQueueType[]>([]);
    const [listeners, setListeners] = useState<any[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();
    const auth = useAuth();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);


    async function updateCompanies() {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_entity_analysis_runs').select("*").eq("run_by", auth?.user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

    }


    async function addCompany() {
        let company = (ref.current as any).value;
        if (await sendToSlack("Please add new company " + company)) {
            toast({
                description: `Company ${company} added successfully, please now wait for the first analysis to complete, this can take several hours.`,
                variant: "default"
            })
        } else {
            toast({description: `Failed to add company ${company}`, variant: "destructive"})
        }


    }

    async function updateQueue(userId: string) {
        const {
            data: queue,
            error: queueError
        } = await supabase.from('api_queue').select().eq("requester", userId).eq("request_action", "analyse_entity").order("created_at", {ascending: false}).limit(10)
        setApiQueue(queue as APIQueueType[]);
    }

    useEffect(() => {
        if (auth) {

            listeners.forEach((listener) => listener.unsubscribe());
            setListeners([listenForQueueChange()]);

            runAsync(async () => {
                const userId = auth.user?.id;
                console.log(auth);
                if (!userId) return;
                await updateQueue(userId);
                const {
                    data: analysisQuota,
                    error: analysisQuotaError
                } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity-analysis").single();
                console.log(analysisQuota);
                if (analysisQuotaError) {
                    toast({description: analysisQuotaError.message, variant: 'destructive'});
                } else {
                    setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
                }
                const {
                    data: companyQuota,
                    error: companyQuotaError
                } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity").single();
                if (companyQuotaError) {
                    toast({description: companyQuotaError.message, variant: 'destructive'});
                } else {
                    setEntityQuotaInfo(companyQuota as QuotaUsedType);
                }
                await updateCompanies();
            });
        }
        return () => {
            listeners.forEach((listener) => listener.unsubscribe());
        }
    }, [auth]);


    const ref = useRef(null)


    function listenForQueueChange() {
        return backOfficeRequestListener(supabase, auth.user?.id!, null, async (status, payload, error, message) => {
            updateQueue(auth.user?.id!);
            if (!payload && !error && message) {
                // Just a status update
                return;
            }
            if (error || !payload) {
                toast({description: error.message, variant: "destructive"});
            } else if (payload.status === 'completed') {
                toast({description: `Entity analysis completed successfully`});
            } else if (payload.status === 'processing') {
                toast({description: "Processing entity now, this may take from a few minutes to a few hours"});
            }
        });
    }

    async function analyseEntity(entity: string, quick: boolean) {
        const id = await callBackofficeAsync(supabase, "analyse_entity", {
            entity,
            quick
        });
        await updateQueue(auth.user?.id!);
    }

    async function clickHandler(quick: boolean) {

        if (company) {
            analyseEntity(company, quick);

        } else {
            toast({description: "Please select a company", variant: "destructive"});
            return;
        }


    }

    async function retryHandler(id: number) {
        await supabase.from('api_queue').update({status: 'pending'}).eq('id', id);
        await updateQueue(auth.user?.id!);
    }

    function colorForStatus(status: string) {
        if (status === 'completed') {
            return 'border-green-500';
        } else if (status === 'processing') {
            return 'border-amber-500';
        } else if (status === 'failed') {
            return 'border-red-500';
        } else {
            return 'border-zinc-500';
        }
    }

    if (!auth.user) return null;

    return (
        <>
            <PageHeader/>
            <div className="p-6 space-y-6 dashboard-container">
                {/* Usage Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    {entityAnalysisQuotaInfo &&
                        <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>}
                    {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}


                </div>


                <Card>
                    <CardHeader>
                        <CardTitle>Add New Company</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            <Input ref={ref} placeholder="Enter company name" className="max-w-sm"/>
                            <Button onClick={addCompany} variant="default">
                                <Building2Icon className="h-4 w-4 mr-2"/>
                                Add Company
                            </Button>
                        </div>
                        <Instructions>
                            Please enter the name of the company you wish to add to your company list, the initial analysis can take up
                            to 48 hours to complete due to the thorough nature of the first analysis.
                        </Instructions>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle>Start Company Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap space-x-2 gap-2">
                            <EntitySelector defaultEntity={undefined}
                                            onChange={(value) => setCompany(value)}/>
                            <Button onClick={() => clickHandler(false)} variant="default">
                                <FilePieChartIcon className="h-4 w-4 mr-2"/>
                                Deep Analysis (Hours)
                            </Button>
                            <Button onClick={() => clickHandler(true)} variant="outline">
                                <RabbitIcon className="h-4 w-4 mr-2"/>
                                Quick Analysis (Minutes)
                            </Button>
                        </div>
                        <Instructions>
                            A full analysis will take between 12-48 hours, depending on the size of the company, this
                            will
                            involve searching the web for new documents and analysing them. A quick analysis will take
                            between 5-120 minutes and will only analyse existing documents in our store.
                        </Instructions>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Analysis Queue</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table className="w-full">
                                <TableHeader className="border-b">
                                    <TableRow>
                                    <TableHead
                                        className="px-4 py-2 text-left font-medium text-zinc-500">Entity</TableHead>
                                        <TableHead
                                            className="px-4 py-2 text-left font-medium text-zinc-500">Type</TableHead>
                                    <TableHead
                                        className="px-4 py-2 text-left font-medium text-zinc-500">Status</TableHead>
                                    <TableHead
                                        className="hidden md:table-cell px-4 py-2 text-left font-medium text-zinc-500">Message</TableHead>
                                    <TableHead
                                        className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Started</TableHead>
                                    <TableHead
                                        className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Updated</TableHead>
                                    <TableHead
                                        className="hidden sm:table-cell px-4 py-2 text-left font-medium text-zinc-500">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody className="divide-y">
                                    {apiQueue.map((analysis) => (
                                            <TableRow key={analysis.id} className="hover:bg-zinc-50">
                                                <TableCell
                                                    className="px-4 py-2">{(analysis.request_data as any)?.entity || 'Unknown'}</TableCell>
                                                <TableCell
                                                    className="px-4 py-2">{(analysis.request_data as any)?.quick ? 'Quick' : 'Deep'}</TableCell>
                                                <TableCell className="px-4 py-2"><Badge variant="outline" className={cn("border-opacity-80 font-light",colorForStatus(analysis.status!))}>{analysis.status}</Badge></TableCell>
                                                <TableCell className="hidden md:table-cell px-4 py-2">{analysis.message}</TableCell>
                                                <TableCell
                                                    className="hidden lg:table-cell px-4 py-2">{conciseDateTime(new Date(analysis.created_at!), Date.now(), navigator.language)}</TableCell>
                                                <TableCell
                                                    className="hidden lg:table-cell px-4 py-2">{conciseDateTime(new Date(analysis.updated_at!), Date.now(), navigator.language)}</TableCell>
                                                <TableCell className="hidden sm:table-cell px-4 py-2 text-right">
                                                    {analysis.status === 'failed' &&
                                                        (<Button onClick={() => retryHandler(analysis.id!)}
                                                                 variant="outline" size="sm">Retry</Button>)
                                                    }
                                                </TableCell>

                                            </TableRow>
                                        )
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
                <EntityAnalysisHistory entityAnalyses={entityAnalyses}/>

            </div>
        </>
    );


};

export default AnalysisDashboard;
