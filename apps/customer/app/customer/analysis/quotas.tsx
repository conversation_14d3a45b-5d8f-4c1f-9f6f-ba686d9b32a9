import {QuotaUsedType} from "@/types";
import {<PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle} from "@ui/components/ui/card";
import {Building2, FileText} from "lucide-react";
import React from "react";

export function EntityQuotaCard({entityQuotaInfo}: { entityQuotaInfo: QuotaUsedType }) {
    return <Card data-testid="entity-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Companies</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-blue-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{entityQuotaInfo.used ?? 0}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{entityQuotaInfo.quota}</span> companies
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-blue-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100.0}
                    style={{width: `${((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100.0}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}

export function DocQuotaCard({quotaInfo}: { quotaInfo: QuotaUsedType }) {
    return <Card data-testid="document-analysis-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Document Analysis Usage</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-green-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{quotaInfo.used}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{quotaInfo.quota}</span> analyses
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-green-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100}
                    style={{width: `${((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}

export function EntityAnalysisQuotaCard({entityAnalysisQuotaInfo}: { entityAnalysisQuotaInfo: QuotaUsedType }) {
    return <Card data-testid="entity-analysis-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Company Analysis Usage</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-blue-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{entityAnalysisQuotaInfo.used || 0}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{entityAnalysisQuotaInfo.quota}</span> analyses
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-blue-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100}
                    style={{width: `${((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}
