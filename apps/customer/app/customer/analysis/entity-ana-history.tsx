import { EntityAnalysesRunType } from '@/types'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { conciseDateTime } from '@utils/date-utils'
import { Button } from '@ui/components/ui/button'
import React from 'react'
import Link from 'next/link'
import { TableBody, TableCell, TableHead, TableHeader, TableRow } from '@ui/components/ui/table'

export function EntityAnalysisHistory({entityAnalyses}: { entityAnalyses: EntityAnalysesRunType[] }) {
    return <Card>
        <CardHeader>
            <CardTitle>Entity Analysis History</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="overflow-x-auto">
                <table className="w-full">
                    <TableHeader className="border-b">
                        <TableRow>
                            <TableHead className="px-4 py-2 text-left font-medium text-zinc-500">Name</TableHead>
                            <TableHead
                                className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Type</TableHead>
                            <TableHead
                                className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Date</TableHead>
                            <TableHead
                                className="hidden sm:table-cell px-4 py-2 text-right font-medium text-zinc-500">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y">
                        {entityAnalyses.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={4} className="text-center py-8">
                                    <div data-testid="no-company-analysis-message" className="text-gray-500">
                                        No company analyses found. Start by analyzing a company.
                                    </div>
                                </TableCell>
                            </TableRow>
                        ) : (
                            entityAnalyses.map((analysis) => (
                                <TableRow key={analysis.id} className="hover:bg-zinc-50" data-testid="company-analysis-entry">
                                    <TableCell className="px-4 py-2" data-testid="company-name">{analysis.name}</TableCell>
                                    <TableCell className="hidden lg:table-cell px-4 py-2">{analysis.type}</TableCell>
                                    <TableCell
                                        className="hidden lg:table-cell px-4 py-2" data-testid="analysis-date">{conciseDateTime(new Date(analysis.created_at!), Date.now(), navigator.language)}</TableCell>
                                    <TableCell className="hidden sm:table-cell px-4 py-2 text-right">
                                        <Link
                                            href={"/customer/dashboard?entity=" + analysis.entity_xid + "&run=" + analysis.run_id + "&model=sdg"}
                                            passHref>
                                            <Button variant="ghost" size="sm" data-testid="analysis-status">View Dashboard</Button>
                                        </Link>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </table>
            </div>
        </CardContent>
    </Card>;
}
