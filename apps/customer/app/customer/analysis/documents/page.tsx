"use client"
import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@ui/components/ui/card';
import {DocumentUpload} from "@/app/customer/files/document-upload";
import {createClient} from "@/app/supabase/client";
import {runAsync} from "@utils/react-utils";
import {useToast} from "@ui/hooks/use-toast";
import {QuotaUsedType, SingleDocAnalysesType} from "@/types";
import {useRouter} from "next/navigation";
import {DocumentAnalysisHistory} from "@/app/customer/analysis/doc-ana-history";
import {DocQuotaCard} from "@/app/customer/analysis/quotas";
import {PageHeader} from "@/components/page-header";

const AnalysisDashboard = () => {
    const supabase = createClient();
    const [activeTab, setActiveTab] = useState('companies');
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const [userId, setUserId] = useState<string>();
    const {toast} = useToast();
    const router = useRouter();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();


    async function updateRuns(userId: string) {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_single_doc_runs').select("*").eq("run_by", userId);

        const {
            data: quota,
            error: quotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "document-analysis").single();
        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: 'destructive'});
        } else {
            setQuotaInfo(quota as QuotaUsedType);
        }

    }

    useEffect(() => {
        runAsync(async () => {
            if (!userId) return;
            await updateRuns(userId);
        });

    }, [userId]);

    useEffect(() => {
        runAsync(async () => {
            setUserId((await supabase.auth.getUser())?.data?.user?.id!)
        });
    }, []);

    if (!quotaInfo) return null;

    return (
        <>
            <PageHeader/>

            <div className="p-6 space-y-6">

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


                    {quotaInfo && <DocQuotaCard quotaInfo={quotaInfo}/>}

                </div>

                <DocumentAnalysisHistory docAnalyses={docAnalyses}/>

                <Card className="mb-8">
                    <CardHeader className="flex items-center justify-between">
                        <CardTitle>Upload Document</CardTitle>
                        <CardDescription>Upload a document or specify a URL to have it analysed. Please also specify
                            which
                            company this applies to.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <DocumentUpload onComplete={(payload: any) => userId && updateRuns(userId)}/>
                    </CardContent>
                </Card>


            </div>
        </>
    );
};

export default AnalysisDashboard;
