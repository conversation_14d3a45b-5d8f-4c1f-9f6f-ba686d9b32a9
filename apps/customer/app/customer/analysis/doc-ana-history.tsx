import {SingleDocAnalysesType} from "@/types";
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON>} from "@ui/components/ui/card";
import {But<PERSON>} from "@ui/components/ui/button";
import React from "react";

export function DocumentAnalysisHistory({docAnalyses}: { docAnalyses: SingleDocAnalysesType[] }) {
    function onViewReport(analysis: SingleDocAnalysesType) {
        return window.location.href = ("/customer/dashboard/gw/doc/" + analysis.analysis_id + "?entity=" + analysis.entity_xid);
    }

    return <Card>
        <CardHeader>
            <CardTitle>Document Analysis History</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="border-b">
                    <tr className="bg-zinc-50">
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Name</th>
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Type</th>
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Date</th>
                        <th className="px-4 py-2 text-right font-medium text-zinc-500">Actions</th>
                    </tr>
                    </thead>
                    <tbody className="divide-y">
                    {docAnalyses.length === 0 ? (
                        <tr>
                            <td colSpan={4} className="text-center py-8">
                                <div data-testid="no-document-analysis-message" className="text-gray-500">
                                    No document analyses found. Start by analyzing a document.
                                </div>
                            </td>
                        </tr>
                    ) : (
                        docAnalyses.map((analysis) => (
                            <tr key={analysis.id} className="hover:bg-zinc-50" data-testid="document-analysis-entry">
                                <td className="px-4 py-2" data-testid="document-title">{analysis.name}</td>
                                <td className="px-4 py-2">{analysis.type}</td>
                                <td className="px-4 py-2" data-testid="analysis-date">{analysis.created_at}</td>
                                <td className="px-4 py-2 text-right">
                                    <Button onClick={() => onViewReport(analysis)} variant="ghost" size="sm" data-testid="analysis-status">View
                                        Report</Button>
                                </td>
                            </tr>
                        ))
                    )}
                    </tbody>
                </table>
            </div>
        </CardContent>
    </Card>;
}
