import Link from 'next/link'
import {<PERSON><PERSON>} from "@ui/components/ui/button"
import {AlertCircle} from 'lucide-react'

export default function NotFound() {
    return (
        <body>
        <div className="min-h-screen bg-background flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 text-center">
                <div className="space-y-2">
                    <AlertCircle className="mx-auto h-12 w-12 text-destructive" aria-hidden="true"/>
                    <h1 className="text-4xl font-bold tracking-tight">404</h1>
                    <p className="text-xl font-semibold text-muted-foreground">Page not found</p>
                </div>
                <div className="text-muted-foreground">
                    <p>Sorry, we couldn't find the page you're looking for.</p>
                    <p>Please check the URL in the address bar and try again.</p>
                </div>
                <div className="pt-4">
                    <Button asChild>
                        <Link href="/">
                            Go back home
                        </Link>
                    </Button>
                </div>
            </div>
        </div>
        </body>
    )
}
