'use client'

import * as Sentry from "@sentry/nextjs";
import {useEffect} from 'react'
import {Button} from "@ui/components/ui/button"
import {AlertOctagon} from 'lucide-react'

export default function GlobalError({
                                        error,
                                        reset,
                                    }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error(error)
        Sentry.captureException(error);
    }, [error])

    return (
        <html lang="en">
        <body className="bg-background">
        <div className="min-h-screen flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 text-center">
                <div className="space-y-2">
                    <AlertOctagon className="mx-auto h-12 w-12 text-destructive" aria-hidden="true" />
                    <h1 className="text-4xl font-bold tracking-tight">Critical Error</h1>
                    <p className="text-xl font-semibold text-muted-foreground">We're experiencing technical difficulties</p>
                </div>
                <div className="text-muted-foreground">
                    <p>We apologize for the inconvenience. Our team has been notified and is working on resolving the issue.</p>
                    {error.message && (
                        <p className="mt-2 p-2 bg-muted rounded-md text-sm">
                            Error details: {error.message}
                        </p>
                    )}
                    {error.digest && (
                        <p className="mt-2 text-sm text-muted-foreground">
                            Error ID: {error.digest}
                        </p>
                    )}
                </div>
                <div className="pt-4 space-y-4">
                    <Button onClick={() => reset()} variant="default">
                        Try again
                    </Button>
                    <div>
                        <Button asChild variant="outline">
                            <a href="/">Go back home</a>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        </body>
        </html>
    )
}
