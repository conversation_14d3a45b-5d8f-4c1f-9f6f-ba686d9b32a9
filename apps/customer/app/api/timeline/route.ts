import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { truncate } from '@utils/text-utils'
import { createClient } from '@/app/supabase/server'
import { AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
export const maxDuration = 180;
const caching = true;

export async function POST(request: Request) {
    const { preamble, obj, version, includeDisclosures } = await request.json();
    const supabase = await createClient();

    // Create a cache key using the preamble, object content, and includeDisclosures setting
    const cacheKey = `gemini-timeline-${crypto.createHash('md5').update(version + ":" + JSON.stringify(obj) + ":" + includeDisclosures).digest('hex')}`;

    // Check cache first
    const cachedData = await kv.hgetall(cacheKey);
    if (cachedData && caching) {
        console.log("cachedData", cachedData);
        return new Response(JSON.stringify({
            response: cachedData.timeline,
        }));
    }

    try {
        console.log("obj", obj);
        console.log("includeDisclosures setting:", includeDisclosures);
        let promptText = `You are a historian tasked with creating timelines from structured data. Given the input JSON below, generate a JSON array where each element represents an event with the following properties:
- \`year\`: The year of the event (from the input data).
- \`event\`: A brief title of the event.
- \`summary\`: A concise summary of the event.
- \`description\`: A detailed description of the event.
- \`source\`: The source type (\`flag\`, \`claim\`, \`promise\`, \`cherry\`, or \`other\`).
- \`source_id\`: The unique identifier of the source.

Ensure the response contains no additional text or metadata—just the JSON array. Do not infer or include information not present in the input data. Aim for between 6 and 12 events.

Here is the input JSON:
\`\`\`json
${JSON.stringify(obj, null, 2)}
\`\`\`

${preamble}`;

        promptText = truncate(promptText, 100000)!;

        // Generate content using the Gemini client directly
        const text = await generateReportContent({
            modelName: AI_MODEL_NAME,
            prompt: `You are a historian who generates accurate JSON timelines from structured data. Only respond with the JSON array.\n\n${promptText}`,
            endpoint: '/api/timeline',
            entityName: 'Timeline',
        });

        console.log("Received complete response");

        // Extract the JSON array from the response
        let responseContent = text.trim();

        // Try to extract JSON if it's wrapped in markdown code blocks
        if (responseContent.includes('```json')) {
            responseContent = responseContent.split('```json')[1].split('```')[0].trim();
        } else if (responseContent.includes('```')) {
            responseContent = responseContent.split('```')[1].split('```')[0].trim();
        }

        // Find the JSON array in the response
        const startIndex = responseContent.indexOf('[');
        const endIndex = responseContent.lastIndexOf(']') + 1;

        if (startIndex >= 0 && endIndex > startIndex) {
            responseContent = responseContent.substring(startIndex, endIndex);
        }

        // Ensure it's a valid JSON array
        if (!responseContent.startsWith('[') || !responseContent.endsWith(']')) {
            console.error('Response is not a valid JSON array:', responseContent);
            return new Response(
                JSON.stringify({ error: 'Failed to generate valid timeline' }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }

        try {
            const timeline = JSON.parse(responseContent);
            console.log("Filtered timeline:", timeline);

            // Cache the filtered timeline
            await kv.hset(cacheKey, { timeline: timeline });

            // Return the filtered timeline as JSON
            return new Response(
                JSON.stringify({ response: timeline }),
                { headers: { 'Content-Type': 'application/json' } }
            );
        } catch (error) {
            console.error('Error parsing or filtering timeline:', error);
            return new Response(
                JSON.stringify({ error: 'Failed to process timeline data' }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }
    } catch (error) {
        console.error('Error with Google Gemini API:', error);
        return new Response(
            JSON.stringify({ error: 'Failed to fetch response from Google Gemini' }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}
