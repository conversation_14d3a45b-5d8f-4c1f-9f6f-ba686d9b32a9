import OpenAI from 'openai';
import {NextResponse} from 'next/server';
import {kv} from "@vercel/kv";
import crypto from 'crypto';
import Anthropic from "@anthropic-ai/sdk";

export const maxDuration =180;

export async function POST(request: Request) {
    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    const anthropic = new Anthropic();
    const {sections} = await request.json();

    // Create a cache key using the preamble and object content
    const cacheKey = `14-${crypto.createHash('md5').update(JSON.stringify(sections)).digest('hex')}`;

    return await kv.hgetall(cacheKey).then(async (cachedData: any) => {
        if (cachedData) {
            return NextResponse.json({
                response: cachedData.response,
            });
        }


        try {
            const completion = await openai.chat.completions.create({
                model: 'gpt-4o',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a corporate ESG report writer, you are objective and factual but knowing you are criticising your clients, you remain diplomatic.'
                    },
                    {
                        role: 'user',
                        content: `
                        Please preserve citations and quotes from the source where possible. Example citation is: "Barclays has been profitting from global warming [^3468]".
                        Please join the following text together into a seamless single long report (aim for 2000 words), preserving the markdown formatting and citations and supplying the improved text as output without any commentary. 
                        Do not lose any factual detail from the report only remove repetition.
                         
                         <sections>
                        ${JSON.stringify(sections, null, 2)}
                        </sections>
                        `
                    }
                ],
            });

            const responseContent = completion.choices[0].message.content;


            await kv.hset(cacheKey, {response:responseContent!});

            return NextResponse.json({
                response: responseContent,
            });
        } catch (error) {
            console.error('Error with OpenAI API:', error);
            return NextResponse.json(
                {error: 'Failed to fetch response from OpenAI'},
                {status: 500}
            );
        }
    });
}
