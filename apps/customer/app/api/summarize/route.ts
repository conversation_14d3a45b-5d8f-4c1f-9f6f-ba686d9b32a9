import { streamText } from 'ai'
import { google } from '@ai-sdk/google'
import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { truncate } from '@utils/text-utils'

export const maxDuration = 180;
const caching = false;

export async function POST(request: Request) {
    const { preamble, keepCitations, obj, key } = JSON.parse((await request.json()).prompt);

    // Create a cache key using the preamble and object content
    const cacheKey = `gemini-flash-${key}-${crypto.createHash('md5').update(JSON.stringify({preamble, keepCitations, obj})).digest('hex')}`;

    // Check cache first
    const cachedData = await kv.hgetall(cacheKey);
    if (cachedData && caching) {
        return new Response(JSON.stringify({
            response: cachedData.response,
        }));
    }

    const citations = keepCitations
        ? "Please preserve source where possible. Example citation is: Citations look like: [^3468]. Please do not summarize them at the end, like footnotes, I will do that."
      : 'Please don\'t include the citations in the form of [^123]';

    try {
        console.log("obj", obj);
        let promptText = `
            <instructions>
            ${citations}
            ${preamble}
            <note>Remain upbeat but matter of fact, you are writing a report for a client.</note>
            </instructions>

            Please summarize in prose form, in markdown from the information contained within the <input> tags.
            Now please provide your response in markdown format but do not include an intro or preamble of any kind.

           <input>${JSON.stringify(obj, null, 2)}</input>

            Please summarize in prose form, in markdown from the information contained within the <input> tags.
            DO NOT include any additional information that is not present in the supplied text.
            DO NOT add any formatting or headings.

            ${preamble}
        `;

        promptText = truncate(promptText, 400000)!;

        // Use the AI SDK's streamText function with Gemini Flash Lite
        const result = await streamText({
            model: google('gemini-2.0-flash'),
            prompt: `You are a professional and highly paid report writer for a leading auditing firm specialising in ESG related matters, you are objective and factual but knowing you are criticising your clients, you remain diplomatic. You are not conversational.\n\n${promptText}`,
            temperature: 0.2,
            maxTokens: 4096,
            onFinish: async ({ text }) => {
                console.log("text", text);
                // Clean up the response and store in cache
                const cleanedResponse = text
                    .replace(/[\s\S]*<o>/g, '')
                    .replace(/<\/output>[\s\S]*/g, '')
                    .replace(/<\/o>[\s\S]*/g, '');

                await kv.hset(cacheKey, { response: cleanedResponse });
            },
            onChunk: async ({ chunk }) => {
                console.log("chunk", chunk);
            },
            onError: async ({ error }) => {
                console.error("error", error);
            }
        });

        // console.log("result", result);

        // Return the streaming response
        return result.toDataStreamResponse();
    } catch (error) {
        console.error('Error with Google Gemini API:', error);
        return new Response(
            JSON.stringify({ error: 'Failed to fetch response from Google Gemini' }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}
