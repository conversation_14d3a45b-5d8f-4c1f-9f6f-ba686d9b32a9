
// upload file to assistant's vector store

import {createClient} from "@/app/supabase/server";

export async function POST(req: Request) {

    const supabase = await createClient();
    const files: {filename:string, type:string, url:string}[] = (await req.json()).files;
    console.log(files);


    for (const file of files) {
        const filename = file.filename;

      //TODO: upload file

    }

    return new Response();
}
