// upload file to assistant's vector store
import {createClient} from "@/app/supabase/server";

export async function POST(req: Request) {

    const supabase = await createClient();

    //Get make to do the work of downloading the URL etc.
    const url = (await req.json()).url;
    fetch("https://hook.eu2.make.com/z6ntfiyrmpyd2bp5w76dsjgcnsoy439i", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({url}),
    });

    return new Response();
}
