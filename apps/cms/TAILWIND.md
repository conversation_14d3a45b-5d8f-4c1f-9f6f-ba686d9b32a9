# Tailwind CSS Customizations

This document outlines the tailwind customizations in the project, including utility classes, theme extensions, animations, and plugins.

## Project Structure

The tailwind configuration is organized as follows:

```
/apps/cms/
  tailwind.config.ts         # Main tailwind config
  /tailwind/
    /theme/
      index.mjs               # Theme configuration
      colors.mjs              # Color definitions
    /plugins/
      index.mjs               # Plugin registry
      shadows.mjs             # Shadow utilities
      glass-effects.mjs       # Glass effect utilities
      transitions.mjs         # Transition utilities
      spacing.mjs             # Spacing utilities
      typography.mjs          # Typography utilities
      rich-text.mjs           # Rich text styling
      borders.mjs             # Border utilities
      layout.mjs              # Layout utilities
```

## Core Customizations

### Theme Extensions

#### Colors

The project uses a comprehensive color system with:

- Standard color palette (slate, gray, zinc, etc.)
- Brand colors with light/dark variants
- Semantic colors (success, error, warning)
- HSL color variables for theme switching

```js
colors: {
  // Standard colors
  'slate': { '50': '#f7f7f7', '100': '#f0f0f0', /* ... */ },
  // ...

  // Brand colors
  'brand': {
    DEFAULT: 'hsl(145, 27%, 45%)',
    foreground: 'hsl(145, 27%, 45%)',
    background: 'hsl(144,27%,34%)',
    accent: 'hsl(4, 27%, 51%)',
    dark: {
      foreground: 'hsl(145, 27%, 30%)',
      background: 'hsl(145, 27%, 26%)',
    },
    light: 'hsl(145, 27%, 60%)',
  },

  // Theme colors (HSL variables)
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  border: 'hsl(var(--border))',
  // ...
}
```

#### Gradients

Custom background gradients for various UI elements:

```js
backgroundImage: {
  'brand-gradient': 'linear-gradient(197deg, hsl(145, 40%, 45%) 0%, hsl(144, 40%, 40%) 35%, hsl(144, 40%, 45%) 100%)',
  'brand-gradient-dark': 'linear-gradient(197deg, hsl(145, 40%, 25%) 0%, hsl(144, 40%, 20%) 35%, hsl(144, 40%, 25%) 100%)',
  'brand-gradient-compliment': 'linear-gradient(197deg, hsl(55, 55%, 56%) 0%, hsl(55, 55%, 60%) 35%, hsl(55, 55%, 58%) 100%)',
  // ...
}
```

#### Shadows

Consistent shadow system for depth and elevation:

```js
boxShadow: {
  'subtle': '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.03)',
  'soft': '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  'medium': '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.08)',
  'strong': '0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
}
```

#### Transitions

Standardized transitions for consistent animations:

```js
transitionProperty: {
  'standard': 'all',
},
transitionDuration: {
  'fast': '150ms',
  'standard': '250ms',
  'slow': '350ms',
  '1000': '1000ms',
  '1500': '1500ms',
  '2000': '2000ms',
},
transitionTimingFunction: {
  'standard': 'ease-in-out',
},
```

#### Container Sizes

Responsive container configuration:

```js
container: {
  center: true,
  padding: {
    '2xl': '2rem',
    DEFAULT: '1rem',
    lg: '2rem',
    md: '2rem',
    sm: '1rem',
    xl: '2rem',
  },
  screens: {
    '2xl': '86rem',
    lg: '64rem',
    md: '48rem',
    sm: '40rem',
    xl: '80rem',
  },
},
```

### Animations

The project includes custom animations for enhanced UI interactions:

```js
animation: {
  'accordion-down': 'accordion-down 0.2s ease-out',
  'accordion-up': 'accordion-up 0.2s ease-out',
  aurora: 'aurora 120s cubic-bezier(0.4, 0, 0.2, 1) infinite',
  'aurora-slow': 'aurora 240s cubic-bezier(0.4, 0, 0.2, 1) infinite',
  'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  wiggle: 'wiggle 0.4s ease-in-out 3',
  'float': 'float 6s ease-in-out infinite',
  'float-slow': 'float 8s ease-in-out infinite alternate',
  'scroll': 'scroll 25s linear infinite',
  'scroll-reverse': 'scroll-reverse 25s linear infinite',
},
keyframes: {
  'accordion-down': {
    from: { height: '0' },
    to: { height: 'var(--radix-accordion-content-height)' },
  },
  aurora: {
    '0%': {
      backgroundPosition: '0% 50%, 0% 50%',
      transform: 'translateZ(0)',
    },
    '50%': {
      backgroundPosition: '100% 50%, 100% 50%',
      transform: 'translateZ(0)',
    },
    '100%': {
      backgroundPosition: '200% 50%, 200% 50%',
      transform: 'translateZ(0)',
    },
  },
  wiggle: {
    '0%, 100%': { transform: 'rotate(-3deg) translateZ(0)' },
    '50%': { transform: 'rotate(3deg) translateZ(0)' },
  },
  float: {
    '0%, 100%': { transform: 'translate3d(0, 0, 0)' },
    '50%': { transform: 'translate3d(0, -10px, 0)' },
  },
  // ...
},
```

## Custom Plugins

### Glass Effect Utilities

Modern glass morphism effects with dark mode support:

```js
// In glass-effects.mjs
const glassEffectUtilities = {
  '.glass-effect': {
    background: 'rgba(230, 230, 230, 0.1)',
    backdropFilter: 'blur(12px)',
    borderBottom: '1px solid rgba(150, 150, 150, 0.3)',
    borderLeft: '1px solid rgba(150, 150, 150, 0.3)',
    borderTop: '1px solid rgba(240, 240, 240, 0.2)',
    borderRight: '1px solid rgba(240, 240, 240, 0.2)',
    filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.1))',
    overflow: 'visible'
  },
  '.glass-effect-subtle': { /* ... */ },
  '.glass-effect-strong': { /* ... */ },
  '.glass-effect-brand': { /* ... */ },
  '.glass-effect-brand-strong': { /* ... */ },
  '.glass-effect-brand-alt-strong': { /* ... */ },
  '[data-theme="dark"] .glass-effect': { /* ... */ },
  // ...
}
```

### Transition Utilities

Interactive hover and transition effects:

```js
// In transitions.mjs
const transitionUtilities = {
  '.transition-fast': {
    transition: 'all 150ms ease-in-out',
  },
  '.transition-standard': {
    transition: 'all 250ms ease-in-out',
  },
  '.transition-slow': {
    transition: 'all 350ms ease-in-out',
  },
  '.hover-lift': {
    transition: 'transform 250ms ease-in-out',
  },
  '.hover-lift:hover': {
    transform: 'translateY(-4px)',
  },
  '.hover-lift-subtle': { /* ... */ },
  '.hover-scale': { /* ... */ },
  '.hover-scale-subtle': { /* ... */ },
  // ...
}
```

### Spacing Utilities

Responsive spacing system for consistent layouts:

```js
// In spacing.mjs
const sectionSpacingUtilities = {
  '.section-spacing-small': {
    paddingTop: '2rem',
    paddingBottom: '2rem',
    '@media (min-width: 768px)': {
      paddingTop: '3rem',
      paddingBottom: '3rem',
    },
  },
  '.section-spacing-medium': { /* ... */ },
  '.section-spacing-large': { /* ... */ },
  // ...
}

const contentSpacingUtilities = {
  '.content-spacing-small': { /* ... */ },
  '.content-spacing-medium': { /* ... */ },
  '.content-spacing-large': { /* ... */ },
  // ...
}
```

### Typography Utilities

Comprehensive typography system with responsive sizing:

```js
// In typography.mjs
const headingStyles = {
  '.heading-1': {
    fontSize: '2.5rem',
    lineHeight: '1.2',
    fontWeight: '600',
    letterSpacing: '-0.015em',
    marginBottom: '0.5em',
    '@media (min-width: 768px)': {
      fontSize: '3.5rem',
    },
  },
  '.heading-2': { /* ... */ },
  '.heading-3': { /* ... */ },
  // ...
}

const textStyles = {
  '.text-lead': { /* ... */ },
  '.text-body': { /* ... */ },
  '.text-small': { /* ... */ },
  '.text-tiny': { /* ... */ },
  // ...
}

const textColors = {
  '.text-primary': {
    color: 'hsl(var(--brand-primary))',
  },
  '.text-muted': {
    color: 'hsl(var(--muted-foreground))',
  },
  // ...
}
```

### Rich Text Styling

Consistent styling for rich text content:

```js
// In rich-text.mjs
const richTextStyles = {
  '.rich-text h1': {
    fontSize: '2.5rem',
    lineHeight: '1.2',
    fontWeight: '600',
    letterSpacing: '-0.015em',
    marginBottom: '0.5em',
    '@media (min-width: 768px)': {
      fontSize: '3.5rem',
    },
  },
  '.rich-text h2': { /* ... */ },
  '.rich-text h3': { /* ... */ },
  '.rich-text p': { /* ... */ },
  '.rich-text ul': { /* ... */ },
  '.rich-text ol': { /* ... */ },
  '.rich-text blockquote': { /* ... */ },
  // ...
}
```

### Layout Utilities

Responsive grid and container utilities:

```js
// In layout.mjs
const gridUtilities = {
  '.grid-2-cols': {
    display: 'grid',
    gridTemplateColumns: '1fr',
    gap: '2rem',
    '@media (min-width: 640px)': {
      gridTemplateColumns: 'repeat(2, 1fr)',
    },
  },
  '.grid-3-cols': { /* ... */ },
  '.grid-4-cols': { /* ... */ },
  // ...
}

const containerUtilities = {
  '.container-small': {
    width: '100%',
    maxWidth: '640px',
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingLeft: '1rem',
    paddingRight: '1rem',
    '@media (min-width: 768px)': {
      paddingLeft: '2rem',
      paddingRight: '2rem',
    },
  },
  '.container-medium': { /* ... */ },
  '.container-large': { /* ... */ },
  '.container-xlarge': { /* ... */ },
  // ...
}
```

### Border Utilities

Consistent border styling:

```js
// In borders.mjs
const borderRadiusUtilities = {
  '.rounded-standard': {
    borderRadius: '1.5rem',
  },
  '.rounded-small': { /* ... */ },
  '.rounded-medium': { /* ... */ },
  '.rounded-large': { /* ... */ },
  // ...
}

const borderUtilities = {
  '.border-standard': { /* ... */ },
  '.border-subtle': { /* ... */ },
  '.border-strong': { /* ... */ },
  '.border-primary': { /* ... */ },
  // ...
}
```

## Additional Features

### Color Variables

The project automatically converts all Tailwind colors to CSS variables:

```js
// This plugin adds each Tailwind color as a global CSS variable, e.g. var(--gray-200).
function addVariablesForColors({ addBase, theme }) {
  let allColors = flattenColorPalette(theme('colors'))
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val]),
  )

  addBase({
    ':root': newVars,
  })
}
```

### Safelist

The project includes an extensive safelist to ensure dynamic classes are included in the build:

```js
safelist: [
  'lg:col-span-4',
  'lg:col-span-6',
  'lg:col-span-8',
  'lg:col-span-12',
  'border-border',
  'bg-card',
  'border-error',
  'bg-error/30',
  // ... many more classes
]
```

### Dark Mode

The project supports dark mode using a data attribute:

```js
darkMode: ['selector', '[data-theme="dark"]'],
```

## Usage Examples

### Glass Effect with Hover Animation

```jsx
<div className="glass-effect shadow-medium hover-lift-subtle transition-standard">
  <h2 className="heading-2">Title</h2>
  <p className="text-body">Content</p>
</div>
```

### Responsive Grid Layout

```jsx
<div className="grid-3-cols section-spacing-medium">
  <div className="glass-effect-subtle rounded-medium p-6">
    <h3 className="heading-3">Card 1</h3>
  </div>
  <div className="glass-effect-subtle rounded-medium p-6">
    <h3 className="heading-3">Card 2</h3>
  </div>
  <div className="glass-effect-subtle rounded-medium p-6">
    <h3 className="heading-3">Card 3</h3>
  </div>
</div>
```

### Animation Example

```jsx
<div className="animate-float glass-effect-brand rounded-large p-8">
  <h2 className="heading-2">Animated Content</h2>
</div>
```

## Benefits

1. **Consistency**: All styling is defined in one place with a structured approach
2. **Better IDE Support**: Tailwind IntelliSense works better with configured utilities
3. **Performance**: Reduces CSS bundle size by eliminating duplicate styles
4. **Maintainability**: Easier to update and maintain styles through modular plugins
5. **Documentation**: Better self-documentation through the configuration
6. **Dark Mode Support**: Built-in support for light and dark themes
7. **Responsive Design**: Consistent responsive behavior across components
