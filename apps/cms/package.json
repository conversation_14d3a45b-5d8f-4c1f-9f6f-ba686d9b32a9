{"name": "eko-cms", "version": "1.0.0", "description": "ekoIntelligence CMS built on Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@lexical/react": "^0.30.0", "@lexical/table": "^0.30.0", "@payloadcms/db-postgres": "latest", "@payloadcms/live-preview-react": "latest", "@payloadcms/next": "latest", "@payloadcms/payload-cloud": "latest", "@payloadcms/plugin-form-builder": "latest", "@payloadcms/plugin-nested-docs": "latest", "@payloadcms/plugin-redirects": "latest", "@payloadcms/plugin-search": "latest", "@payloadcms/plugin-seo": "latest", "@payloadcms/richtext-lexical": "latest", "@payloadcms/storage-s3": "latest", "@payloadcms/ui": "latest", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@repo/ui": "workspace:*", "@repo/utils": "workspace:*", "@tailwindcss/typography": "^0.5.16", "@types/d3": "^7.4.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "d3": "^7.9.0", "d3plus-react": "^1.3.3", "d3plus-text": "^1.2.5", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "geist": "^1.3.1", "graphql": "^16.11.0", "lucide-react": "^0.378.0", "motion": "^12.9.2", "next": "^15.3.1", "next-plausible": "^3.12.4", "next-sitemap": "^4.2.3", "payload": "latest", "payload-admin-bar": "^1.0.7", "pptxgenjs": "^4.0.0", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "7.45.4", "recharts": "^2.15.3", "sharp": "0.32.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-textshadow": "^2.1.3", "usehooks-ts": "^3.1.1", "webpack": "^5.99.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/typography": "^0.5.13", "@types/escape-html": "^1.0.4", "@types/node": "^22.15.3", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "autoprefixer": "^10.4.21", "copyfiles": "^2.4.1", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "5.7.3"}, "engines": {"node": "22.x"}, "overrides": {"react-is": "^19.1.0"}}