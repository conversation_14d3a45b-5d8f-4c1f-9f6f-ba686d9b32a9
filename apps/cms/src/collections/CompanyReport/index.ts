// src/collections/CompanyReport.ts
import { Block, CollectionConfig, Field } from 'payload'
import { authenticated } from '@/access/authenticated'
import { authenticatedOrPublished } from '@/access/authenticatedOrPublished'
import { generatePreviewPath } from '@/utilities/generatePreviewPath'
import { HighlightedText } from '@/blocks/HighlightedText/config'
import { FormBlock } from '@/blocks/Form/config'

// Define your reusable field groups (EntityData and Reference) and block configurations
// (Copy over the code for EntityDataFields, ReferenceFields, and each block below)

const EntityDataFields: Field[] = [
  {
    name: 'short_id',
    type: 'text',
    required: true,
  },
  {
    name: 'names',
    type: 'array',
    fields: [{ name: 'name', type: 'text' }],
  },
  {
    name: 'name',
    type: 'text',
  },
  {
    name: 'cn',
    type: 'text',
  },
  {
    name: 'description',
    type: 'textarea',
  },
  {
    name: 'domains',
    type: 'array',
    fields: [{ name: 'url', type: 'text' }],
  },
  {
    name: 'url',
    type: 'text',
  },
  {
    name: 'eko_id',
    type: 'text',
    admin: { hidden: true },
  },
]

const ReferenceFields: Field[] = [
  {
    name: 'title',
    type: 'text',
  },
  {
    name: 'url',
    type: 'text',
  },
  {
    name: 'publish_year',
    type: 'number',
  },
  {
    name: 'publish_date',
    type: 'text',
  },
  {
    name: 'authors',
    type: 'array',
    fields: EntityDataFields,
  },
  {
    name: 'pages',
    type: 'array',
    fields: [{ name: 'page', type: 'number' }],
  },
  {
    name: 'type',
    type: 'select',
    options: [
      { label: 'XXXX', value: 'web_page' },
      { label: 'HTML', value: 'html' },
      { label: 'PDF', value: 'pdf' },
    ],
  },
  {
    name: 'extract',
    type: 'textarea',
  },
  {
    name: 'doc_page_id',
    type: 'text',
    admin: { hidden: true },
  },
]

// Define your blocks
const CompanyProfileBlock: Block = {
  slug: 'cr-profile',
  labels: {
    singular: 'Company Profile',
    plural: 'Company Profiles',
  },
  fields: [
    {
      name: 'company_data',
      type: 'group',
      fields: EntityDataFields,
    },
  ],
}

const CompanyOverviewGraphsBlock: Block = {
  slug: 'cr-overview-graphs',
  labels: {
    singular: 'Company Overview Graphs',
    plural: 'Company Overview Graphs',
  },
  fields: [
    { name: 'version', type: 'text' },
    { name: 'data', type: 'json' },
  ],
}

const CompanyGraphBlock: Block = {
  slug: 'cr-graph',
  interfaceName: 'ICompanyGraphBlock',
  labels: {
    singular: 'Company Graph',
    plural: 'Company Graphs',
  },
  fields: [
    { name: 'title', type: 'text', required: true },
    { name: 'graph', type: 'select', required: true, options: [{ label: 'Bar', value: 'bar' }] },
    { name: 'data', type: 'json', required: true },
  ],
}

const CompanyAnalysisBlock: Block = {
  slug: 'cr-analysis',
  interfaceName: 'ICompanyAnalysisBlock',
  labels: {
    singular: 'Company Analysis',
    plural: 'Company Analyses',
  },
  fields: [
    { name: 'analysis', type: 'richText' },
    {
      name: 'visibility',
      type: 'select',
      options: [{ label: 'Public', value: 'public' }, { label: 'Signed In', value: 'auth' }, { label: 'Email Supplied', value: 'email' },  {
        label: 'Paid Users',
        value: 'paid',
      }],
    },
    {
      name: 'refs',
      type: 'array',
      fields: ReferenceFields,
    },

  ],
}

const CompanyReportCTABlock: Block = {
  slug: 'cr-cta',
  labels: {
    singular: 'Company Report CTA',
    plural: 'Company Report CTAs',
  },
  fields: [
    { name: 'title', type: 'text' },
    { name: 'description', type: 'text' },
    { name: 'cta', type: 'text' },
  ],
}

// Define the CompanyReport collection using the blocks field
export const CompanyReport: CollectionConfig = {
  slug: 'company-reports',
  labels: {
    singular: 'Company Report',
    plural: 'Company Reports',
  },
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  // Enable drafts by turning on versions with drafts
  versions: {
    drafts: true,
  },
  admin: {
    // Configure Live Preview for the collection:
    livePreview: {
      url: ({ data, req }) => {
        const path = generatePreviewPath({
          slug: typeof data?.slug === 'string' ? data.slug : '',
          collection: 'company-reports',
          req,
        })

        return path
      },
    },
    preview: (data, { req }) =>
      generatePreviewPath({
        slug: typeof data?.slug === 'string' ? data.slug : '',
        collection: 'company-reports',
        req,
      }),
  },
  fields: [
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'title',
      type: 'text',
      required: false,
    },
    {
      name: 'extract',
      type: 'textarea',
      required: false,
    },
    {
      name: 'blocks',
      type: 'blocks',
      blocks: [
        CompanyProfileBlock,
        CompanyOverviewGraphsBlock,
        CompanyGraphBlock,
        CompanyAnalysisBlock,
        CompanyReportCTABlock,
        HighlightedText,
        FormBlock
      ],
    },
  ],
}

export default CompanyReport
