import { Form, FormSubmission } from '@payloadcms/plugin-form-builder/types'

// Extend FormSubmission with the additional properties we need
interface ExtendedFormSubmission extends FormSubmission {
  id?: string;
  createdAt?: string | Date;
}

/**
 * Formats and sends form submission data to Slack
 * @param formSubmission The form submission data
 * @param form The form definition
 */
export const sendFormSubmissionToSlack = async (
  formSubmission: ExtendedFormSubmission,
  form: Form,
): Promise<void> => {
  try {
    const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL
    if (!SLACK_WEBHOOK_URL) {
      console.warn('Form submission notification failed: SLACK_WEBHOOK_URL is not defined')
      return
    }

    const formFields = formSubmission.submissionData.map((field) => {
      return {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${field.field}*: ${field.value}`,
        },
      }
    })

    const message = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `📝 New Form Submission: ${form.title}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `A new form submission was received at ${formSubmission.createdAt 
              ? new Date(formSubmission.createdAt).toLocaleString() 
              : new Date().toLocaleString()}`,
          },
        },
        { type: 'divider' },
        ...formFields,
        { type: 'divider' },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: formSubmission.id 
                ? `View details at: https://ekointel.com/admin/collections/form-submissions/${formSubmission.id}`
                : 'Form submission details not available',
            },
          ],
        },
      ],
    }

    const response = await fetch(SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    })

    if (!response.ok) {
      console.error(`Failed to send Slack notification: ${response.statusText}`)
    }
  } catch (error) {
    console.error('Error sending form submission to Slack:', error)
  }
}
