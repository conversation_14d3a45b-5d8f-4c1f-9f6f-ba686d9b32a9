import type { Block } from 'payload'
import { FixedToolbarFeature, HeadingFeature, InlineToolbarFeature, lexicalEditor } from '@payloadcms/richtext-lexical'

export const PricingTable: Block = {
  slug: 'pricing',
  interfaceName: 'IPricingBlock',
  // imageURL: '/images/blocks/pricing-table.png', // optional thumbnail for the admin UI
  labels: {
    singular: 'Pricing Table',
    plural: 'Pricing Tables',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'altcolor',
      type: 'checkbox',
      label: 'Alternate Color Scheme',
      defaultValue: false,
    },
    {
      name: 'title',
      type: 'text',
      label: 'Table Title',
      required: true,
      admin: {
        description: 'Heading for the pricing table (e.g. “Our Plans”)',
      },
    },
    {
      name: 'intro',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
          ]
        },
      }),
      label: 'Intro Content',
    },
    {
      name: 'pricingOptions',
      type: 'array',
      label: 'Pricing Options',
      admin: {
        description: 'Add one entry for each pricing plan you wish to offer.',
      },
      fields: [
        {
          name: 'planName',
          type: 'text',
          required: true,
          label: 'Plan Name',
          admin: {
            description: 'Name of the plan (e.g. Basic, Pro, Enterprise)',
          },
        },
        {
          name: 'subTitle',
          type: 'text',
          required: false,
          label: 'Sub Title',
          admin: {
            description: 'Text to go under the plan name',
          },
        },
        {
          name: 'price',
          type: 'number',
          required: true,
          label: 'Price',
          admin: {
            description: 'Enter the numeric value for the plan’s price',
          },
        },
        {
          name: 'pricePeriod',
          type: 'text',
          label: 'Price Period',
          defaultValue: '/month',
          admin: {
            description: 'Price period suffix (e.g. /month, /year)',
          },
        },
        {
          name: 'features',
          type: 'array',
          label: 'Features',
          admin: {
            description: 'List the features included with this plan',
          },
          fields: [
            {
              name: 'feature',
              type: 'text',
              required: true,
              label: 'Feature',
            },
          ],
        },
        {
          name: 'buttonText',
          type: 'text',
          required: true,
          label: 'Button Text',
          defaultValue: 'Get Started',
        },
        {
          name: 'buttonLink',
          type: 'text',
          required: true,
          label: 'Button Link',
          admin: {
            description: 'URL to which the call-to-action button should point',
          },
        },
        {
          name: 'isRecommended',
          type: 'checkbox',
          label: 'Recommended Plan',
          defaultValue: false,
          admin: {
            description: 'Mark this plan as the recommended option',
          },
        },
      ],
    },
  ],
};
