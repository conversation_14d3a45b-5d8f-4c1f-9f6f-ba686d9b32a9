'use client'
import { motion } from 'framer-motion'
import React from 'react'
import { cn } from '@utils/lib/utils' // Assuming this path is correct

// Define a more specific type for the option prop if possible
// interface PricingOptionData {
//   id: string | number;
//   planName: string;
//   subTitle: string;
//   price: number;
//   pricePeriod?: string;
//   features?: { id?: string | number; feature: string }[];
//   buttonText: string;
//   buttonLink: string;
//   isRecommended?: boolean;
// }

export const PricingOption: React.FC<{
  option: any; // Replace 'any' with PricingOptionData if defined
  altcolor: boolean;
  index: number;
  total: number;
  isVisible?: boolean;
  isMobile?: boolean;
}> = ({
        option,
        altcolor,
        index,
        total,
        isVisible = false, // Default to false
        isMobile = false,
      }) => {
  // Ensure total is at least 1 to avoid division by zero or weird calculations
  const validTotal = Math.max(1, total);

  // Calculate the center position index for the stack
  const middleIndex = Math.floor((validTotal - 1) / 2);

  // Calculate horizontal offset for the fan-out effect
  // Using a slightly curved spacing for smoother distribution
  // This helps prevent the jerky ending by creating more natural final positions
  const calculateOffset = (distance: number) => {
    // Using a slight curve (less than exponential, more than linear)
    // This creates a more natural distribution of cards
    return Math.pow(distance, 1.05) * 416; // Very slight curve with moderate spacing
  };

  const targetX = (isVisible
    ? index === middleIndex
      ? 0 // Center card stays horizontally centered
      : index < middleIndex
        ? -calculateOffset(middleIndex - index) // Cards to the left
        : calculateOffset(index - middleIndex) // Cards to the right
    : 0) - 190; // Offset to adjust center position


  // Determine z-index: higher index = further back initially. Recommended gets a boost when visible.
  const initialZIndex = validTotal - index;
  const animatedZIndex = initialZIndex + (option.isRecommended && isVisible ? 10 : 0); // Boost recommended card when visible

  // Calculate the staggered animation delay based on the index
  // Using a gentler staggering for smoother overall animation
  // Also using absolute distance from middle for more natural expansion
  const distanceFromMiddle = Math.abs(index - middleIndex);
  // For smoother animation, use smaller increments between cards
  const animationDelay = 0.2 + distanceFromMiddle * 0.04; // Smaller delay increments for smoother staggering

  // Initial state (stacked in the center with no vertical offset)
  // Using even more minimal differences to reduce visual jerkiness
  const initialState = {
    x: -200, // All cards start at the same horizontal position
    y: 0, // All cards start at the same vertical position
    rotate: (index % 3 -1)*1.5, // No rotation initially to reduce jerkiness
    scale: 1, // No scale difference to prevent jerky scaling
    opacity: 1, // Full opacity for all cards
    zIndex: initialZIndex,
    boxShadow: `0 ${index * 1}px ${index * 1}px rgba(0, 0, 0, 0.05)`, // Very subtle shadow
  };

  // Animated state (fanned out horizontally)
  // Using even more subtle effects to reduce visual jerkiness
  const animateState = {
    x: targetX, // Spread cards horizontally based on targetX calculation
    y: option.isRecommended ? -3 : 0, // Very minimal lift for recommended card
    rotate: 0, // No rotation to reduce jerkiness
    scale: option.isRecommended ? 1.02 : 1, // Very minimal scale difference
    opacity: 1, // Fully opaque
    zIndex: animatedZIndex,
    boxShadow: option.isRecommended
      ? '0 10px 20px rgba(0, 0, 0, 0.1)' // Very subtle shadow for recommended
      : '0 5px 15px rgba(0, 0, 0, 0.08)', // Very subtle shadow for others
  };

  // --- Mobile Rendering ---
  if (isMobile) {
    return (
      <div
        className={cn(
          'transform-gpu w-full max-w-full overflow-visible standard-rounding shadow-lg', // Added shadow-lg for mobile clarity
          // Removed hover-scale for mobile as it's less relevant
            altcolor
              ? 'glass-effect-brand-alt-strong-lit text-black/80'
              : 'glass-effect-brand-strong-lit text-white',
          // Add margin if cards stack vertically on mobile
          'mb-4' // Example: Add margin-bottom for vertical stacking
        )}
        // Optionally add a simpler motion effect for mobile entrance if desired
        // initial={{ opacity: 0, y: 20 }}
        // animate={{ opacity: 1, y: 0 }}
        // transition={{ duration: 0.5 }}
      >
        {renderCardContent(option,altcolor)}
      </div>
    );
  }

  // --- Desktop Rendering with Animation ---
  return (
    <motion.div
      // Adding will-change to optimize animation performance
      style={{
        zIndex: initialZIndex, // Set initial zIndex directly
        willChange: 'transform', // Hint to browser to optimize these properties
        backfaceVisibility: 'hidden', // Prevent flickering in some browsers
        WebkitFontSmoothing: 'antialiased', // Smoother text rendering
      }}
      initial={initialState}
      animate={isVisible ? animateState : initialState} // Animate to 'animateState' only when isVisible is true
      transition={{
        duration: 0.3, // Even longer duration for smoother animation
        delay: animationDelay, // Keep staggered start time
      }}
      className={cn(
        'transform-gpu w-full h-[680px]  max-w-[24rem]', // Responsive max-width
        'overflow-visible standard-rounding hover-scale',
        'absolute left-1/2 ', // Position center point
        // Translate is handled by motion.div x/y props, no need for -translate classes

      )}
    >
      <div className={cn("absolute transform-gpu standard-rounding  h-[680px] inset-0", altcolor
          ? 'glass-effect-brand-alt-strong-lit'
          : 'glass-effect-brand-strong-lit')}>
      {renderCardContent(option, altcolor)}
      </div>
    </motion.div>
  );
};

// --- Card Content Component (no changes needed from yours) ---
const renderCardContent = (option: any, altcolor: boolean) => (
  <div className={cn("relative flex-grow h-full flex flex-col items-center justify-center", altcolor ? 'text-black/70' : 'text-white')}>
    {option.isRecommended && (
      <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 bg-brand-gradient-accent  text-xs font-bold px-6 py-3 rounded-b-lg shadow-md z-10">
        Recommended
      </div>
    )}
    <div className={cn("px-6 flex h-full flex-col flex-grow",option.isRecommended ? ' pt-6 ' : '')}>
      <div className="px-6 py-8">
        <h3 className="text-2xl font-semibold text-center">
          {option.planName}
        </h3>
        <h4 className="text-sm opacity-80 text-center">
          {option.subTitle}
        </h4>

        <p className="mt-4 text-4xl font-extrabold text-center">
          {option.price > 0 ? '£' + option.price : option.price === 0 ? 'Free' : 'Contact Us'}{' '}
          {option.pricePeriod || ''}
        </p>
      </div>
      <ul className="mt-4 space-y-4 flex-grow">
        {option.features?.map((f: any, index: number) => ( // Added index for key fallback
          <li key={f.id || f.feature || index} className="flex items-start"> {/* Improved key */}
            <svg
              className="flex-shrink-0 h-6 w-6 text-green-50" // Check color contrast if needed
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke={altcolor ? 'black' : 'white'}
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <span className="ml-3 text-base">
                {f.feature}
              </span>
          </li>
        ))}
      </ul>
      {/* Using mt-auto pushes the button to the bottom */}
      <div className="mt-auto m-4 pt-8">
        <a
          href={option.buttonLink}
          className={`w-full block text-center px-4 py-3 shadow-md standard-rounding text-sm font-medium hover:-translate-y-1 hover:shadow-2xl transition-transform duration-300 ${
            option.isRecommended
              ? 'bg-brand-gradient-accent hover:bg-neutral-900 text-white' // Ensure hover state exists for gradient
              : 'bg-white hover:bg-neutral-50 text-neutral-900'
          } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`} // Check focus ring color
        >
          {option.buttonText}
        </a>
      </div>
    </div>
  </div>
);

// --- Parent Component Setup (Crucial!) ---
/*
 You need a parent component to manage the layout and visibility.

 Example using react-intersection-observer:

 import { useInView } from 'react-intersection-observer';
 import { useState } from 'react'; // If needed for mobile detection

 const PricingSection = ({ pricingOptions }) => {
   const [isMobile, setIsMobile] = useState(false); // Add logic to detect mobile (e.g., window resize listener)

   const { ref, inView } = useInView({
     triggerOnce: true, // Animate only once when it enters view
     threshold: 0.2, // Trigger when 20% of the container is visible
   });

   // Give the container relative positioning and enough height
   return (
     <div ref={ref} className="relative w-full min-h-[500px] flex items-center justify-center py-16">
       {pricingOptions.map((option, index) => (
         <PricingOption
           key={option.id || index}
           option={option}
           altcolor={index % 2 !== 0} // Example alternating color logic
           index={index}
           total={pricingOptions.length}
           isVisible={inView} // Pass the visibility state to ALL cards
           isMobile={isMobile} // Pass mobile state
         />
       ))}
     </div>
   );
 };

*/
