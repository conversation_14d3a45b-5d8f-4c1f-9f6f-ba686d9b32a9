'use client';

import React, { useEffect, useRef, useState } from 'react'
import { IPricingBlock } from '@/payload-types' // Assuming these types are correct
import { PricingOption } from '@/blocks/PricingTable/PricingOption' // Adjust path if needed
import { Headline } from '@ui/components/front-page/headline' // Adjust path if needed
import RichText from '@/components/RichText' // Adjust path if needed
import { useIsClient } from '@/hooks/useIsClient' // Adjust path if needed

const PricingTableBlock: React.FC<IPricingBlock> = ({ title, pricingOptions, intro, altcolor }) => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const totalOptions = pricingOptions?.length || 0;
  const isClient = useIsClient();
  // Correctly initialize isMobile state
  const [isMobile, setIsMobile] = useState(false);

  // Effect for mobile detection - run only on client
  useEffect(() => {
    if (!isClient) return;

    const checkMobile = () => setIsMobile(window.innerWidth < 1280); // Example breakpoint: Tailwind's 'md'
    checkMobile(); // Initial check on mount
    window.addEventListener('resize', checkMobile);

    // Cleanup listener
    return () => window.removeEventListener('resize', checkMobile);
  }, [isClient]); // Re-run if isClient changes (after hydration)

  // Effect for Intersection Observer - run only on client
  useEffect(() => {
    // Ensure we're on client and ref is available
    if (!isClient || !sectionRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing once visible
        }
      },
      { threshold: 0.5 } // Trigger when just 10% of the element is visible for earlier animation
    );

    observer.observe(sectionRef.current);

    // Cleanup observer
    return () => {
      observer.disconnect();
    };
  }, [isClient]); // Dependency: ensures runs client-side

  // --- SSR Placeholder ---
  // Render a placeholder on the server or before hydration to prevent layout shifts/mismatches
  if (!isClient) {
    // Calculate approximate height for SSR placeholder
    const placeholderHeight = `${Math.max(650, totalOptions * 80)}px`;
    return (
      <div className="container mt-16 my-0 bg-transparent relative mx-auto px-4 sm:px-6 lg:px-8 pb-16 lg:pb-24">
        <a id="pricing"></a>
        {title && <Headline>{title}</Headline>}
        <div className="mt-4 mb-8 mx-auto prose max-w-prose"> {/* Constrained width */}
          {intro && <RichText className="intro-text" data={intro} enableGutter={false} />}
        </div>
        {/* Placeholder with calculated height */}
        <div style={{ minHeight: placeholderHeight }}></div>
      </div>
    );
  }

  // --- Client-side Render ---
  return (
    <div className="container mt-16 my-0 bg-transparent relative mx-auto px-4 sm:px-6 lg:px-8 pb-16 lg:pb-24">
      {/* Optional: Subtle noise texture */}
      {/* <div className="absolute inset-0 bg-noise opacity-[0.02] mix-blend-overlay pointer-events-none rounded-xl"></div> */}
      <a id="pricing"></a>
      {title && <Headline>{title}</Headline>}
      <div className="mt-4 mb-8 mx-auto prose max-w-prose"> {/* Constrained width */}
        {intro && <RichText className="intro-text" data={intro} enableGutter={false} />}
      </div>

      {/* --- Pricing cards section --- */}
      <div
        ref={sectionRef} // Ref for Intersection Observer
        className="relative mx-auto mt-12 w-auto"
        // Dynamic height calculation based on number of options, ensures space for animation
        style={{
          minHeight: isMobile ? 'auto' : '700px',
          perspective: '1000px', // Add perspective for smoother 3D transforms
          perspectiveOrigin: 'center center', // Center the perspective
          willChange: 'contents', // Optimize for content changes
          transform: 'translateZ(0)', // Force GPU acceleration
        }}
      >
        {isMobile ? (
          // Mobile: Simple Grid Layout
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2"> {/* Adjust grid cols as needed */}
            {pricingOptions?.map((option, index) => (
              // No extra wrapper needed unless for specific grid styling
              <PricingOption
                key={option.id || option.planName}
                option={option}
                altcolor={altcolor || false} // Pass down altcolor prop
                index={index}
                total={totalOptions}
                isVisible={true} // Always visible in mobile grid
                isMobile={true} // Use mobile rendering
              />
            ))}
          </div>
        ) : (
          // Desktop: Animation Layout - PricingOption are DIRECT children
          pricingOptions?.map((option, index) => (
            <PricingOption
              key={option.id || option.planName}
              option={option}
              altcolor={altcolor || false} // Pass down altcolor prop
              index={index}
              total={totalOptions}
              isVisible={isVisible} // Use state controlled by observer
              isMobile={false} // Use desktop rendering with animation
            />
          ))
        )}
      </div>
    </div>
  );
};

export default PricingTableBlock;
