import type { ArchiveBlock as ArchiveBlockProps, Post } from '@/payload-types'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import RichText from '@/components/RichText'

import { CollectionArchive } from '@/components/CollectionArchive'
import { Headline } from '@ui/components/front-page/headline'

export const ArchiveBlock: React.FC<
  ArchiveBlockProps & {
    id?: string
  }
> = async (props) => {
  const { id, categories, introContent, limit: limitFromProps, populateBy, selectedDocs } = props

  const limit = limitFromProps || 3

  let posts: Post[] = []

  if (populateBy === 'collection') {
    const payload = await getPayload({ config: configPromise })

    const flattenedCategories = categories?.map((category) => {
      if (typeof category === 'object') return category.id
      else return category
    })

    const fetchedPosts = await payload.find({
      collection: 'posts',
      depth: 1,
      limit,
      sort: [
        '-pinned', // Sort pinned posts first (true values before false)
        '-publishedAt', // Then sort by publish date (newest first)
      ],
      select: {
        title: true,
        slug: true,
        categories: true,
        meta: true,
        pinned: true,
        publishedAt: true,
      },
      ...(flattenedCategories && flattenedCategories.length > 0
        ? {
            where: {
              categories: {
                in: flattenedCategories,
              },
            },
          }
        : {}),
    })

    posts = fetchedPosts.docs as Post[]
  } else {
    if (selectedDocs?.length) {
      const filteredSelectedPosts = selectedDocs.map((post) => {
        if (typeof post.value === 'object') return post.value
      }) as Post[]

      posts = filteredSelectedPosts
    }
  }

  return (
    <div className=" pb-16 pt-16 w-full" id={`block-${id}`}>
      {props.heading ? (
        <div className="flex flex-col items-center justify-center space-y-4 text-center ">
          <div className="space-y-2 z-10 max-w-4xl mx-auto">
            <Headline>
              {props.heading}
            </Headline>
          </div>
        </div>
      ) : (<div className="min-h-6"/>) }
      {introContent && (
        <div className="container mb-16 copy ">
          <RichText className="ml-0 max-w-[48rem] mx-auto text-center" data={introContent} enableGutter={false} />
        </div>
      )}
      <CollectionArchive posts={posts} />
    </div>
  )
}
