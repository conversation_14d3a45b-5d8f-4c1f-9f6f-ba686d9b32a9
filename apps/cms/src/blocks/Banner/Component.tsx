import type { BannerBlock as BannerBlockProps } from 'src/payload-types'

import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

type Props = {
  className?: string
} & BannerBlockProps

export const BannerBlock: React.FC<Props> = ({ className, content, style }) => {
  return (
    <div className={cn('mx-auto my-16 w-full max-w-5xl', className)}>
      <div
        className={cn(
          'relative border py-7 px-10 flex items-center rounded-lg shadow-sm backdrop-blur-md', 
          'transition-all duration-300 hover:shadow-md',
          'overflow-hidden', {
          'border-border/30 bg-card/70': style === 'info',
          'border-error/30 bg-error/10': style === 'error',
          'border-success/30 bg-success/10': style === 'success',
          'border-warning/30 bg-warning/10': style === 'warning',
        })}
      >
        {/* Enhanced gradient background */}
        <div className={cn(
          "absolute inset-0 pointer-events-none",
          {
            'bg-gradient-to-br from-brand/10 via-transparent to-foreground/5': style === 'info',
            'bg-gradient-to-br from-error/10 via-transparent to-foreground/5': style === 'error',
            'bg-gradient-to-br from-success/10 via-transparent to-foreground/5': style === 'success',
            'bg-gradient-to-br from-warning/10 via-transparent to-foreground/5': style === 'warning',
          }
        )}></div>
        
        {/* Enhanced decorative corner accent */}
        <div className="absolute top-0 right-0 w-16 h-16 overflow-hidden pointer-events-none">
          <div className={cn('absolute top-0 right-0 w-20 h-1.5 transform rotate-45 translate-y-4', {
            'bg-brand/50': style === 'info',
            'bg-error/50': style === 'error',
            'bg-success/50': style === 'success',
            'bg-warning/50': style === 'warning',
          })}></div>
        </div>
        
        {/* Subtle decorative element */}
        <div className={cn(
          "absolute -bottom-10 -left-10 w-40 h-40 rounded-full blur-3xl opacity-10",
          {
            'bg-brand/40': style === 'info',
            'bg-error/30': style === 'error',
            'bg-success/30': style === 'success',
            'bg-warning/30': style === 'warning',
          }
        )}></div>
        
        {/* Very subtle grain texture */}
        <div className="absolute inset-0 bg-noise opacity-[0.02] mix-blend-overlay pointer-events-none"></div>
        
        <div className="w-full relative z-10">
          <RichText data={content} enableGutter={false} enableProse={true} />
        </div>
      </div>
    </div>
  )
}
