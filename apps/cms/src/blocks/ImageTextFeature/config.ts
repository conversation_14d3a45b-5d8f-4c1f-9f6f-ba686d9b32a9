import { Block } from 'payload'

const ImageTextFeature: Block = {
  slug: 'imageText',
  interfaceName: 'IImageTextFeatureBlock',
  labels: {
    singular: 'Image Text Feature',
    plural: 'Image Text Features',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      type: 'row',
      fields: [
        {
          name: 'heading',
          type: 'text',
          label: 'Feature Heading',
          required: false,
          admin: {
            width: '50%',
          },
        },
        {
          name: 'subheading',
          type: 'text',
          label: 'Feature Subheading',
          required: false,
          admin: {
            width: '50%',
          },
        },
      ],
    },
    {
      name: 'layoutStyle',
      type: 'select',
      label: 'Layout Style',
      options: [

        {
          label: 'Left Side Image',
          value: 'leftSideImage',
        },
        {
          label: 'Right Side Image',
          value: 'rightSideImage',
        },
        {
          label: 'Image Overlay (text on image)',
          value: 'overlay',
        },
        {
          label: 'Text Above Image',
          value: 'textAbove',
        },
        {
          label: 'Text Below Image',
          value: 'textBelow',
        },
      ],
      defaultValue: 'textBelow',
      required: true,
    },
    {
      name: 'content',
      type: 'richText',
      label: 'Feature Content',
      required: true,
    },
    {
      name: 'media',
      type: 'upload',
      label: 'Feature Image',
      relationTo: 'media',
      required: true,
    },
    {
      type: 'row',
      fields: [
        {
          name: 'imageSize',
          type: 'select',
          label: 'Image Size',
          options: [
            {
              label: 'Small',
              value: 'small',
            },
            {
              label: 'Medium',
              value: 'medium',
            },
            {
              label: 'Large',
              value: 'large',
            },
            {
              label: 'Full Width',
              value: 'full',
            },
          ],
          defaultValue: 'medium',
          required: true,
          admin: {
            width: '50%',
          },
        },
        {
          name: 'imageBorderRadius',
          type: 'select',
          label: 'Image Border Radius',
          options: [
            {
              label: 'None',
              value: 'none',
            },
            {
              label: 'Small',
              value: 'small',
            },
            {
              label: 'Medium',
              value: 'medium',
            },
            {
              label: 'Large',
              value: 'large',
            },
            {
              label: 'Full (Circle)',
              value: 'full',
            },
          ],
          defaultValue: 'medium',
          required: true,
          admin: {
            width: '50%',
          },
        },
      ],
    },
    {
      name: 'headingPosition',
      type: 'select',
      label: 'Heading Position',
      options: [
        {
          label: 'Above Content',
          value: 'above',
        },
        {
          label: 'Above Image',
          value: 'aboveImage',
        },
        {
          label: 'Center',
          value: 'center',
        },
      ],
      defaultValue: 'above',
      required: true,
    },
    {
      name: 'headingAlignment',
      type: 'select',
      label: 'Heading Alignment',
      options: [
        {
          label: 'Left',
          value: 'left',
        },
        {
          label: 'Center',
          value: 'center',
        },
        {
          label: 'Right',
          value: 'right',
        },
      ],
      defaultValue: 'center',
      required: true,
    },
    {
      name: 'se',
      type: 'group',
      label: 'Style & Effects',
      fields: [
        {
          name: 'background',
          type: 'checkbox',
          label: 'Background Effect',
          defaultValue: false,
        },
        {
          name: 'backgroundMedia',
          type: 'upload',
          label: 'Background Media',
          relationTo: 'media',
          required: false,
        },
        {
          name: 'color',
          type: 'select',
          label: 'Text Color Theme',
          options: [
            {
              label: 'Default',
              value: 'default',
            },
            {
              label: 'Light',
              value: 'light',
            },
            {
              label: 'Dark',
              value: 'dark',
            },
            {
              label: 'Brand Accent',
              value: 'brand',
            },
          ],
          defaultValue: 'default',
          required: true,
        },
        {
          name: 'enableShadow',
          type: 'checkbox',
          label: 'Enable Shadow Effect on Image',
          defaultValue: true,
        },
        {
          name: 'enableHoverEffect',
          type: 'checkbox',
          label: 'Enable Hover Animation on Image',
          defaultValue: true,
        },
      ],
    },
  ],
}

export default ImageTextFeature
