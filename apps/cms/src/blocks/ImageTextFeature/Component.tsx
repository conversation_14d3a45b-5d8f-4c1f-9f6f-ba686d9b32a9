import React from 'react'
import { Media } from '@/components/Media'
import RichText from '@/components/RichText'
import { cn } from '@/utilities/ui'
import { BlockWrapper } from '@/components/BlockWrapper'
import { IImageTextFeatureBlock } from '@/payload-types'

const getImageSizeClass = (size?: string): string => {
  switch (size) {
    case 'small':
      return 'max-w-md mx-auto lg:max-w-sm'
    case 'medium':
      return 'max-w-2xl mx-auto lg:max-w-xl'
    case 'large':
      return 'max-w-4xl mx-auto lg:max-w-2xl'
    case 'full':
      return 'w-full'
    default:
      return 'max-w-2xl mx-auto lg:max-w-xl'
  }
}

const getImageBorderRadiusClass = (radius?: string): string => {
  switch (radius) {
    case 'none':
      return 'rounded-none'
    case 'small':
      return 'rounded-md'
    case 'medium':
      return 'rounded-lg'
    case 'large':
      return 'rounded-2xl'
    case 'full':
      return 'rounded-full'
    default:
      return 'rounded-lg'
  }
}

const getTextColorClass = (colorTheme?: string, hasBackgroundMedia?: boolean): string => {
  if (hasBackgroundMedia) return 'text-background'

  switch (colorTheme) {
    case 'light':
      return 'text-neutral-subtle'
    case 'dark':
      return 'text-neutral-dark'
    case 'brand':
      return 'text-brand-500'
    default:
      return 'text-foreground'
  }
}

export const ImageTextFeatureBlock: React.FC<IImageTextFeatureBlock> = ({
                                                                          heading,
                                                                          subheading,
                                                                          content,
                                                                          media,
                                                                          layoutStyle,
                                                                          imageSize = 'medium',
                                                                          imageBorderRadius = 'medium',
                                                                          headingPosition = 'above',
                                                                          headingAlignment = 'center',
                                                                          se,
                                                                          inset,
                                                                        }) => {

  // Derived classes based on props
  const hasBackgroundMedia = !!se.backgroundMedia
  const textColorClass = getTextColorClass(se.color, hasBackgroundMedia)
  const imageSizeClass = getImageSizeClass(imageSize)
  const imageBorderClass = getImageBorderRadiusClass(imageBorderRadius)
  const headingAlignmentClass = headingAlignment === 'center' ? 'text-center' :
    headingAlignment === 'right' ? 'text-right' : 'text-left'

  // Image classes
  const imageClasses = cn(
    imageBorderClass,
    "border border-foreground/10",
    se.enableShadow && "shadow-lg",
    se.enableHoverEffect && "transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1",
    "w-full h-auto"
  )

  // Heading block
  const renderHeading = () => {
    if (!heading && !subheading) return null

    return (
      <div className={cn("mb-4", headingAlignmentClass)}>
        {heading && (
          <h2 className={cn(
            "text-3xl md:text-4xl font-bold tracking-tight leading-tight mb-4",
            textColorClass
          )}>
            {heading}
          </h2>
        )}
        {subheading && (
          <h4 className={cn(
            "text-lg md:text-xl opacity-80",
            textColorClass
          )}>
            {subheading}
          </h4>
        )}
      </div>
    )
  }

  // Render the image
  const renderImage = () => {
    if (!media) return null

    return (
      <div className={cn("relative", imageSizeClass)}>
        <Media
          resource={media}
          className={imageClasses}
          imgClassName={imageClasses}
        />
      </div>
    )
  }

  // Render the content
  const renderContent = () => {
    if (!content) return null

    return (
      <div className={cn(
        "prose prose-lg max-w-none",
        hasBackgroundMedia ? "prose-invert" : ""
      )}>
        <RichText data={content}  enableGutter={false}/>
      </div>
    )
  }

  // Render layout based on selected option
  const renderLayout = () => {
    switch (layoutStyle) {
      case 'leftSideImage':
        return (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center lg:grid-flow-row-dense">
              {headingPosition === 'center' && (
                <div className="lg:col-span-2 mb-8">
                  {renderHeading()}
                </div>
              )}

              {/* Text Content */}
              <div className="flex flex-col space-y-4 lg:order-2">
                {headingPosition === 'above' && !headingPosition.includes('center') && renderHeading()}
                {renderContent()}
              </div>

              {/* Image */}
              <div className="flex items-center justify-center lg:order-1">
                {headingPosition === 'aboveImage' && renderHeading()}
                {renderImage()}
              </div>
            </div>
          </>
        )

      case 'rightSideImage':
        return (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center lg:grid-flow-row">
              {headingPosition === 'center' && (
                <div className="lg:col-span-2 mb-8">
                  {renderHeading()}
                </div>
              )}

              {/* Text Content */}
              <div className="flex flex-col space-y-4 lg:order-1">
                {headingPosition === 'above' && !headingPosition.includes('center') && renderHeading()}
                {renderContent()}
              </div>

              {/* Image */}
              <div className="flex items-center justify-center lg:order-2">
                {headingPosition === 'aboveImage' && renderHeading()}
                {renderImage()}
              </div>
            </div>
          </>
        )

      case 'overlay':
        return (
          <>
            {headingPosition !== 'center' && renderHeading()}

            <div className="relative overflow-hidden">
              {renderImage()}
              <div className="absolute inset-0 bg-neutral-dark/40 flex items-center justify-center p-8 md:p-12">
                <div className="text-neutral-50 text-center max-w-2xl">
                  {headingPosition === 'center' && renderHeading()}
                  {renderContent()}
                </div>
              </div>
            </div>
          </>
        )

      case 'textAbove':
        return (
          <div className="flex flex-col gap-8 lg:gap-12">
            {renderHeading()}
            <div className="max-w-3xl mx-auto">{renderContent()}</div>
            <div className="mt-8">{renderImage()}</div>
          </div>
        )

      case 'textBelow':
        return (
          <div className="flex flex-col gap-8 lg:gap-12">
            {renderHeading()}
            <div>{renderImage()}</div>
            <div className="max-w-3xl mx-auto mt-8">{renderContent()}</div>
          </div>
        )


      default:
        return (
          <div className="flex flex-col gap-8">
            {renderHeading()}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">{renderImage()}{renderContent()}</div>
          </div>
        )
    }
  }

  return (
    <BlockWrapper
      className="mx-4 sm:mx-0 sm:px-0"
      background={se.background}
      backgroundMedia={se.backgroundMedia as any}
      darkMode={true}
      subtle={true}
      inset={inset}
    >
      <div className={cn(textColorClass)}>
        {renderLayout()}
      </div>
    </BlockWrapper>
  )
}

export default ImageTextFeatureBlock
