import { Block } from 'payload'

const Newsletter: Block = {
  slug: 'newsletter-signup',
  interfaceName: 'INewsletterBlock',
  labels: {
    singular: 'Newsletter Signup',
    plural: 'Newsletter Signups',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'title',
      type: 'text',
      label: 'Title',
      required: true,
      defaultValue: 'Join Our Growing Community',
    },
    {
      name: 'subtitle',
      type: 'textarea',
      label: 'Subtitle',
      required: true,
      defaultValue:
        'Sign up to stay up to date with the latest news and updates from ekoIntelligence.',
    },
    {
      name: 'buttonLabel',
      type: 'text',
      label: 'Button Label',
      required: true,
      defaultValue: 'Sign Up',
    },
    {
      name: 'placeholder',
      type: 'text',
      label: 'Email Input Placeholder',
      required: true,
      defaultValue: 'Enter your email',
    },
    {
      name: 'background',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
  ],
};

export default Newsletter;
