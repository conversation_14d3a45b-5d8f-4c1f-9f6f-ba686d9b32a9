"use client";
import React, { useEffect, useState } from 'react'
import { INewsletterBlock, Media } from '@/payload-types'
import { EmailCapture } from '@/components/eko/email-capture/email-capture'
import { useWindowSize } from 'usehooks-ts'
import { motion } from 'framer-motion'
import { BlockWrapper } from '@/components/BlockWrapper'
import { cn } from '@utils/lib/utils'

export const NewsletterBlock: React.FC<INewsletterBlock> = ({
  title,
  subtitle,
  buttonLabel,
  placeholder,
  background,
}) => {
  const [isSmallWidth, setIsSmallWidth] = useState(false)
  const winWidth = useWindowSize().width;

  useEffect(() => {
    setIsSmallWidth(winWidth < 600)
  }, [winWidth])

  return (
    <BlockWrapper
      backgroundMedia={background as Media}
      darkMode={true}
      subtle={true}
      className="overflow-hidden"
      enableParallax={true}
      parallaxIntensity={0.05}
    >

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className={cn(
          "relative mx-auto w-full standard-rounding px-8 py-16 md:px-12 md:py-16 overflow-visible glass-effect-lit"
        )}
        style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
      >
          {/* Enhanced background pattern or image with aurora-inspired gradient */}

          {/* Enhanced decorative elements */}
        <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-to-br from-brand/20 to-transparent rounded-full blur-2xl -mr-20 -mt-20 opacity-60 animate-aurora-slow" style={{ willChange: 'transform, background-position', transform: 'translateZ(0)' }}></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-brand-accent/10 to-transparent rounded-full blur-2xl -ml-20 -mb-20 opacity-40" style={{ willChange: 'transform', transform: 'translateZ(0)' }}></div>

        {/* Subtle noise texture */}
        <div className="absolute inset-0 bg-noise opacity-[0.03] mix-blend-overlay pointer-events-none"></div>

        <div className="relative z-10 space-y-8 text-center">
          <div className="space-y-4">
            <h2 className="heading-2 text-white dark:text-white">
              {title}
            </h2>

            <p className="mx-auto max-w-xl text-white dark:text-white text-lead">
              {subtitle}
            </p>
          </div>

          <div className={cn(
            "max-w-lg mx-auto w-full",
            "hover-lift-subtle transition-standard"
          )}>
            <EmailCapture
              buttonLabel={buttonLabel || "Sign Up"}
              placeholder={!isSmallWidth ? (placeholder || "Please enter your email address") : "Your email"}
              signUpMessage="Thank you for joining our community!"
            />
          </div>
        </div>
      </motion.div>
    </BlockWrapper>
  )
}
