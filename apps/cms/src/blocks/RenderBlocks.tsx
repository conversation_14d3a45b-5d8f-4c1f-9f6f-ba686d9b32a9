import React, { Fragment } from 'react'

import type { Page } from '@/payload-types'

import { ArchiveBlock } from '@/blocks/ArchiveBlock/Component'
import { CallToActionBlock } from '@/blocks/CallToAction/Component'
import { ContentBlock } from '@/blocks/Content/Component'
import { FormBlock } from '@/blocks/Form/Component'
import { MediaBlock } from '@/blocks/MediaBlock/Component'
import { CompanyReportCTABlock } from '@/blocks/CompanyReports/CompanyReportCTABlock'
import { CompanyOverviewGraphsBlock } from '@/blocks/CompanyReports/CompanyOverviewGraphsBlock'
import { CompanyGraphBlock } from '@/blocks/CompanyReports/CompanyGraphBlock'
import { CompanyProfileBlock } from '@/blocks/CompanyReports/CompanyProfileBlock'
import { AnalysisBlock } from '@/blocks/CompanyReports/AnalysisBlock'
import { TeamContentBlock } from '@/blocks/Team/Component'
import PricingTableBlock from '@/blocks/PricingTable/Component'
import FeaturesBlock from '@/blocks/Features/Component'
import { NewsletterBlock } from '@/blocks/Newsletter/Component'
import { BackgroundReadingBlock } from '@/blocks/BackgroundReading/Component'
import { HighlightedTextBlock } from '@/blocks/HighlightedText/Component'
import SocialProofBlock from '@/blocks/SocialProof/Component'
import FAQBlock from '@/blocks/FAQ/Component'
import ImageTextFeatureBlock from '@/blocks/ImageTextFeature/Component'
import CascadingTextBlock from '@/blocks/CascadingText/Component'
import QuadrantBlock from '@/blocks/Quadrant/Component'

// Common interface for all block components
interface CommonBlockProps {
  disableInnerContainer?: boolean;
}

// Define block components with the common interface
const blockComponents: Record<string, React.ComponentType<any & CommonBlockProps>> = {
  archive: ArchiveBlock,
  content: ContentBlock,
  cta: CallToActionBlock,
  formBlock: FormBlock,
  mediaBlock: MediaBlock,
  'cr-profile': CompanyProfileBlock,
  'cr-overview-graphs': CompanyOverviewGraphsBlock,
  'cr-graph': CompanyGraphBlock,
  'cr-analysis': AnalysisBlock,
  'cr-cta': CompanyReportCTABlock,
  pricing: PricingTableBlock,
  team: TeamContentBlock,
  features: FeaturesBlock,
  'newsletter-signup': NewsletterBlock,
  'background-reading': BackgroundReadingBlock,
  highlightedText: HighlightedTextBlock,
  socialProof: SocialProofBlock,
  faq: FAQBlock,
  imageText: ImageTextFeatureBlock,
  cascadingText: CascadingTextBlock,
  quadrant: QuadrantBlock
}

export const RenderBlocks: React.FC<{
  blocks: Page['layout'][0][]
}> = (props) => {
  const { blocks } = props

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    return (
      <Fragment>
        {blocks.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              return (
                <div className={`relative my-0 py-0  ${block.inset ? 'bg-gradient-to-b from-neutral-100/5 via-transparent to-neutral-100/5 dark:from-brand/5 dark:via-transparent dark:to-brand/5' : ''}`} key={index}>

                  {block.inset ? (
                    <>
                      {/* Light mode inset shadows */}
                      <div className="absolute bottom-0 left-0 right-0 h-8 shadow-[0_-3px_5px_-2px_rgba(0,0,0,0.06)_inset] bg-transparent dark:hidden"></div>
                      <div className="absolute top-0 left-0 right-0 h-8 shadow-[0_3px_5px_-2px_rgba(0,0,0,0.06)_inset] bg-transparent dark:hidden"></div>

                      {/* Dark mode gradient borders */}
                      <div className="hidden dark:block absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-brand/50 to-transparent"></div>
                      <div className="hidden dark:block absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-brand/30 to-transparent"></div>

                      {/* Background gradient effect for both modes */}
                      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_hsla(145,10%,45%,0.1),transparent_50%),radial-gradient(ellipse_at_bottom_left,_hsl(145,10%,30%,0.05),transparent_50%)] dark:bg-[radial-gradient(ellipse_at_top_right,_hsla(145,20%,45%,0.15),transparent_50%),radial-gradient(ellipse_at_bottom_left,_hsl(145,20%,30%,0.1),transparent_50%)] pointer-events-none"></div>
                    </>
                  ) : <div className='bg-noise opacity-[0.03] mix-blend-darken dark:mix-blend-soft-light pointer-events-none absolute inset-0'/>}
                  <Block {...block} disableInnerContainer={true} />
                </div>
              )
            }
          }
          return null
        })}
      </Fragment>
    )
  }

  return null
}
