import { cn } from '@/utilities/ui'
import React from 'react'
import RichText from '@/components/RichText'

import type { ContentBlock as ContentBlockProps } from '@/payload-types'

import { CMSLink } from '../../components/Link'
import { BlockWrapper } from '@/components/BlockWrapper'
import { Section } from '@/components/Section'

export const ContentBlock: React.FC<ContentBlockProps> = (props) => {
  const { columns, inset } = props

  const colsSpanClasses = {
    full: '12',
    half: '6',
    oneThird: '4',
    twoThirds: '8',
  }


  const medColsSpanClasses = {
    full: '12',
    half: '12',
    oneThird: '6',
    twoThirds: '12',
  }

  return (
    <BlockWrapper inset={inset} noPadding containerClassName="container">
      <Section spacing="none">
        <div className="grid grid-cols-4 lg:grid-cols-12 gap-y-12 gap-x-8 md:gap-x-16">
        {columns &&
          columns.length > 0 &&
          columns.map((col, index) => {
            const { enableLink, link, richText, size } = col

            return (
              <div
                className={cn(`col-span-4 lg:col-span-${colsSpanClasses[size!]} relative group`, {
                  'md:col-span-4 ': size !== 'full',
                })}
                key={index}
              >

                {/* Refined decorative floating elements */}
                <div className="absolute -z-10 w-28 h-28 rounded-full bg-gradient-to-br from-brand/10 to-brand/5 blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 ease-in-out transform translate-x-4 -translate-y-4 group-hover:translate-x-0 group-hover:-translate-y-0"
                     style={{top: '-8%', right: '-12%'}}></div>
                <div className="absolute -z-10 w-20 h-20 rounded-full bg-gradient-to-tr from-brand-light/10 to-transparent blur-lg opacity-0 group-hover:opacity-60 transition-opacity duration-700 delay-100 ease-in-out transform -translate-x-4 translate-y-4 group-hover:translate-x-0 group-hover:translate-y-0"
                     style={{bottom: '3%', left: '-10%'}}></div>

                <div className="h-full flex flex-col sm:px-8 lg:px-10  relative z-10 group-hover:bg-foreground/[0.02] transition-colors duration-500">
                  {richText && (
                    <div className="flex-grow">
                      <RichText
                        data={richText}
                        enableGutter={false}
                        enableProse={true}
                        className="prose-headings:relative prose-headings:z-10 prose-headings:mb-6 prose-headings:text-gray-900 dark:prose-headings:text-white
                                 prose-p:relative prose-p:z-10 prose-p:text-foreground/80 dark:prose-p:text-white/80
                                 prose-a:text-brand prose-a:no-underline prose-a:font-medium hover:prose-a:text-brand-dark
                                  prose-a:duration-300 hover:prose-a:underline
                                "
                      />
                    </div>
                  )}

                  {enableLink && (
                    <div className="mt-8">
                      <CMSLink
                        {...link}
                        className="inline-flex items-center rounded-full bg-gradient-to-r from-brand to-brand-dark hover:from-brand-dark hover:to-brand
                                text-white px-7 py-3 text-sm font-medium shadow-md hover:shadow-lg transition-all duration-500
                                transform hover:-translate-y-0.5 border border-brand-light/10 relative overflow-hidden group"
                      >
                        <span className="relative z-10 flex items-center">
                          <span className="mr-1.5">View details</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16" height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="transition-transform duration-300 group-hover:translate-x-1">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                          </svg>
                        </span>
                        {/* Button animated highlight effect */}
                        <span className="absolute inset-0 bg-white/10 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500"></span>
                      </CMSLink>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
      </div>
      </Section>
    </BlockWrapper>
  )
}
