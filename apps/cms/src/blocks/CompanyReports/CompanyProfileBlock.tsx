// Component for the Company Profile Block
import React from 'react'

export const CompanyProfileBlock = ({ company_data }: { company_data: any }) => {
  return (
    <section className="company-profile p-4  container-medium">
      <h1 className="text-2xl font-bold">{company_data.name}</h1>
      <p className="mt-2">{company_data.description}</p>
      {/*{company_data.short_id && (*/}
      {/*  <p className="text-sm text-neutral-500">ID: {company_data.short_id}</p>*/}
      {/*)}*/}
      {company_data.domains && company_data.domains.length > 0 && (
        <div className="mt-2">
          <h3 className="font-semibold">Domains:</h3>
          <ul>
            {company_data.domains.map((domain: any, idx: number) => (
              <li key={idx}>
                <a href={domain.url} className="text-blue-500 underline">
                  {domain.url}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </section>
  )
}
