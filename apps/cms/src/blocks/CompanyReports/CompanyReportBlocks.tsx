// src/components/CompanyReportBlocks.tsx
import React from 'react'
import { CompanyReportCTABlock } from '@/blocks/CompanyReports/CompanyReportCTABlock'
import { CompanyOverviewGraphsBlock } from '@/blocks/CompanyReports/CompanyOverviewGraphsBlock'
import { CompanyGraphBlock } from '@/blocks/CompanyReports/CompanyGraphBlock'
import { CompanyProfileBlock } from '@/blocks/CompanyReports/CompanyProfileBlock'
import { AnalysisBlock } from '@/blocks/CompanyReports/AnalysisBlock'
import { FormBlock } from '@/blocks/Form/Component'


// Main Blocks Renderer component that maps over blocks data
const BlocksRenderer = ({ blocks }: { blocks: any[] }) => {
  return (
    <>
      {blocks.map((block, index) => {
        // block.blockType is set to the block's slug in your Payload config
        switch (block.blockType) {
          case 'cr-profile':
            return (
              <CompanyProfileBlock
                key={index}
                company_data={block.company_data}
              />
            )
          case 'cr-overview-graphs':
            return (
              <CompanyOverviewGraphsBlock
                key={index}
                version={block.version}
                data={block.data}
              />
            )
          case 'cr-graph':
            return (
              <CompanyGraphBlock
                key={index}
                graph={block.graph}
                title={block.title}
                data={block.data}
                blockType={block.blockType}
              />
            )
          case 'cr-analysis':
            return (
              <AnalysisBlock
                key={index}
                analysis={block.analysis}
                refs={block.refs}
                visibility={block.visibility}
                blockType={block.blockType}
              />
            )
          case 'cr-cta':
            return (
              <CompanyReportCTABlock
                key={index}
                title={block.title}
                description={block.description}
                cta={block.cta}
              />
            )
          case 'formBlock':
            return (
              <FormBlock
                key={index}
                id={block.id}
                enableIntro={block.enableIntro}
                form={block.form}
                introContent={block.introContent}
              />
            )
          default:
            return null
        }
      })}
    </>
  )
}

export default BlocksRenderer
