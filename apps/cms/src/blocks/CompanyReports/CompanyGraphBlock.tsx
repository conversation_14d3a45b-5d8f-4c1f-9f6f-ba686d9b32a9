"use client";
// Component for the Company Graph Block
import React from 'react'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@ui/components/ui/chart'
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from 'recharts'
import { ICompanyGraphBlock } from '@/payload-types'

export const CompanyGraphBlock: React.FC<ICompanyGraphBlock> =(props) => {
  const {data,title,graph}= props;
  const chartConfig= (data as any).config;
  const xAxis=(data as any).axis.x;
  const yAxis=(data as any).axis.y;

  // Sort the data points by the x-axis key (date)
  const sortedPoints = React.useMemo(() => {
    if (!(data as any).points || !(data as any).points.length) return [];

    return [...(data as any).points].sort((a, b) => {
      // Handle numeric years
      if (!isNaN(Number(a[xAxis.key])) && !isNaN(Number(b[xAxis.key]))) {
        return Number(a[xAxis.key]) - Number(b[xAxis.key]);
      }
      // Handle string dates (fallback to string comparison)
      return String(a[xAxis.key]).localeCompare(String(b[xAxis.key]));
    });
  }, [(data as any).points, xAxis.key]);

  return (
    <section className="company-graph container-small text-center mb-16" >
      <h2 className="text-xl mb-6 font-bold">{title}</h2>
      { graph === "bar" && (<ChartContainer config={chartConfig} className="min-h-[200px] w-full">
        <BarChart accessibilityLayer data={sortedPoints}>
          <CartesianGrid/>
          <XAxis
            dataKey={xAxis["key"]}
            tickLine={false}
            tickMargin={20}
          />
          <YAxis

          />
          <ChartTooltip content={<ChartTooltipContent />} />
          {Object.keys(chartConfig).map((s: any) => (
          <Bar key={s} dataKey={s} fill={chartConfig[s].color} radius={4} />
          ))}
        </BarChart>
      </ChartContainer>)
      }
    </section>
  )
}
