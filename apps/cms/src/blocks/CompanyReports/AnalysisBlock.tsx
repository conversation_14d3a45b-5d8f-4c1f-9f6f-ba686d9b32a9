'use client'
import { ICompanyAnalysisBlock } from '@/payload-types'
import { useLocalStorage } from 'usehooks-ts'
import { EmailCapture } from '@/components/eko/email-capture/email-capture'
import RichText from '@/components/RichText'
import { cn } from '@utils/lib/utils'
import CustomErrorBoundary from '@ui/components/error-boundary'
import { useEffect, useState } from 'react'

export const AnalysisBlock = ({
                                analysis,
                                refs,
                                visibility,
                              }: ICompanyAnalysisBlock) => {
  const [hasGivenEmail, setHasGivenEmail] = useLocalStorage<string | null>('has-given-email', null)
  const [visible, setVisible] = useState(true)

  useEffect(() => {
    if (visibility === 'email') {
      setVisible(hasGivenEmail !== null)
    } else {
      setVisible(true)
    }
  }, [])

  console.log('Company Analysis', analysis, refs, visibility, hasGivenEmail, visible)
  return (
    <CustomErrorBoundary>
      <section className="company-analysis p-4 container-medium relative prose:text-foreground">
        {analysis && <RichText enableGutter={false} data={analysis} className={`${visible ? 'prose:container-medium' : 'hidden-content'}`} />}
        {refs && refs.length > 0 && (
          <div className={cn('mt-4 filter', !visible && 'blur-md max-h-40 overflow-clip')}>
            <h3 className="font-semibold">References:</h3>
            <ul className="list-disc ml-5">
              {refs.map((ref: any, idx: number) => (
                <li key={idx}>
                  <a href={ref.url + '#page=' + (ref.pages[0]?.page + 1)} className="text-blue-500 underline">
                    {ref.title && ref.title}, p.{ref.pages.map((i: any) => i.page + 1).join(', ')}
                  </a>
                  {ref.publish_year && ` (${ref.publish_year})`}
                </li>
              ))}
            </ul>
          </div>
        )}
        {!visible && (
          <div className={cn('absolute left-0 right-0 bottom-24 container', visible && 'hidden')}>
            <div
              className="z-50 shadow-xl border-1 mx-auto my-0 bg-background dark:bg-neutral-800 flex flex-col max-w-[600px] items-center gap-4 px-6 py-4 text-center sm:px-8 md:py-8 rounded-lg  border-neutral-500 dark:border-neutral-200">
              <div className="space-y-3">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight md:text-shadow-md border-1">
                  Would you like to know more?
                </h2>
                <p
                  className="mx-auto max-w-[600px] text-foreground dark:text-foreground opacity-80 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Just enter your email and we&apos;ll show you the rest!
                </p>
              </div>
              <EmailCapture onSuccess={(email) => setHasGivenEmail(email)} buttonLabel="Sign Up"
                            placeholder="Please enter your email address" />
            </div>
          </div>
        )
        }
      </section>
    </CustomErrorBoundary>

  )
}
