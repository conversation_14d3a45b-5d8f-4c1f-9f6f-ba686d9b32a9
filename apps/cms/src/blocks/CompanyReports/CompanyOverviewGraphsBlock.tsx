// Component for the Company Overview Graphs Block
import React from 'react'
import Donut from '@ui/components/graph/donut/donut'
import { DOUGHNUT_SEGMENT_MAP } from '@ui/components/graph/donut/types'
import { Card, CardContent, CardHeader } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { cn } from '@utils/lib/utils'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@ui/components/ui/accordion'
import { TooltipStyled } from '@/components/ui/tooltip-styled'

interface Issue {
  name: string
  type: 'red' | 'green'
  value: number
  start_year: number
  end_year: number
  category: string
  summary: string
}

function boxColorRed(value:number) {
  return value >= 80 ? 'bg-red-700 hover:bg-red-800 text-white/80' :
    value >= 70 ? 'bg-red-600 hover:bg-red-700 text-white/90' :
      value >= 50 ? 'bg-red-500 hover:bg-red-600 text-white/90' :
        value >= 30 ? 'bg-red-300 hover:bg-red-400  text-white/90' :
          value >= 10 ? 'bg-red-200 text-neutral-700 hover:bg-red-300' :
            'bg-red-200 text-neutral-700 hover:bg-red-300'
}

function boxColorGreen(value: number) {
  return value >= 80 ? 'bg-green-700 hover:bg-green-800 text-white/80' :
    value >= 70 ? 'bg-green-600 hover:bg-green-700 text-white/90' :
      value >= 50 ? 'bg-green-500 hover:bg-green-600 text-white/90' :
        value >= 30 ? 'bg-green-300  text-neutral-700 hover:bg-green-400' :
          value >= 10 ? 'bg-green-200 text-neutral-700 hover:bg-green-300' :
            'bg-green-200 dark:text-neutral-700 hover:bg-green-300'
}

export const CompanyOverviewGraphsBlock = ({
                                             version,
                                             data,
                                           }: {
  version: string
  data: any
}) => {
  console.log(data)
  const breakpoints = {
    0: 300,
    400: 300,
    600: 500,
    768: 350,
    1024: 400,
    1280: 400,
    1536: 500,
  }
  const red_data = data.doughnut?.filter((i: any) => i.type === 'red' || i.type == null)
  const green_data = data.doughnut?.filter((i: any) => i.type === 'green' || i.type == null)
  // Filter issues by type
  const issues: Issue[] = data.issues
  const redIssues = issues.filter(issue => issue.type === 'red')
  const greenIssues = issues.filter(issue => issue.type === 'green')


  const getMinorMajorBad = (score: number) => {
    if (score >= 80) return 'Disastrous'
    if (score >= 70) return 'Serious'
    if (score >= 50) return 'Major'
    if (score >= 30) return 'Minor'
    if (score >= 10) return 'Very Minor'
    return 'Trivial'
  }

  const getMinorMajorGood = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 70) return 'Great'
    if (score >= 50) return 'Good'
    if (score >= 30) return 'Helpful'
    if (score >= 10) return 'Minor'
    return 'Trivial'
  }


  const donuts = false

  function getYears(start_year: number, end_year: number) {
    if (start_year === end_year) {
      return start_year
    } else {
      return `${start_year}-${end_year || ''}`
    }
  }

  return donuts ? (
    <section className="company-overview-graphs p-4 container-medium ">
      <div className="m-auto grid grid-cols-1 md:grid-cols-2 max-w-fit">
        <Donut
          id="donut_red"
          ecoSegments={red_data?.filter((i: any) => i.level === 'ecological')}
          // governanceSegments={red_data?.filter((i: any) => i.level === 'governance')}
          socialSegments={red_data?.filter((i: any) => i.level === 'social')}
          colorScale="red-flags"
          breakpoints={breakpoints}
          className="flex"
          model={DOUGHNUT_SEGMENT_MAP}
        />
        <Donut
          id="donut_green"
          ecoSegments={green_data?.filter((i: any) => i.level === 'ecological')}
          // governanceSegments={green_data?.filter((i: any) => i.level === 'governance')}
          socialSegments={green_data?.filter((i: any) => i.level === 'social')}
          colorScale="green-flags"
          breakpoints={breakpoints}
          className="flex"
          model={DOUGHNUT_SEGMENT_MAP}
        />
      </div>
    </section>
  ) : (
    <section className="container company-overview-graphs p-4  container-medium">

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Final Score Card */}
        <Card className="md:col-span-2">
          <CardHeader className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold">Overall Score</h2>
          </CardHeader>
          <CardContent>
            <div className="text-4xl sm:text-7xl  font-bold text-center">
              {data.final_score}
            </div>
            <div
              className={cn('text-2xl sm:text-7xl font-semibold text-center mt-2', data.final_score >= 60 ? 'text-green-600' : 'text-red-600')}>
              {data.rating_text}
            </div>
            <div className="text-sm text-center opacity-75">

            </div>
            <div className="text-[10px] sm:text-[12px]  text-center opacity-30 mt-4 max-w-[32rem] mx-auto" >
              <p>The score is calculated from the <b>red</b> flags only, as we believe that no amount of good behaviour can offset bad behaviour. Higher values are better, 60+ is good.</p>
            </div>
          </CardContent>
        </Card>
        {/* Red Issues Card */}
        <Card>
          <CardHeader className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold text-red-600">Top Red Flags</h2>
            <span className="text-sm text-neutral-600">
              {redIssues.length} Flag{redIssues.length !== 1 && 's'} • Avg Impact: {Math.round(+data.median_red)}
            </span>
          </CardHeader>
          <CardContent>
            <Accordion type="multiple" className="w-full">
              {['Ecological', 'Social', 'Animal', 'Governance'].map((category, index) => {
                const categoryIssues = redIssues.filter(issue => issue.category === category);
                if (categoryIssues.length === 0) return null;

                const avgValue = (categoryIssues.reduce((a, b) => a + b.value, 0) / categoryIssues.length)
                return (
                  <AccordionItem key={category} value={`red-${category}`} className="border-b border-neutral-200 ">
                    <AccordionTrigger className={cn("p-3 text-white",boxColorRed(avgValue))}>
                      <div className="flex items-center">
                        <span className="text-sm font-semibold">{category}</span>
                        <span className={cn("text-xs   ml-2")}>({categoryIssues.length})</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 py-2">
                        {categoryIssues.sort((a, b) => b.value - a.value).slice(0,5).map(issue => (
                          <TooltipStyled
                            key={issue.name}
                            content={
                              <div className="space-y-1 max-w-64">
                                <div>Years: {getYears(issue.start_year, issue.end_year)}</div>
                                <div>Impact: {getMinorMajorBad(issue.value)}</div>
                                <div>{issue.summary}</div>
                              </div>
                            }
                            variant="error"
                          >
                            <Badge
                              className={cn(
                                'flex cursor-pointer gap-4 flex-row justify-between items-center px-3 py-1 rounded transition-transform duration-300 hover:translate-x-1 ',
                                boxColorRed(issue.value)
                              )}
                            >
                              <div className="flex w-full">{issue.name}</div>
                            </Badge>
                          </TooltipStyled>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </CardContent>
        </Card>

        {/* Green Issues Card */}
        <Card className="flex flex-col">
          <CardHeader className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold text-green-600">Top Green Flags</h2>
            <span className="text-sm text-neutral-600">
              {greenIssues.length} Flag{greenIssues.length !== 1 && 's'} • Avg Impact: {Math.round(data.median_green)}
            </span>
          </CardHeader>
          <CardContent className="flex-grow">
            <Accordion type="multiple" className="w-full">
              {['Ecological', 'Social', 'Animal', 'Governance'].map((category, index) => {
                const categoryIssues = greenIssues.filter(issue => issue.category === category);
                if (categoryIssues.length === 0) return null;
                const avgValue = (categoryIssues.reduce((a, b) => a + b.value, 0) / categoryIssues.length)

                return (
                  <AccordionItem key={category} value={`green-${category}`} className="border-b border-neutral-200">
                    <AccordionTrigger className={cn("p-3 hover:bg-neutral-500 text-white ", boxColorGreen(avgValue))}>
                      <div className="flex items-center">
                        <span className="text-sm font-semibold">{category}</span>
                        <span className={cn("text-xs  hover:bg-neutral-500 text-white  ml-2")}>({categoryIssues.length})</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 py-2">
                        {categoryIssues.sort((a, b) => b.value - a.value).slice(0,5).map(issue => {
                          return (
                            <TooltipStyled
                              key={issue.name}
                              content={
                                <div className="space-y-1 max-w-64">
                                  <div>Years: {getYears(issue.start_year, issue.end_year)}</div>
                                  <div>Impact: {getMinorMajorGood(issue.value)}</div>
                                  <div>{issue.summary}</div>
                                </div>
                              }
                              variant="success"
                            >
                              <Badge
                                className={cn(
                                  'flex cursor-pointer gap-4 flex-row px-3 py-1 rounded transition-transform duration-300 hover:translate-x-1 text-white',
                                  boxColorGreen(issue.value),
                                )}
                              >
                                <div className="flex w-full">{issue.name}</div>
                              </Badge>
                            </TooltipStyled>
                          )
                        })}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </CardContent>
        </Card>


      </div>
    </section>
  )
}
