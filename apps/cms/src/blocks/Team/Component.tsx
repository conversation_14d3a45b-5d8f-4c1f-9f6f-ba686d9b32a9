'use client'
import React, { useEffect } from 'react'

import type { Media, TeamBlock } from '@/payload-types'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { cn } from '@/utilities/ui'
import { Media as MediaTag } from '@/components/Media'
import RichText from '@/components/RichText'
import { Headline } from '@ui/components/front-page/headline'

// Animation variants for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2,
    },
  },
}

// Define a custom slow pulse animation in Tailwind config
// This will need to be added to the project's tailwind.config.js under 'extend.animation'
// 'animate-pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',

const TeamMember = ({
                      name,
                      role,
                      description,
                      media,
                      url,
                      startX,
                    }: {
  name: string
  role: string
  description: any
  media: number | Media
  url: string
  startX: number
}) => {
  console.log(startX)
  return (
    <div className="relative mx-auto flex flex-col items-center justify-start md:max-w-[320px] lg:max-w-[320px]"
        >
      <motion.div
        initial={{ opacity: 0.8, x: startX, y: 20, rotate:startX/100 }}
        whileInView={{ opacity: 1, x: 0, y: 0, rotate:0 }}
        transition={{
          delay: 0.3,
          duration: 0.4,
          ease: 'easeOut',
        }}
        viewport={{ once: true, amount: 0.25 }}
        className="h-full w-full relative standard-rounding"
      >
        <div
          className="relative group p-6 standard-rounding  hover:shadow-xl glass-effect-brand-strong-lit transition-all duration-300 h-full">
          <div className="absolute inset-0 standard-rounding z-0"></div>
          <div className="relative z-10 ">
            <div className="relative w-[160px] h-[160px] mx-auto mb-6 overflow-hidden ">
              <MediaTag
                imgClassName={cn(
                  'rounded-full shadow-lg group-hover:scale-105 transition-transform duration-500 border-2 border-green-100/30 max-w-[160px] max-h-[160px] object-cover',
                )}
                resource={media}
              />
              <div
                className="absolute inset-0 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div
                className="absolute -inset-0.5 rounded-full bg-gradient-to-br from-green-300/20 to-blue-300/20 animate-pulse-slow"></div>
            </div>
            <div className="space-y-4 text-center mb-18">
              <h3
                className="text-xl font-semibold text-green-800 dark:text-green-50 group-hover:text-green-700 dark:group-hover:text-white transition-colors">{name}</h3>
              <p
                className="text-foreground font-bold text-sm inline-block px-4 py-1.5 rounded-full bg-green-100/30 dark:bg-green-900/40 shadow-sm">{role}</p>
              <RichText
                className="not-intro text-sm text-white/90 md:text-base dark:text-white/90 w-full pb-18"
                data={description}
                enableGutter={false}
              />
              <div className="h-16" />
            </div>
          </div>
        </div>
      </motion.div>
      <div className="flex absolute bottom-8 justify-center items-center w-full pt-3">
        <Link
          href={url}
          className="text-green-700 dark:text-green-300 hover:text-green-500 dark:hover:text-green-200 transition-colors p-2.5 rounded-full bg-green-50/30 dark:bg-green-900/30 hover:bg-green-50/50 dark:hover:bg-green-900/50 shadow-sm hover:shadow-md"
          target="_blank"
          rel="noopener noreferrer"
          prefetch={false}
          aria-label={`${name}'s LinkedIn profile`}
        >
          <LinkedinIcon className="w-5 h-5" />
        </Link>
      </div>
    </div>
  )
}


export function TeamContentBlock(props: TeamBlock) {
  const { teamMembers } = props
  const [isSmallScreen, setIsSmallScreen] = React.useState<boolean | null>(null)
  useEffect(() => {
    setIsSmallScreen(window.matchMedia('(max-width: 767px)').matches)
  }, [])

  console.log(isSmallScreen)
  return (
    <>
      <a id="team" className="scroll-mt-20" />
      <section className="pb-16 pt-16  relative bg-gray-50/40 dark:bg-gray-900/20 ">
        <div className="max-w-7xl mx-auto sm:px-4  lg:px-8 relative">
          {/* Subtle container with minimal styling */}
          <div
            className="relative ">


            <div className="container mx-auto">
              <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
                <div className="space-y-5 z-10 max-w-3xl">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, ease: 'easeOut' }}
                    viewport={{ once: true }}
                  >
                    <Headline className="text-foreground dark:text-white relative">
                      Meet Our Team
                    </Headline>
                  </motion.div>
                  <motion.p
                    className="text-muted-foreground intro-text dark:text-white text-lg md:text-xl max-w-3xl mx-auto"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.2, ease: 'easeOut' }}
                    viewport={{ once: true }}
                  >
                    The talented individuals behind our innovative SaaS product.
                  </motion.p>
                </div>
              </div>

              <motion.div
                className="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-2 xl:grid-cols-4"
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.1 }}
              >
                {isSmallScreen != null && teamMembers?.map((member, index) => (
                  <TeamMember
                    key={index}
                    {...member}
                    startX={isSmallScreen ? 0 : (index < 2 ? 300 * (1.5-index) : -300 * (index - 1.5))}
                  />
                ))}
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

function LinkedinIcon(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
      <rect width="4" height="12" x="2" y="9" />
      <circle cx="4" cy="4" r="2" />
    </svg>
  )
}
