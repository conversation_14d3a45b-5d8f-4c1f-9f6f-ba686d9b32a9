import type { Block } from 'payload'

import { FixedToolbarFeature, HeadingFeature, InlineToolbarFeature, lexicalEditor } from '@payloadcms/richtext-lexical'

/*
  {
        name: "<PERSON>",
        role: "Chairman",
        description: "<PERSON> brings a wealth of experience in leadership, investment and financial management to ekoIntelligence.  With a distinguished career spanning over three decades, including roles in investment management, audit, and corporate finance, <PERSON> is dedicated to steering us towards a future where businesses are held accountable for their social and ecological impact.",
        imageUrl: "/images/team/neilw.jpeg",
        linkedin: "https://www.linkedin.com/in/neil-wolstenholme-2aa00b59/",
    },
 */
export const Team: Block = {
  slug: 'team',
  interfaceName: 'TeamBlock',
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'teamMembers',
      type: 'array',

      fields: [
        {
          name: 'name',
          type: 'text',
          admin: {
            description: 'The name of the team member.',
          },
          required: true,
        },
        {
          name: 'role',
          type: 'text',
          admin: {
            description: 'The team member\'s role.',
          },
          required: true,
        },

        {
          name: 'description',
          type: 'richText',
          editor: lexicalEditor({
            features: ({ rootFeatures }) => {
              return [
                ...rootFeatures,
                HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                FixedToolbarFeature(),
                InlineToolbarFeature(),
              ]
            },
          }),
          required: true,
          label: false,
        },
        {
          name: 'media',
          type: 'upload',
          relationTo: 'media',
          admin: {
            description: 'The team member\'s image.',
          },
          required: true,
        },
        {
          name: 'url',
          type: 'text',
          admin: {
            description: 'The URL of the team member\'s LinkedIn profile.',
          },
          required:true
        },
      ],
    }],
  labels: {
    plural: 'Team Members',
    singular: 'Team Member',
  },

}
