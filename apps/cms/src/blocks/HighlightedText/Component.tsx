'use client'

import { cn } from '@/utilities/ui'
import React from 'react'
import { motion } from 'framer-motion'

import type { HighlightedTextBlock as HighlightedTextBlockProps } from '@/payload-types'
import { BlockWrapper } from '@/components/BlockWrapper'

type MaxWidthOption = 'small' | 'medium' | 'large' | 'full';
type AlignmentOption = 'left' | 'center' | 'right';
type StyleOption = 'gradient' | 'bordered' | 'floating' | 'glow' | 'accent-border' | 'quote' | 'glass' | 'spotlight' | 'angled';
type ColorThemeOption = 'brand' | 'blue' | 'purple' | 'amber' | 'teal' | 'rose' | 'grey' | 'clear';

export const HighlightedTextBlock: React.FC<HighlightedTextBlockProps> = (props) => {
  const {
    style = 'gradient' as StyleOption,
    textContent,
    textAlignment = 'center' as AlignmentOption,
    maxWidth = 'large' as MaxWidthOption,
    colorTheme = 'brand' as ColorThemeOption,
    // inset is not used with Framer Motion implementation
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    inset,
    animate = false
  } = props

  const maxWidthClasses: Record<MaxWidthOption, string> = {
    small: 'max-w-screen-sm',
    medium: 'max-w-screen-md',
    large: 'max-w-screen-lg',
    full: 'w-full',
  }

  const alignmentClasses: Record<AlignmentOption, string> = {
    left: 'text-left',
    center: 'text-center mx-auto',
    right: 'text-right ml-auto',
  }

  // Color themes
  const getColorClasses = (theme: ColorThemeOption): Record<string, string> => {
    const themeMap: Record<ColorThemeOption, Record<string, string>> = {
      brand: {
        primary: 'brand',
        secondary: 'brand-dark',
        light: 'brand-light',
        accent: 'brand-accent'
      },
      blue: {
        primary: 'blue-500',
        secondary: 'blue-700',
        light: 'blue-300',
        accent: 'cyan-400'
      },
      purple: {
        primary: 'purple-500',
        secondary: 'purple-700',
        light: 'purple-300',
        accent: 'fuchsia-400'
      },
      amber: {
        primary: 'amber-500',
        secondary: 'amber-700',
        light: 'amber-300',
        accent: 'orange-400'
      },
      teal: {
        primary: 'teal-500',
        secondary: 'teal-700',
        light: 'teal-300',
        accent: 'emerald-400'
      },
      rose: {
        primary: 'rose-500',
        secondary: 'rose-700',
        light: 'rose-300',
        accent: 'pink-400'
      },
      grey: {
        primary: 'neutral-400',
        secondary: 'neutral-600',
        light: 'neutral-300',
        accent: 'neutral-500'
      },
      clear: {
        primary: 'foreground',
        secondary: 'foreground',
        light: 'foreground',
        accent: 'foreground'
      }
    };

    return themeMap[theme];
  }

  // Style-specific base classes (without animation classes, since we use Framer Motion)
  const getStyleClasses = (): string => {
    const colors = getColorClasses(colorTheme as ColorThemeOption);

    // Special handling for the 'clear' theme which should match the page background
    if (colorTheme === 'clear') {
      switch (style as StyleOption) {
        case 'gradient':
          return `bg-background dark:bg-background border border-foreground/10 dark:border-foreground/20`
        case 'bordered':
          return `border-2 border-foreground/20 bg-background dark:border-foreground/30 dark:bg-background`
        case 'floating':
          return `bg-background border border-foreground/5 dark:bg-background dark:border-foreground/10`
        case 'glow':
          return `bg-background border border-foreground/20 dark:bg-background dark:border-foreground/30`
        case 'accent-border':
          return `border-l-4 border-l-foreground/30 border border-foreground/10 bg-background dark:border-foreground/20 dark:bg-background`
        case 'quote':
          return `italic border-l-8 border-l-foreground/20 pl-6 pr-8 py-6 bg-background dark:border-l-foreground/30 dark:bg-background`
        case 'glass':
          return `bg-background dark:bg-background border border-foreground/20 dark:border-foreground/30 shadow-sm backdrop-blur-sm`
        case 'spotlight':
          return `bg-background border border-foreground/10 dark:bg-background dark:border-foreground/20 overflow-hidden`
        case 'angled':
          return `bg-background border-foreground/10 border dark:bg-background dark:border-foreground/20 relative overflow-hidden`
        default:
          return `bg-background border border-foreground/10 dark:bg-background dark:border-foreground/20`
      }
    }

    // Standard handling for all other color themes
    switch (style as StyleOption) {
      case 'gradient':
        return `bg-gradient-to-br from-${colors.primary}/10 via-${colors.primary}/5 to-${colors.light}/10 border border-${colors.primary}/10 dark:from-${colors.primary}/15 dark:via-${colors.primary}/10 dark:to-${colors.light}/15 dark:border-${colors.primary}/20`
      case 'bordered':
        return `border-2 border-${colors.primary}/20 bg-${colors.primary}/5 dark:border-${colors.primary}/30 dark:bg-${colors.primary}/10`
      case 'floating':
        return `bg-gradient-to-tr from-${colors.primary}/5 via-transparent to-${colors.light}/5 border border-foreground/5 dark:from-${colors.primary}/10 dark:to-${colors.light}/10 dark:border-foreground/10`
      case 'glow':
        // shadowColors is now used in getShadowStyle instead
        return `bg-${colors.primary}/5 border border-${colors.primary}/20 dark:bg-${colors.primary}/15 dark:border-${colors.primary}/30`
      case 'accent-border':
        return `border-l-4 border-l-${colors.primary} border border-foreground/10 bg-foreground/5 dark:border-foreground/20 dark:bg-foreground/10`
      case 'quote':
        return `italic border-l-8 border-l-${colors.primary}/40 pl-6 pr-8 py-6 bg-foreground/5 dark:border-l-${colors.primary}/60 dark:bg-foreground/10`
      case 'glass':
        return `backdrop-blur-md bg-neutral-subtle/10 dark:bg-neutral-dark/20 border border-${colors.primary}/20 dark:border-${colors.primary}/30 shadow-xl`
      case 'spotlight':
        return `bg-foreground/5 border border-${colors.primary}/10 dark:bg-foreground/10 dark:border-${colors.primary}/20 overflow-hidden`
      case 'angled':
        return `bg-${colors.primary}/5 border-${colors.primary}/10 border dark:bg-${colors.primary}/15 dark:border-${colors.primary}/20 relative overflow-hidden`
      default:
        return `bg-gradient-to-br from-${colors.primary}/10 via-${colors.primary}/5 to-${colors.light}/10 border border-${colors.primary}/10 dark:from-${colors.primary}/15 dark:via-${colors.primary}/10 dark:to-${colors.light}/15 dark:border-${colors.primary}/20`
    }
  }

  // Dynamic decorative elements with Framer Motion
  const renderDecorativeElements = (): React.ReactNode => {
    if (!animate) return null;

    const styleType = style as StyleOption;
    const colors = getColorClasses(colorTheme as ColorThemeOption);

    // For clear theme, we'll still render decorative elements but with subtle effects
    const isClearTheme = colorTheme === 'clear';
    const opacityModifier = isClearTheme ? '0.4' : '1'; // Lower opacity for clear theme effects

    switch(styleType) {
      case 'floating':
        return (
          <>
            <motion.div
              className={`absolute -z-10 w-32 h-32 rounded-full bg-gradient-to-br from-${colors.primary}/20 to-${colors.primary}/5 blur-xl opacity-70 dark:from-${colors.primary}/30 dark:to-${colors.primary}/10`}
              style={{top: '-8%', right: '-5%'}}
              initial={{ y: 0 }}
              animate={{
                y: [0, -25, 0],
                x: [0, 15, 0],
                rotate: [0, 15, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className={`absolute -z-10 w-24 h-24 rounded-full bg-gradient-to-tr from-${colors.light}/20 to-transparent blur-lg opacity-60 dark:from-${colors.light}/30 dark:opacity-80`}
              style={{bottom: '8%', left: '-3%'}}
              initial={{ y: 0 }}
              animate={{
                y: [0, -20, 0],
                x: [0, -15, 0],
                rotate: [0, -10, 0],
                scale: [1, 1.15, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
            <motion.div
              className={`absolute -z-10 w-16 h-16 rounded-full bg-gradient-to-bl from-${colors.accent}/15 to-transparent blur-md opacity-50 dark:from-${colors.accent}/25`}
              style={{bottom: '20%', right: '10%'}}
              initial={{ y: 0 }}
              animate={{
                y: [0, 25, 0],
                x: [0, -10, 0],
                rotate: [0, 20, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </>
        );

      case 'quote':
        return (
          <>
            <motion.div
              className={`absolute top-6 left-6 text-${colors.primary}/20 text-7xl font-serif dark:text-${colors.primary}/30`}
              initial={{ opacity: 0.2, rotateZ: 0 }}
              animate={{
                opacity: [0.2, 0.3, 0.2],
                rotateZ: [0, 2, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              whileHover={{ opacity: 0.5, x: 2, y: -2, scale: 1.1 }}
            >
              &#8220;
            </motion.div>
            <motion.div
              className={`absolute bottom-6 right-6 text-${colors.primary}/10 text-7xl font-serif rotate-180 dark:text-${colors.primary}/20`}
              initial={{ opacity: 0, rotateZ: 180 }}
              animate={{
                opacity: [0.1, 0.2, 0.1],
                rotateZ: [180, 178, 180],
                scale: [1, 1.03, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
              whileHover={{ opacity: 0.4, x: -2, y: 2, scale: 1.08 }}
            >
              &#8220;
            </motion.div>
          </>
        );

      case 'spotlight':
        return (
          <>
            <motion.div
              className={`absolute inset-0 bg-gradient-to-tr from-${colors.primary}/30 to-transparent rounded-br-[40px] -z-10 opacity-30 dark:from-${colors.primary}/40 dark:opacity-40`}
              initial={{
                opacity: 0,
                backgroundPosition: '0% 0%'
              }}
              animate={{
                opacity: [0, 0.3, 0.25],
                backgroundPosition: ['0% 0%', '100% 100%', '80% 80%'],
                transition: {
                  duration: 3.5,
                  ease: "easeOut",
                  repeat: 1,
                  repeatType: "reverse"
                }
              }}
            />
            {/* Subtle light sweep */}
            <motion.div
              className={`absolute h-[200%] w-[120px] bg-${colors.primary}/20 dark:bg-${colors.primary}/30 blur-2xl -z-10 rotate-45`}
              style={{
                top: '-50%',
                left: '-120px',
                transformOrigin: 'center',
              }}
              initial={{
                opacity: 0,
                left: '-120px'
              }}
              animate={{
                opacity: [0, 0.5, 0],
                left: ['-120px', '110%'],
                rotate: [45, 40],
                transition: {
                  duration: 4,
                  ease: "easeInOut",
                  times: [0, 0.6, 1],
                  repeat: 1,
                  repeatType: "mirror"
                }
              }}
            />
            {/* Second light sweep (accent color) */}
            <motion.div
              className={`absolute h-[200%] w-[80px] bg-${colors.accent}/15 dark:bg-${colors.accent}/25 blur-2xl -z-10 rotate-45`}
              style={{
                top: '-20%',
                left: '-80px',
                transformOrigin: 'center',
              }}
              initial={{
                opacity: 0,
                left: '-80px'
              }}
              animate={{
                opacity: [0, 0.4, 0],
                left: ['-80px', '105%'],
                rotate: [45, 35],
                transition: {
                  duration: 4.5,
                  delay: 1,
                  ease: "easeInOut",
                  times: [0, 0.6, 1],
                  repeat: 1,
                  repeatType: "mirror"
                }
              }}
            />
            {/* Spotlight corner effect */}
            <motion.div
              className={`absolute bottom-0 right-0 w-[120px] h-[120px] bg-gradient-to-tr from-${colors.primary}/20 to-transparent rounded-tl-[80px] -z-10 dark:from-${colors.primary}/30`}
              initial={{
                opacity: 0,
                scale: 0.95,
              }}
              animate={{
                opacity: [0, 0.3, 0.2],
                scale: [0.95, 1.03, 1],
                transition: {
                  duration: 3,
                  ease: "easeInOut",
                  repeat: 1,
                  repeatType: "reverse"
                }
              }}
            />
          </>
        );

      case 'angled':
        return (
          <>
            <motion.div
              className={`absolute top-0 right-0 w-1/3 h-full bg-${colors.primary}/10 -skew-x-12 transform origin-top-right -z-10 dark:bg-${colors.primary}/20`}
              animate={{
                width: ['33%', '40%', '33%'],
                skewX: ['-12deg', '-15deg', '-12deg'],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              whileHover={{
                width: '50%',
                backgroundColor: `rgba(var(--${colors.primary})/0.2)`,
                skewX: '-20deg',
              }}
            />
            <motion.div
              className={`absolute bottom-0 left-0 w-1/2 h-1/3 bg-${colors.primary}/5 skew-x-12 transform origin-bottom-left -z-10 dark:bg-${colors.primary}/15`}
              animate={{
                height: ['33%', '40%', '33%'],
                skewX: ['12deg', '15deg', '12deg'],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }}
              whileHover={{
                height: '50%',
                backgroundColor: `rgba(var(--${colors.primary})/0.15)`,
                skewX: '20deg',
              }}
            />
            <motion.div
              className={`absolute top-[40%] right-[40%] w-5 h-5 rounded-full bg-${colors.primary}/30 blur-sm dark:bg-${colors.primary}/40`}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.5, 0.3],
                y: [0, -10, 0],
                x: [0, 10, 0],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </>
        );

      case 'gradient':
        return (
          <>
            <motion.div
              className={`absolute inset-0 bg-gradient-to-tr from-${colors.primary}/25 to-${colors.light}/25 -z-10 rounded-2xl dark:from-${colors.primary}/30 dark:to-${colors.light}/30`}
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '50% 50%', '80% 20%', '0% 0%'],
                transition: {
                  duration: 8,
                  times: [0, 0.2, 0.5, 0.7, 1],
                  ease: "easeInOut",
                  repeat: 2,
                  repeatType: "reverse",
                }
              }}
            />
            {/* Enhanced flowing gradient animation */}
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r from-${colors.primary}/15 via-${colors.accent}/15 to-${colors.light}/15 -z-10 rounded-2xl dark:from-${colors.primary}/20 dark:via-${colors.accent}/20 dark:to-${colors.light}/20`}
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                transition: {
                  duration: 10,
                  ease: "easeInOut",
                  repeat: 2,
                  repeatType: "reverse",
                }
              }}
            />
            {/* Accent gradient that shifts position */}
            <motion.div
              className={`absolute inset-0 bg-gradient-to-br from-transparent to-${colors.accent}/20 -z-10 rounded-2xl dark:to-${colors.accent}/30`}
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '0% 0%'],
                transition: {
                  duration: 6,
                  ease: "easeInOut",
                  times: [0, 0.5, 1],
                  repeat: 2,
                  repeatType: "reverse",
                }
              }}
            />
          </>
        );

      case 'bordered':
        return (
          <>
            <motion.div
              className={`absolute inset-0 bg-gradient-to-br from-${colors.primary}/5 to-${colors.light}/5 -z-10 rounded-2xl dark:from-${colors.primary}/10 dark:to-${colors.light}/10`}
              animate={{
                opacity: [0.2, 0.5, 0.2],
                scale: [1, 1.01, 1],
              }}
              transition={{
                duration: 8,
                ease: "easeInOut",
                repeat: Infinity,
              }}
            />
            <motion.div
              className={`absolute inset-[3px] border-2 border-dashed border-${colors.primary}/5 rounded-xl dark:border-${colors.primary}/10`}
              animate={{
                opacity: [0.2, 0.6, 0.2],
                rotate: [0, 1, 0],
                scale: [0.99, 1, 0.99],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className={`absolute h-8 w-8 bg-${colors.primary}/10 dark:bg-${colors.primary}/20 rounded-full blur-md -z-10`}
              style={{
                top: '10%',
                left: '5%',
              }}
              animate={{
                opacity: [0, 0.8, 0],
                left: ['5%', '95%'],
                top: ['10%', '90%'],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatDelay: 4,
                ease: "easeInOut",
              }}
            />
          </>
        );

      case 'glow':
        return (
          <>
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r from-${colors.primary}/15 via-${colors.light}/15 to-${colors.primary}/15 -z-10 rounded-2xl dark:from-${colors.primary}/25 dark:via-${colors.light}/20 dark:to-${colors.primary}/25 opacity-80 dark:opacity-90`}
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '50% 50%'],
                transition: {
                  duration: 10,
                  times: [0, 0.5, 1],
                  ease: "easeInOut",
                  repeat: 2,
                  repeatType: "reverse"
                }
              }}
            />
            {/* Glow effect using only box-shadow */}
            <motion.div
              className={`absolute inset-0 rounded-2xl -z-10 opacity-80 dark:opacity-90`}
              animate={{
                boxShadow: [
                  `0 0 5px rgba(var(--${colors.primary})/0.1)`,
                  `0 0 20px rgba(var(--${colors.primary})/0.25)`,
                  `0 0 15px rgba(var(--${colors.primary})/0.2)`,
                  `0 0 18px rgba(var(--${colors.primary})/0.22)`,
                  `0 0 10px rgba(var(--${colors.primary})/0.15)`,
                ],
                transition: {
                  duration: 12,
                  ease: "easeInOut",
                  times: [0, 0.3, 0.5, 0.7, 1],
                  repeat: 2,
                  repeatType: "mirror"
                }
              }}
            />
            {/* Animated border glow */}
            <motion.div
              className={`absolute inset-0 rounded-2xl border border-${colors.primary}/20 dark:border-${colors.primary}/40 -z-10`}
              animate={{
                borderColor: [
                  `rgba(var(--${colors.primary})/0.2)`,
                  `rgba(var(--${colors.primary})/0.4)`,
                  `rgba(var(--${colors.primary})/0.3)`,
                  `rgba(var(--${colors.primary})/0.35)`,
                  `rgba(var(--${colors.primary})/0.2)`,
                ],
                transition: {
                  duration: 8,
                  ease: "easeInOut",
                  times: [0, 0.2, 0.5, 0.7, 1],
                  repeat: 2,
                  repeatType: "mirror"
                }
              }}
            />
            {/* Background accent spots - fixed in place */}
            <div className={`absolute rounded-full bg-${colors.accent}/20 blur-lg w-24 h-24 -z-10 dark:bg-${colors.accent}/30 opacity-70`} style={{ bottom: '15%', right: '10%' }}></div>
            <div className={`absolute rounded-full bg-${colors.light}/20 blur-lg w-16 h-16 -z-10 dark:bg-${colors.light}/30 opacity-60`} style={{ top: '15%', left: '15%' }}></div>
          </>
        );

      case 'glass':
        return (
          <>
            <motion.div
              className={`absolute inset-0 bg-gradient-to-tr from-${colors.primary}/30 to-transparent -z-10 rounded-2xl dark:from-${colors.primary}/40 opacity-80 dark:opacity-90`}
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '50% 50%', '80% 20%', '30% 70%'],
                transition: {
                  duration: 15,
                  times: [0, 0.2, 0.5, 0.7, 1],
                  ease: "easeInOut",
                  repeat: 2,
                  repeatType: "reverse"
                }
              }}
            />
            <div className="absolute inset-0 overflow-hidden rounded-2xl -z-10 opacity-70">
              <motion.div
                className={`absolute -inset-[200%] bg-[radial-gradient(circle,_rgba(var(--${colors.primary})_/_0.35)_0%,_transparent_25%)] dark:bg-[radial-gradient(circle,_rgba(var(--${colors.primary})_/_0.45)_0%,_transparent_25%)]`}
                animate={{
                  backgroundPosition: ['0% 0%', '100% 100%', '50% 50%', '20% 80%', '70% 30%'],
                  transition: {
                    duration: 20,
                    times: [0, 0.2, 0.5, 0.7, 1],
                    ease: "easeInOut",
                    repeat: 2,
                    repeatType: "reverse"
                  }
                }}
              />
            </div>
            {/* Enhanced glass effect - fixed blur layer */}
            <div className="absolute inset-0 bg-white/25 dark:bg-black/25 backdrop-blur-lg rounded-2xl -z-10 opacity-70"></div>

            {/* Animated border glow */}
            <motion.div
              className={`absolute inset-0 rounded-2xl border border-${colors.primary}/30 dark:border-${colors.primary}/50 -z-10`}
              animate={{
                borderColor: [
                  `rgba(var(--${colors.primary})/0.3)`,
                  `rgba(var(--${colors.primary})/0.5)`,
                  `rgba(var(--${colors.primary})/0.4)`,
                  `rgba(var(--${colors.primary})/0.45)`,
                  `rgba(var(--${colors.primary})/0.3)`,
                ],
                boxShadow: [
                  `0 0 0px rgba(var(--${colors.primary})/0.1)`,
                  `0 0 15px rgba(var(--${colors.primary})/0.3)`,
                  `0 0 8px rgba(var(--${colors.primary})/0.2)`,
                  `0 0 12px rgba(var(--${colors.primary})/0.25)`,
                  `0 0 5px rgba(var(--${colors.primary})/0.15)`
                ],
                transition: {
                  duration: 14,
                  ease: "easeInOut",
                  times: [0, 0.2, 0.5, 0.7, 1],
                  repeat: 2,
                  repeatType: "mirror"
                }
              }}
            />

            {/* Glimmering sparkles - staggered appearance */}
            <div className="absolute inset-0 overflow-hidden rounded-2xl pointer-events-none">
              {[...Array(15)].map((_, i) => (
                <motion.div
                  key={i}
                  className={`absolute rounded-full bg-white/80 dark:bg-white/90`}
                  style={{
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    width: `${Math.max(1.5, Math.random() * 3)}px`,
                    height: `${Math.max(1.5, Math.random() * 3)}px`,
                  }}
                  initial={{
                    opacity: 0
                  }}
                  animate={{
                    opacity: [0, 0.8, 0],
                    transition: {
                      duration: 1 + Math.random() * 1,
                      delay: Math.random() * 5,
                      ease: "easeInOut",
                      repeat: 2,
                      repeatDelay: Math.random() * 5
                    }
                  }}
                />
              ))}
            </div>
          </>
        );

      case 'accent-border':
        return (
          <>
            <motion.div
              className={`absolute left-0 top-0 h-full w-1 bg-${colors.primary} dark:bg-${colors.primary}/80 -z-10`}
              animate={{
                height: ['100%', '70%', '100%'],
                y: ['0%', '15%', '0%'],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r from-${colors.primary}/10 to-transparent opacity-20 -z-10 dark:from-${colors.primary}/20`}
              animate={{
                opacity: [0.1, 0.2, 0.1],
                backgroundPosition: ['0% 0%', '100% 0%', '0% 0%']
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </>
        );

      default:
        return null;
    }
  }

  // Ensure maxWidth and textAlignment have valid values for indexing
  const validMaxWidth = maxWidth || 'large' as MaxWidthOption;
  const validTextAlignment = textAlignment || 'center' as AlignmentOption;
  const validStyle = style || 'gradient' as StyleOption;
  const validColorTheme = colorTheme || 'brand' as ColorThemeOption;
  const colors = getColorClasses(validColorTheme);

  // Motion variants for the container - movement only, no opacity change
  const containerVariants = {
    hidden: { y: 20 },
    visible: {
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  // Border animation only for the container
  const containerAnimateProps = animate ? {
    borderColor: ['rgba(var(--foreground)/0.05)', 'rgba(var(--foreground)/0.1)', 'rgba(var(--foreground)/0.07)', 'rgba(var(--foreground)/0.05)'],
    transition: {
      duration: 8,
      times: [0, 0.3, 0.7, 1],
      ease: "easeInOut",
      repeat: 2,
      repeatType: "reverse"
    }
  } : {};

  // Shadow variants with continuous subtle animation
  const getShadowStyle = () => {
    if (!animate) return {};

    const lightShadowMap = {
      'brand': 'rgba(0,100,80,0.3)',
      'blue': 'rgba(59,130,246,0.3)',
      'purple': 'rgba(168,85,247,0.3)',
      'amber': 'rgba(245,158,11,0.3)',
      'teal': 'rgba(20,184,166,0.3)',
      'rose': 'rgba(244,63,94,0.3)',
      'grey': 'rgba(161,161,170,0.3)',
      'clear': 'rgba(100,100,100,0.2)'
    };

    const darkShadowMap = {
      'brand': 'rgba(0,180,150,0.5)',
      'blue': 'rgba(59,130,246,0.5)',
      'purple': 'rgba(168,85,247,0.5)',
      'amber': 'rgba(245,158,11,0.5)',
      'teal': 'rgba(20,184,166,0.5)',
      'rose': 'rgba(244,63,94,0.5)',
      'grey': 'rgba(161,161,170,0.5)',
      'clear': 'rgba(150,150,150,0.3)'
    };

    // Detect if we're in dark mode using a media query
    const isDarkMode = typeof window !== 'undefined' ? window.matchMedia('(prefers-color-scheme: dark)').matches : false;
    const shadowMap = isDarkMode ? darkShadowMap : lightShadowMap;

    // Special handling for the clear theme
    if (validColorTheme === 'clear') {
      if (validStyle === 'glow') {
        return {
          boxShadow: isDarkMode
            ? `0 0 15px rgba(150, 150, 150, 0.25)`
            : `0 0 15px rgba(100, 100, 100, 0.15)`,
        };
      }

      if (validStyle === 'glass') {
        return {
          boxShadow: isDarkMode
            ? `0 8px 20px rgba(0, 0, 0, 0.25)`
            : `0 8px 15px rgba(0, 0, 0, 0.1)`,
        };
      }

      if (!['accent-border', 'quote'].includes(validStyle)) {
        return {
          boxShadow: isDarkMode
            ? `0 6px 15px rgba(0, 0, 0, 0.25)`
            : `0 6px 15px rgba(0, 0, 0, 0.08)`,
        };
      }

      return {};
    }

    // Enhanced shadow values for one-time animations
    if (validStyle === 'glow') {
      return {
        boxShadow: `0 0 30px ${shadowMap[validColorTheme].replace(isDarkMode ? '0.5' : '0.3', isDarkMode ? '0.6' : '0.4')}`,
        // The additional animation is handled by the motion.div in the renderDecorativeElements function
      };
    }

    if (validStyle === 'glass') {
      return {
        boxShadow: isDarkMode
          ? `0 10px 30px rgba(0, 0, 0, 0.4), 0 0 20px ${shadowMap[validColorTheme].replace(isDarkMode ? '0.5' : '0.3', '0.25')}`
          : `0 10px 25px rgba(0, 0, 0, 0.15), 0 0 15px ${shadowMap[validColorTheme].replace(isDarkMode ? '0.5' : '0.3', '0.2')}`,
      };
    }

    if (validStyle === 'spotlight') {
      return {
        boxShadow: isDarkMode
          ? `0 10px 30px rgba(0, 0, 0, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.1)`
          : `0 10px 25px rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.2)`,
      };
    }

    if (!['accent-border', 'quote'].includes(validStyle)) {
      return {
        boxShadow: isDarkMode
          ? `0 8px 25px rgba(0, 0, 0, 0.35), 0 4px 10px rgba(0, 0, 0, 0.2)`
          : `0 8px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.05)`,
      };
    }

    return {};
  };

  // Border style variants with continuous subtle pulsing
  const getBorderStyles = () => {
    if (!animate) return {};

    const isDarkMode = typeof window !== 'undefined' ? window.matchMedia('(prefers-color-scheme: dark)').matches : false;

    if (validStyle === 'accent-border') {
      return {
        borderColor: `rgba(var(--${colors.primary})/${isDarkMode ? '0.2' : '0.1'})`,
        // No need for hover styles as we're using continuous animations
      };
    }

    if (validStyle === 'quote') {
      return {
        borderLeftColor: `rgba(var(--${colors.primary})/${isDarkMode ? '0.5' : '0.4'})`,
      };
    }

    if (validStyle !== 'glow' && validStyle !== 'glass') {
      return {
        borderColor: `rgba(var(--${colors.primary})/${isDarkMode ? '0.15' : '0.1'})`,
      };
    }

    return {};
  };

  return (
    <BlockWrapper noPadding className="sm:px-6 md:px-16 relative" sectionSpacingClassName="section-spacing-xs">
      <motion.div
        className={cn(
          maxWidthClasses[validMaxWidth],
          alignmentClasses[validTextAlignment],
          "relative rounded-xl p-4 sm:p-10 md:p-14 backdrop-blur-[2px] group shadow-lg",
          getStyleClasses()
        )}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        style={{
          ...getShadowStyle(),
          borderColor: getBorderStyles().borderColor
        }}
      >
        {/* Enhanced noise texture for greater depth */}
        <div className="absolute inset-0 bg-noise opacity-[0.04] mix-blend-overlay pointer-events-none rounded-xl"></div>

        {/* Additional subtle patterns */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_107%,rgba(255,255,255,0.02)_0%,rgba(255,255,255,0)_75%)] dark:bg-[radial-gradient(circle_at_30%_107%,rgba(255,255,255,0.03)_0%,rgba(255,255,255,0)_75%)] pointer-events-none rounded-xl"></div>

        {/* Subtle top highlight */}
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-foreground/10 dark:via-foreground/15 to-transparent"></div>

        {/* Decorative elements */}
        {renderDecorativeElements()}

        {/* Main content with enhanced typography */}
        <motion.div
          className="relative z-10"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.5,
            delay: 0.2,
            ease: "easeOut"
          }}
        >
          {textContent && (
            <div
              className={`prose prose-lg md:prose-xl lg:prose-xl text-foreground dark:text-foreground/95
                         ${colorTheme === 'clear' || colorTheme === 'grey'
                           ? `prose-strong:text-foreground dark:prose-strong:text-foreground/95 prose-strong:font-semibold
                              prose-headings:text-foreground dark:prose-headings:text-foreground/95 prose-headings:font-bold
                              prose-p:leading-relaxed prose-p:opacity-95 dark:prose-p:opacity-95
                              prose-a:text-brand dark:prose-a:text-brand hover:prose-a:text-brand-dark dark:hover:prose-a:text-brand-light hover:prose-a:underline
                              prose-code:bg-foreground/10 dark:prose-code:bg-foreground/20 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md prose-code:text-brand-dark dark:prose-code:text-brand-light`
                           : `prose-strong:text-${colors.primary} dark:prose-strong:text-${colors.light} prose-strong:font-semibold
                              prose-headings:text-${colors.secondary} dark:prose-headings:text-${colors.light} prose-headings:font-bold
                              prose-p:leading-relaxed prose-p:opacity-95 dark:prose-p:opacity-95
                              prose-a:text-${colors.primary} dark:prose-a:text-${colors.light} hover:prose-a:text-${colors.accent} hover:prose-a:underline
                              prose-code:bg-${colors.primary}/15 dark:prose-code:bg-${colors.primary}/25 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md`
                         }
                         max-w-none`}
            >
              <p className="!leading-relaxed tracking-wide">{textContent}</p>
            </div>
          )}
        </motion.div>
      </motion.div>
    </BlockWrapper>
  )
}
