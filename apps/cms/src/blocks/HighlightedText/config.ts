import type { Block } from 'payload'

import { FixedToolbarFeature, HeadingFeature, InlineToolbarFeature, lexicalEditor } from '@payloadcms/richtext-lexical'

export const HighlightedText: Block = {
  slug: 'highlightedText',
  interfaceName: 'HighlightedTextBlock',
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: true,
    },
    {
      name: 'animate',
      type: 'checkbox',
      label: 'Enable animations',
      defaultValue: false,
    },
    {
      name: 'style',
      type: 'select',
      defaultValue: 'gradient',
      options: [
        {
          label: 'Gradient Background',
          value: 'gradient',
        },
        {
          label: 'Bordered Card',
          value: 'bordered',
        },
        {
          label: 'Floating Elements',
          value: 'floating',
        },
        {
          label: 'Glow Effect',
          value: 'glow',
        },
        {
          label: 'Accent Border',
          value: 'accent-border',
        },
        {
          label: 'Quote Style',
          value: 'quote',
        },
        {
          label: 'Glass Morphism',
          value: 'glass',
        },
        {
          label: 'Spotlight',
          value: 'spotlight',
        },
        {
          label: 'Angled Background',
          value: 'angled',
        },
      ],
    },
    {
      name: 'textContent',
      type: 'textarea',
      required: true,
    },
    {
      name: 'colorTheme',
      type: 'select',
      defaultValue: 'brand',
      options: [
        {
          label: 'Brand (Green)',
          value: 'brand',
        },
        {
          label: 'Blue',
          value: 'blue',
        },
        {
          label: 'Purple',
          value: 'purple',
        },
        {
          label: 'Amber',
          value: 'amber',
        },
        {
          label: 'Teal',
          value: 'teal',
        },
        {
          label: 'Rose',
          value: 'rose',
        },
        {
          label: 'Grey (Neutral)',
          value: 'grey',
        },
        {
          label: 'Clear (Background Color)',
          value: 'clear',
        },
      ],
    },
    {
      name: 'textAlignment',
      type: 'select',
      defaultValue: 'center',
      options: [
        {
          label: 'Left',
          value: 'left',
        },
        {
          label: 'Center',
          value: 'center',
        },
        {
          label: 'Right',
          value: 'right',
        },
      ],
    },
    {
      name: 'maxWidth',
      type: 'select',
      defaultValue: 'large',
      options: [
        {
          label: 'Small (640px)',
          value: 'small',
        },
        {
          label: 'Medium (768px)',
          value: 'medium',
        },
        {
          label: 'Large (1024px)',
          value: 'large',
        },
        {
          label: 'Full Width',
          value: 'full',
        },
      ],
    },
  ],
}