import React from 'react'
import { IFeatureBlock, Media } from '@/payload-types'
import { Headline } from '@ui/components/front-page/headline'
import { cn } from '@utils/lib/utils'
import Image from 'next/image'
import RichText from '@/components/RichText'
import { Roboto } from 'next/font/google'
import { BlockWrapper } from '@/components/BlockWrapper'

const headlineFont = Roboto({
  subsets: ['latin'],
  weight: ['400', '700'], // specify the weights you need
})

export function FeaturesBlock({ heading, features, background, backgroundMedia, inset }: IFeatureBlock) {
  return (
    <BlockWrapper
      background={background}
      backgroundMedia={backgroundMedia as Media}
      inset={inset}
      darkMode={true}
      subtle={true}
      noPadding={false}
      enableParallax={true}
      parallaxIntensity={0.05}
    >
      {heading && (
        <div className="flex flex-col items-center justify-center text-center max-w-4xl mx-auto mb-12">
          <Headline>
            {heading}
          </Headline>
        </div>
      )}

      <div className="md:space-y-16">

          <div
            className={cn('mx-auto grid items-stretch gap-4 sm:max-w-4xl sm:grid-cols-1 md:grid-cols-2 md:gap-12 lg:max-w-6xl lg:grid-cols-3 ',
              backgroundMedia ? 'text-neutral-50' : 'text-foreground')}>
            {features?.map((feature, index) => (
              <div
                key={index}
                className={cn('group h-full relative overflow-visible flex flex-col p-4 sm:p-8  sm:rounded-xl sm:hover-scale z-10  transform-gpu',
                  backgroundMedia ? 'sm:glass-effect-lit' : 'sm:glass-effect-lit')}
              >
                {/* Enhanced glow effect on hover */}
                <div className="absolute -inset-1 bg-gradient-to-br from-brand/0 to-brand/0 group-hover:from-brand/5 group-hover:to-transparent standard-rounding -z-20 blur-xl transition-colors duration-500" style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}></div>

                {/* Subtle noise texture */}
                <div className="absolute inset-0 bg-noise opacity-[0.02] mix-blend-overlay pointer-events-none  standard-rounding"></div>

                {/*/!* Decorative elements *!/*/}
                {/*<div className="absolute -z-10 w-24 h-24 rounded-full bg-gradient-to-br from-brand/5 to-transparent blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 ease-in-out transform translate-x-4 -translate-y-4 group-hover:translate-x-0 group-hover:-translate-y-0"*/}
                {/*     style={{top: '-5%', right: '-10%'}}></div>*/}

                <div className="mb-6 relative">
                  {feature.icon && (
                    <div className="p-4 mb-5  standard-rounding bg-background/10 inline-block
                                  border border-foreground/5 shadow-md transition-all duration-500 group-hover:shadow-lg
                                  group-hover:bg-background/15 group-hover:border-brand/10 relative overflow-hidden"
                         style={{ willChange: 'transform, box-shadow', transform: 'translateZ(0)' }}>
                      {/* Icon background glow effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-brand/0 to-transparent group-hover:from-brand/10 group-hover:to-transparent transition-colors duration-500 opacity-0 group-hover:opacity-100" style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}></div>

                      <Image
                        width={48}
                        height={48}
                        src={(feature.icon as Media).url || '#'}
                        alt={(feature.icon as Media).alt || feature.title}
                        className="w-12 h-12 transition-all duration-500 group-hover:scale-110 group-hover:brightness-110 relative z-10"
                        style={{ willChange: 'transform', transform: 'translateZ(0)' }}
                      />
                    </div>
                  )}
                  <h3 className={cn('text-xl lg:text-2xl font-bold mb-3 transition-colors duration-300', headlineFont.className)}>
                    {feature.title}
                  </h3>
                </div>

                {feature.description && !feature.richTextDescription && (
                  <p className={cn('text-md lg:text-md opacity-80 group-hover:opacity-100 transition-opacity duration-300',
                    backgroundMedia ? 'text-neutral-100' : 'text-foreground/90')}>
                    {feature.description}
                  </p>
                )}

                {feature.richTextDescription && (
                  <div className={cn('text-md lg:text-md opacity-90 group-hover:opacity-100 transition-opacity duration-300 prose-a:text-white', backgroundMedia ? 'prose-a:text-neutral-100' : 'prose-a:text-foreground/90')}>
                    <RichText
                      data={feature.richTextDescription}
                      enableProse={true}
                      enableGutter={false}
                      className=""
                    />
                  </div>
                )}


              </div>
            ))}
          </div>
        </div>
    </BlockWrapper>
  )
}

export default FeaturesBlock
