import { Block } from 'payload'

const Features: Block = {
  slug: 'features',
  interfaceName:"IFeatureBlock",
  labels: {
    singular: 'Features Section',
    plural: 'Features Sections',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'heading',
      type: 'text',
      label: 'Section Heading',
      required: false,
    },
    {name:'background', type:'checkbox', label:'Background Effect', defaultValue:false},
    {
      name: 'backgroundMedia',
      type: 'upload',
      label: 'Background Media',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'features',
      type: 'array',
      label: 'Features',
      fields: [
        {
          name: 'title',
          type: 'text',
          label: 'Feature Title',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Feature Description (deprecated: use Rich Text Feature Description)',
        },
        {
          name: 'richTextDescription',
          type: 'richText',
          label: 'Rich Text Feature Description',
        },
        {
          name: 'icon',
          type: 'upload',
          label: 'Feature Icon',
          relationTo: 'media', // Adjust if you have a media collection
        },
      ],
    },
  ],
};

export default Features;
