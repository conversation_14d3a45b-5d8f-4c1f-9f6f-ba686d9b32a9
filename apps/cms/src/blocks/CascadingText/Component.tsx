'use client'

import React, { CSSProperties, useEffect } from 'react'
import { cn } from '@utils/lib/utils'
import { BlockWrapper } from '@/components/BlockWrapper'
import RichText from '@/components/RichText'
import { motion } from 'framer-motion'

import { Media } from '@/payload-types'
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

// Define the props interface based on the config fields
interface CascadingTextBlockProps {
  inset?: boolean | null
  background?: boolean | null
  backgroundMedia?: Media | null
  darkMode?: boolean | null
  title?: string | null
  subtitle?: string | null
  panels?: {
    title?: string | null
    content?: SerializedEditorState | null
    isCentered?: boolean | null
    isHighlighted?: boolean | null
  }[]
}

const CascadingTextBlock: React.FC<CascadingTextBlockProps> = ({
  inset = false,
  background = true,
  backgroundMedia = null,
  darkMode = false,
  title,
  subtitle,
  panels = [],
}) => {
  const [isSmall, setIsSmall] = React.useState<boolean|null>(null)
  useEffect(() => {
    setIsSmall(matchMedia('(max-width: 767px)').matches);
  }, [])

  return (
    <BlockWrapper
      inset={inset}
      background={background}
      backgroundMedia={backgroundMedia}
      darkMode={darkMode}
      enableParallax={false}
      noPadding={true}
      subtle
      className="bg-brand-gradient"
      colorScheme = {{
        '--aurora-color-1': 'hsl(55 27% 95%)',
        '--aurora-color-2': 'hsl(55 27% 85%)',
        '--aurora-color-3': 'hsl(55 27% 90%)',
        '--aurora-color-4': 'hsl(55 27% 85%)',
        '--aurora-color-5': 'hsl(55 27% 90%)',
        '--black': 'rgba(0,0,0,0.9)',
        '--white': '#fff',
        '--transparent': 'transparent',
      } as CSSProperties}
      showRadialGradient={true}
    >
      {(title || subtitle) && (
        <div className="text-center mb-16 max-w-3xl mx-auto px-4 md:pb-8 ">
          {title && <h1 className="text-3xl md:text-4xl font-bold mb-4 relative z-20 text-white/80">{title}</h1>}
          {subtitle && <h3 className="sm:text-2xl text-white opacity-80 italic px-2 mb-4 sm:px-6 relative">{subtitle}</h3>}
        </div>
      )}
      <div>

        <div className="sm:flex sm:flex-col sm:items-start sm:relative sm:container-medium sm:mx-auto sm:px-4 md:px-6">
          {panels && panels.map((panel, index) => (
            <motion.div
              key={index}
              initial={{ opacity: isSmall ? 1 : 0, y: isSmall ? 0 : -40, }}
              whileInView={{ opacity: 1, y: 0, x: isSmall ? 0 : (index % 2 === 0 ? -48 : 48) }}
              viewport={{ once: true, amount: 0.01 }}
              transition={!isSmall ? {
                duration: 0.3,
                delay: index * 0.2,
                ease: [0.2, 0, 0.2, 1]
              }:{duration:0}}
              className={cn(
                "text-black/80 md:text-white relative mb-0 last:mb-0 group transition-all duration-800 standard-rounding z-[1]",
                "md:transform-gpu md:p-6 md:transition-all md:duration-600 md:ease-[cubic-bezier(0.175,0.885,0.32,1.275)]",
                "md:backface-visible md:origin-center md:overflow-hidden md:-mt-8 md:last:mb-0",
                "md:hover:z-10 md:hover:shadow-[0_15px_30px_rgba(0,0,0,0.2)]",
                "md:hover:glass-effect-brand-strong-lit",
                index % 2 === 0
                  ? "md:ml-0 md:rotate-[5deg] md:translate-z-0 md:glass-effect-brand-medium-lit"
                  : "md:mr-0 md:-rotate-[5deg] md:translate-z-0 md:glass-effect-brand-medium-lit",
                panel.isCentered && "text-center",
                panel.isHighlighted && "z-10",
                "max-md:p-4"
              )}
            >
              {panel.title && (
                <h3 className="heading-3 text-slate-900/80 text-center mb-2">
                  {panel.title}
                </h3>
              )}

              {panel.content && (
                <div className="px-4 text-lg text-slate-900/80  leading-relaxed">
                  <RichText
                    data={panel.content}
                    enableGutter={false}
                    className={cn(
                      "",
                      "prose-strong:font-semibold prose-p:text-slate-900/80 "
                    )}
                  />
                </div>
              )}

            </motion.div>
          ))}
        </div>
      </div>
    </BlockWrapper>
  )
}

export default CascadingTextBlock
