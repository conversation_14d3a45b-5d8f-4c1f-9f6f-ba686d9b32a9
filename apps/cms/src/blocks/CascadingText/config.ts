import { Block } from 'payload'

const CascadingText: Block = {
  slug: 'cascadingText',
  interfaceName: 'ICascadingTextBlock',
  labels: {
    singular: 'Cascading Text Section',
    plural: 'Cascading Text Sections',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'background',
      type: 'checkbox',
      label: 'Background Effect',
      defaultValue: true,
    },
    {
      name: 'backgroundMedia',
      type: 'upload',
      label: 'Background Media',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'darkMode',
      type: 'checkbox',
      label: 'Dark Mode',
      defaultValue: false,
    },
    {
      name: 'title',
      type: 'text',
      label: 'Section Title',
      required: false,
    },
    {
      name: 'subtitle',
      type: 'textarea',
      label: 'Section Subtitle',
      required: false,
    },
    {
      name: 'panels',
      type: 'array',
      label: 'Text Panels',
      minRows: 1,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/blocks/CascadingText/RowLabel',
        },
      },
      fields: [
        {
          name: 'title',
          type: 'text',
          label: 'Panel Title',
          required: false,
        },
        {
          name: 'content',
          type: 'richText',
          label: 'Panel Content',
          required: true,
        },
        {
          name: 'isCentered',
          type: 'checkbox',
          label: 'Center Text',
          defaultValue: false,
        },
        {
          name: 'isHighlighted',
          type: 'checkbox',
          label: 'Highlight Panel (for final/important panels)',
          defaultValue: false,
        },
      ],
    },
  ],
}

export default CascadingText
