import { Block } from 'payload'

const FAQ: Block = {
  slug: 'faq',
  interfaceName: "IFAQBlock",
  labels: {
    singular: 'FAQ Section',
    plural: 'FAQ Sections',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'heading',
      type: 'text',
      label: 'Section Heading',
      required: false,
    },
    {
      name: 'subheading',
      type: 'text',
      label: 'Section Subheading',
      required: false,
    },
    {
      name: 'background',
      type: 'checkbox',
      label: 'Background Effect',
      defaultValue: false
    },
    {
      name: 'backgroundMedia',
      type: 'upload',
      label: 'Background Media',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'columnLayout',
      type: 'select',
      label: 'Column Layout',
      defaultValue: 'single',
      options: [
        {
          label: 'Single Column',
          value: 'single',
        },
        {
          label: 'Two Columns',
          value: 'double',
        }
      ],
    },
    {
      name: 'faqItems',
      type: 'array',
      label: 'FAQ Items',
      required: true,
      fields: [
        {
          name: 'question',
          type: 'text',
          label: 'Question',
          required: true,
        },
        {
          name: 'answer',
          type: 'richText',
          label: 'Answer',
          required: true,
        },
      ],
    },
  ],
};

export default FAQ;