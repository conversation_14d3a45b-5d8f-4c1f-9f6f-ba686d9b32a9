'use client'

import React from 'react'
import { IFAQBlock, Media } from '@/payload-types'
import { Headline } from '@ui/components/front-page/headline'
import { cn } from '@utils/lib/utils'
import RichText from '@/components/RichText'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@ui/components/ui/accordion'
import { BlockWrapper } from '@/components/BlockWrapper'

export function FAQBlock({
  heading,
  subheading,
  faqItems,
  background,
  backgroundMedia,
  columnLayout = 'single'
}: IFAQBlock) {
  return (
    <BlockWrapper
      className="border-t border-b border-foreground/5"
      background={background}
      backgroundMedia={backgroundMedia as Media}
      darkMode={!!backgroundMedia}
      subtle={true}
    >
      <div className="space-y-12">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            {heading && (
              <div className="z-10 max-w-4xl mx-auto relative">

                {/* Enhanced headline styling */}
                <div className="relative">
                  <Headline
                    className={cn(
                      "relative z-10",
                      backgroundMedia ? "text-background drop-shadow-sm" : "text-foreground"
                    )}
                  >
                    {heading}
                  </Headline>
                </div>
              </div>
            )}

            {subheading && (
              <p className={cn(
                "text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed font-light",
                backgroundMedia ? "text-background/90" : "text-foreground/70"
              )}>
                {subheading}
              </p>
            )}
          </div>

          {faqItems && faqItems.length > 0 && (
            <div className={cn(
              "max-w-6xl mx-auto",
              columnLayout === 'double' ? "md:columns-2 md:gap-12" : ""
            )}>
              <Accordion type="single" collapsible className="w-full">
                {faqItems.map((item, index) => (
                  <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className={cn(
                      "backdrop-blur-sm rounded-xl overflow-hidden mb-6 group",
                      "bg-background/5 dark:bg-background/10 hover:bg-background/10 dark:hover:bg-background/15",
                      "border border-foreground/10 dark:border-foreground/5 hover:border-brand/10",
                      "transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                    )}
                  >
                    {/* Enhanced subtle decorative elements */}
                    <div className="absolute inset-0 bg-gradient-to-br from-brand/0 to-brand/0 group-hover:from-brand/5 group-hover:to-transparent rounded-xl -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>

                    <AccordionTrigger className={cn(
                      "px-8 py-5 text-lg md:text-xl font-medium group flex",
                      "hover:no-underline hover:bg-background/5 dark:hover:bg-background/5",
                      "transition-all duration-300 group-hover:text-brand",
                      backgroundMedia ? "text-background" : "text-foreground"
                    )}>
                      <div className="flex items-center">
                        <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-3 group-hover:mr-3 transition-all duration-300 opacity-0 group-hover:opacity-100"></span>
                        {item.question}
                      </div>
                    </AccordionTrigger>

                    <AccordionContent className={cn(
                      "px-8 pb-6 text-base",
                      backgroundMedia ? "text-background/90" : "text-foreground/80"
                    )}>
                      <div className="prose prose-sm md:prose dark:prose-invert max-w-none border-l-2 border-brand/20 pl-4 ml-3">
                        <RichText
                          data={item.answer}
                          enableProse={true}
                          enableGutter={false}
                          className="prose-a:text-brand prose-a:no-underline hover:prose-a:underline"
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )}
        </div>
    </BlockWrapper>
  );
}

export default FAQBlock;
