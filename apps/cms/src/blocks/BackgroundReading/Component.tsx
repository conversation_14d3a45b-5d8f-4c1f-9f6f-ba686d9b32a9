import React from 'react'
import { QuoteCard } from '@ui/components/front-page/quote-card'
import { IBackgroundReadingBlock, Media } from '@/payload-types'
import { Headline } from '@ui/components/front-page/headline'
import { AuroraBackground } from '@/components/eko/aurora'

export function BackgroundReadingBlock({
                                         tag,
                                         heading,
                                         description,
                                         quotes,
                                         background,
                                       }: IBackgroundReadingBlock) {
  return (
    <>
      <a id="background-reading"></a>
      <div className="w-full h-full relative bg-brand-gradient">
      <AuroraBackground animated subtle show={background||false} className="relative" parallax={true} parallaxIntensity={0.05}>
        <section className="container pt-8 pb-12 md:pb-14 lg:pb-16 z-10">
          <div className="mx-auto space-y-12 px-4 md:px-6">
            <div className="flex flex-col  space-y-4 text-center">
              <div className="space-y-2">
                {/*<div className="inline-block rounded-lg px-3 py-1 text-sm opacity-80">*/}
                {/*  {tag}*/}
                {/*</div>*/}
                <Headline>{heading}</Headline>
                <div
                  className=" w-full intro-text">
                  {description}
                </div>
              </div>
            </div>
            <div
              className="grid mx-auto justify-center items-center gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
              {quotes?.map((quote, index) => (
                <QuoteCard
                  key={index}
                  title={quote.title}
                  author={quote.author}
                  text={quote.text}
                  context={quote.context || ''}
                  image={quote.image ? (quote.image as Media).url! : ''}
                  link={quote.link || '#'}
                />
              ))}
            </div>
          </div>
        </section>
      </AuroraBackground>
      </div>
    </>
  )
}
