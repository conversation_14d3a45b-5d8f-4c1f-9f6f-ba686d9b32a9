import { Block } from 'payload'

const BackgroundReading: Block = {
  slug: 'background-reading',
  interfaceName: 'IBackgroundReadingBlock',
  labels: {
    singular: 'Background Reading Section',
    plural: 'Background Reading Sections',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'tag',
      type: 'text',
      label: 'Tag Label',
      required: true,
    },
    {
      name: 'heading',
      type: 'text',
      label: 'Heading',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      required: true,
    },
    {name:'background', type:'checkbox', label:'Background', defaultValue:false},
    {
      name: 'quotes',
      type: 'array',
      label: 'Quote Cards',
      fields: [
        {
          name: 'title',
          type: 'text',
          label: 'Quote Title',
          required: true,
        },
        {
          name: 'author',
          type: 'text',
          label: 'Author',
          required: true,
        },
        {
          name: 'text',
          type: 'textarea',
          label: 'Quote Text',
          required: true,
        },
        {
          name: 'context',
          type: 'text',
          label: 'Context',
        },
        {
          name: 'image',
          type: 'upload',
          label: 'Image',
          relationTo: 'media',
        },
        {
          name: 'link',
          type: 'text',
          label: 'Link URL',
        },
      ],
    },
  ],
};

export default BackgroundReading;
