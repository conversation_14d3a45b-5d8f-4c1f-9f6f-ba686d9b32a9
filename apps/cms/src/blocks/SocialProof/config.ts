import { Block } from 'payload'

const SocialProof: Block = {
  slug: 'socialProof',
  interfaceName: "ISocialProofBlock",
  labels: {
    singular: 'Social Proof Section',
    plural: 'Social Proof Sections',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'heading',
      type: 'text',
      label: 'Section Heading',
      required: false,
    },
    {
      name: 'subheading',
      type: 'text',
      label: 'Section Subheading',
      required: false,
    },
    {
      name: 'background',
      type: 'checkbox',
      label: 'Background Effect',
      defaultValue: false
    },
    {
      name: 'backgroundMedia',
      type: 'upload',
      label: 'Background Media',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'monochrome',
      type: 'checkbox',
      label: 'Make logos monochrome',
      defaultValue: true,
    },
    {
      name: 'logos',
      type: 'array',
      label: 'Logos',
      fields: [
        {
          name: 'logo',
          type: 'upload',
          label: 'Logo Image',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'name',
          type: 'text',
          label: 'Company/Organization Name',
          required: true,
        },
        {
          name: 'url',
          type: 'text',
          label: 'Website URL (optional)',
          required: false,
        },
      ],
    },
  ],
};

export default SocialProof;