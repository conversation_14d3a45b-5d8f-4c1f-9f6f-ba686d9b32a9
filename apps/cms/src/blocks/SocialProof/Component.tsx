'use client'

import React from 'react'
import { ISocialProofBlock, Media } from '@/payload-types'
import { Headline } from '@ui/components/front-page/headline'
import { cn } from '@utils/lib/utils'
import Image from 'next/image'
import { BlockWrapper } from '@/components/BlockWrapper'

export function SocialProofBlock({ heading, subheading, logos, background, backgroundMedia, monochrome = true }: ISocialProofBlock) {
  // Duplicate the logos multiple times to ensure a seamless infinite loop
  const scrollableLogos = logos ? [...logos, ...logos, ...logos, ...logos] : [];

  return (
    <BlockWrapper
      className="border-t border-b border-foreground/5 overflow-hidden "
      background={background}
      backgroundMedia={backgroundMedia as Media}
      darkMode={!!backgroundMedia}
      subtle={true}
      sectionSpacingClassName="section-spacing-small"
    >
      <div className="md:space-y-16">
        {(heading || subheading) && (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            {heading && (
              <div className="space-y-2 z-10 max-w-4xl mx-auto">
                <Headline className={cn(backgroundMedia ? "text-background" : "text-foreground")}>
                  {heading}
                </Headline>
              </div>
            )}

            {subheading && (
              <p className={cn(
                "text-xl md:text-2xl max-w-3xl mx-auto",
                backgroundMedia ? "text-background/90" : "text-foreground/70"
              )}>
                {subheading}
              </p>
            )}
          </div>)}

          {logos && logos.length > 0 && (
            <div className="relative w-full overflow-hidden before:absolute before:left-0 before:top-0 before:z-10 before:h-full before:w-16 before:bg-gradient-to-r before:from-background before:to-transparent after:absolute after:right-0 after:top-0 after:z-10 after:h-full after:w-16 after:bg-gradient-to-l after:from-background after:to-transparent">
              <div className="flex animate-scroll" style={{ willChange: 'transform', transform: 'translateZ(0)' }}>
                {scrollableLogos.map((item, index) => {
                  const logo = item.logo as Media
                  return (
                    <div
                      key={index}
                      className="flex flex-none items-center justify-center w-[250px] px-8 transition-all duration-200"
                    >
                      {item.url ? (
                        <a
                          href={item.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label={`Visit ${item.name}`}
                          className="w-full h-full flex items-center justify-center"
                        >
                          <Image
                            src={logo.url || '#'}
                            alt={logo.alt || item.name}
                            width={150}
                            height={80}
                            className={cn(
                              "object-contain h-16 md:h-20 max-w-[150px] transition-all duration-200 hover:scale-110",
                              monochrome && "grayscale hover:grayscale-0 opacity-75 hover:opacity-100"
                            )}
                            style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                          />
                        </a>
                      ) : (
                        <Image
                          src={logo.url || '#'}
                          alt={logo.alt || item.name}
                          width={150}
                          height={80}
                          className={cn(
                            "object-contain h-16 md:h-20 max-w-[150px] transition-all duration-200 hover:scale-110",
                            monochrome && "grayscale hover:grayscale-0 opacity-75 hover:opacity-100"
                          )}
                          style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                        />
                      )}
                    </div>
                  )
                })}
              </div>

              {/* Add a second scrolling row going in the opposite direction for a more dynamic effect */}
              {logos.length > 8 && (
                <div className="flex animate-scroll-reverse mt-12" style={{ willChange: 'transform', transform: 'translateZ(0)' }}>
                  {scrollableLogos.map((item, index) => {
                    const logo = item.logo as Media
                    return (
                      <div
                        key={`reverse-${index}`}
                        className="flex flex-none items-center justify-center w-[250px] px-8 transition-all duration-200"
                      >
                        {item.url ? (
                          <a
                            href={item.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            aria-label={`Visit ${item.name}`}
                            className="w-full h-full flex items-center justify-center"
                          >
                            <Image
                              src={logo.url || '#'}
                              alt={logo.alt || item.name}
                              width={150}
                              height={80}
                              className={cn(
                                "object-contain h-16 md:h-20 max-w-[150px] transition-all duration-200 hover:scale-110",
                                monochrome && "grayscale hover:grayscale-0 opacity-75 hover:opacity-100"
                              )}
                              style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                            />
                          </a>
                        ) : (
                          <Image
                            src={logo.url || '#'}
                            alt={logo.alt || item.name}
                            width={150}
                            height={80}
                            className={cn(
                              "object-contain h-16 md:h-20 max-w-[150px] transition-all duration-200 hover:scale-110",
                              monochrome && "grayscale hover:grayscale-0 opacity-75 hover:opacity-100"
                            )}
                            style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                          />
                        )}
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          )}
        </div>
    </BlockWrapper>
  );
}

export default SocialProofBlock;
