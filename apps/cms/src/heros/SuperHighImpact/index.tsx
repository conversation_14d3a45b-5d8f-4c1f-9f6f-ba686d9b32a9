'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect, useRef, useState } from 'react'
import { useWindowSize } from 'usehooks-ts'

import type { Media, Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import RichText from '@/components/RichText'
import { motion, useViewportScroll } from 'framer-motion'
import { cn } from '@ui/lib/utils'
import { AuroraBackground } from '@/components/eko/aurora'
import { Roboto } from 'next/font/google'
import { ChevronDown } from 'lucide-react'
import { Button } from '@ui/components/ui/button'

const headlineFont = Roboto({
  subsets: ['latin'],
  weight: ['700'], // specify the weights you need
})

export function Headline({ text, words }: { text: string; words: string[] }) {

  return (
    <div className="text-center">
      <h1
        className={cn(headlineFont.className, 'heading-1 mt-4')}>
        {text}
      </h1>
    </div>
  )
}

export const SuperHighImpactHero: React.FC<Page['hero'] & { headlineWords?: string[] }> = ({
                                                                                             title,
                                                                                             links,
                                                                                             media,
                                                                                             richText,
                                                                                             headlineWords = [],
                                                                                           }) => {
  const { setHeaderTheme } = useHeaderTheme()
  const heroRef = useRef(null)
  const { width, height } = useWindowSize()
  const isPortrait = width ? width < height : false;


  useEffect(() => {
    setHeaderTheme('dark')
  }, [setHeaderTheme])

  const [isScrolled, setIsScrolled] = useState(false)
  const [isSmallHeight, setIsSmallHeight] = useState(false)
  const [isSmallWidth, setIsSmallWidth] = useState(false)
  const { scrollY } = useViewportScroll()

  useEffect(() => {
    const unsubscribe = scrollY.onChange((latest) => {
      // If scrollY is greater than 0, we mark as scrolled
      if (!isScrolled && latest > window.innerHeight / 4) {
        setIsScrolled(true)
      }
    })
    return () => unsubscribe()
  }, [scrollY])

  const { width: winWidth, height: winHeight } = useWindowSize()

  useEffect(() => {
    if (winHeight < 400) {
      setIsSmallHeight(true)
      setIsScrolled(true)
    }
    if (winWidth < 400) {
      setIsSmallWidth(true)
    }
  }, [winHeight, winWidth])
  console.log('Height', winHeight)

  const variants = {
    expanded: { height: isSmallWidth ? 'auto' : 'calc(100dvh - 2rem)',},
    collapsed: {  height: 'auto', minHeight: 'fit-content'}, // or a specific height like "50vh"
  }

  const scrollToNext = () => {
    if (heroRef.current) {
      const heroHeight = (heroRef.current as any).offsetHeight
      window.scrollTo({
        top: heroHeight,
        behavior: 'smooth'
      })
    }
  }


  return (
    <motion.div className="relative w-full sm:justify-stretch flex flex-col -mt-1  "
                initial="expanded"
                animate={isScrolled ? 'collapsed' : 'expanded'}
                variants={variants}
                transition={{ duration: 0.8 }}>

      <div
        className="absolute left-0 right-0 -top-16 bottom-0  w-full ">
        <AuroraBackground
          className="absolute inset-0 [background-size:normal]   pointer-events-none"
          animated={true}
          showRadialGradient={true}
          darkMode={false}
          backgroundImage={media as Media}
          parallax={true}
          parallaxIntensity={0.15}
        >
          <span></span>
        </AuroraBackground>
      </div>


      <section
        id="hero"
        ref={heroRef}

        className="relative pb-16 container flex flex-col align-top  sm:flex-grow w-full sm:min-h-full z-10 mx-auto scroll-behavior-smooth bg-none"
      >
        {isPortrait ? (
          // Portrait: donut above, text block at the bottom
          <div className="relative flex flex-col md:grid md:grid-rows-2 items-start sm:flex-grow px-2 sm:px-24 ">
            <div className="flex flex-shrink flex-col sm:flex-grow sm:mb-8">
              {/* Headline block always at the top */}
              <Headline text={title!} words={headlineWords} />

              {/*<FrontPageDonut className="hidden sm:block "/>*/}
            </div>
            <div className="flex flex-col sm:flex-grow  sm:align-middle ">
              <div
                className="flex flex-row sm:gap-8 self-center p-4 sm:p-8  glass-effect-lit standard-rounding">
                {richText && (
                  <RichText
                    className=" text-gray-900 dark:text-white sm:opacity-90 text-left text-[12px]/[1.1rem]  sm:leading-tight sm:text-xl lg:text-xl xl:text-2xl [word-spacing:-0.05rem]"
                    data={richText}
                    enableGutter={false}
                  />
                )}
                {Array.isArray(links) && links.length > 0 && (
                  <div className="flex flex-col sm:justify-center gap-4 pt-0">
                    {links.map(({ link }, i) => (
                      <CMSLink size={!isSmallWidth ? 'lg' : 'sm'} key={i}
                               {...link}
                               className={cn(' text-gray-950 bold last:text-white lg:p-8 w-full text-lg md:text-xl lg:text-2xl bg-white hover:bg-neutral-300 hover:last:bg-amber-500 last:bg-amber-400 hover:last:text-white')}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (<div className="relative flex-grow grid grid-cols-1 grid-rows-2">
            <div className={cn('flex px-8 mt-16', isScrolled ? 'pt-2' : 'pt-[4dvh]')}>
              <Headline text={title!} words={headlineWords} />
            </div>
            <div
              className={cn(!isSmallHeight ? 'flex flex-row px-4 ' : ' flex flex-col justify-center mt-4 mx-auto')}>
                <div
                  className=" mx-auto  gap-8 lg:gap-12 self-center w-[80dvw] max-w-[1024px] max-h-fit  tracking-tight space-y-2 p-8 opacity-90 xl:text-opacity-80 dark:sm:bg-neutral-900/30 sm:backdrop-blur-md bg-white/20 dark:sm:border-neutral-600/60 sm:border sm:rounded-lg  sm:border-t-neutral-200/60  dark:sm:border-t-neutral-400/60 sm:border-r-neutral-100/60 dark:sm:border-r-neutral-500/60">
                  {richText && (
                    <RichText
                      className="opacity-75 text-gray-900 dark:text-white text-left sm:text-md lg:text-md 2xl:text-xl [word-spacing:-0.05rem]"
                      data={richText}
                      enableGutter={false}
                    />
                  )}
                  {Array.isArray(links) && links.length > 0 && (
                    <div className="flex md:justify-center gap-4 flex-col">
                      {links.map(({ link }, i) => (
                        <CMSLink key={i}
                                 {...link}
                                 size={!isSmallWidth ? 'lg' : 'sm'}
                                 className={cn(' text-gray-950 bold last:text-white p-8 w-full text-lg md:text-xl lg:text-2xl bg-white hover:bg-neutral-300 hover:last:bg-amber-500 last:bg-amber-400 hover:last:text-white')}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
          </div>
        )}
      </section>
      {!isScrolled && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 1 }}
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
        >
          <div className="relative">
            <div className="absolute -inset-1 bg-brand/20 rounded-full blur-sm animate-pulse"></div>
            <Button
              onClick={scrollToNext}
              variant="ghost"
              size="icon"
              className="relative w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-white/20 animate-bounce"
            >
              <ChevronDown className="w-6 h-6 text-white" />
            </Button>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
