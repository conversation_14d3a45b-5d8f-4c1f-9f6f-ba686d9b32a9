import React from 'react'

import type { Page } from '@/payload-types'

import { HighImpactHero } from '@/heros/HighImpact'
import { LowImpactHero } from '@/heros/LowImpact'
import { MediumImpactHero } from '@/heros/MediumImpact'
import { SuperHighImpactHero } from '@/heros/SuperHighImpact'
import { FrontPageHero } from '@/heros/FrontPageHero'

const heroes = {
  superHighImpact: SuperHighImpactHero,
  highImpact: HighImpactHero,
  lowImpact: LowImpactHero,
  mediumImpact: MediumImpactHero,
  frontPageHero: FrontPageHero
}

export const RenderHero: React.FC<Page['hero']> = (props) => {
  const { type } = props || {}

  if (!type || type === 'none') return <></>

  const HeroToRender = heroes[type]

  if (!HeroToRender) return  <></>

  return <HeroToRender {...props} />
}
