import React from 'react'

import type { Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'
import RichText from '@/components/RichText'

export const MediumImpactHero: React.FC<Page['hero']> = ({ links, media, richText }) => {
  return (
      <div className="container my-12">
          <div className="w-full relative">
            {media && typeof media === 'object' && (
              <Media
                className="absolute inset-0 overflow-hidden"
                imgClassName=""
                priority
                resource={media}
              />
            )}

            <RichText data={richText as any} enableGutter={true}  enableProse={false} className="text-white text-lg sm:text-xl md:text-3xl relative z-10 p-8 py-16 max-w-[50rem]"/>

            {Array.isArray(links) && links.length > 0 && (
              <ul className="flex gap-4">
                {links.map(({ link }, i) => {
                  return (
                    <li key={i}>
                      <CMSLink {...link} />
                    </li>
                  )
                })}
              </ul>
            )}
          </div>

      </div>

  )
}
