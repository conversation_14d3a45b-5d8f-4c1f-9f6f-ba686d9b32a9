'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect, useRef, useState } from 'react'
import { useMediaQuery, useSessionStorage } from 'usehooks-ts'

import type { Media, Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import RichText from '@/components/RichText'
import { motion, useScroll } from 'framer-motion'
import { cn } from '@utils/lib/utils'
import { AuroraBackground } from '@/components/eko/aurora'
import { Roboto } from 'next/font/google'
import { ChevronDown } from 'lucide-react'
import { Button } from '@ui/components/ui/button'

const headlineFont = Roboto({
  subsets: ['latin'],
  weight: ['700'],
})

const AnimatedHeadline = ({ text, isScrolled }: { text: string, isScrolled: boolean }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.3 }}
      className="text-center max-w-5xl mx-auto"
      style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isScrolled ? 0.0 : 1.0, display: isScrolled ? 'none' : 'flex' }}
        transition={{ duration: isScrolled ? 0.2 : 0.4, delay: 0.2 }}
        className="sm:mb-4 flex"
        style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
      >
        {/*<div className="h-[1px] w-12 bg-brand mr-4 opacity-80"></div>*/}
        <h3 className="text-white  opacity-35 text-xl md:text-2xl font-medium tracking-wide">
          ekoIntelligence
        </h3>
        {/*<div className="h-[1px] w-12 bg-brand ml-4 opacity-8"></div>*/}
      </motion.div>

      <motion.h1
        initial={{ opacity: 0, scale: 0.9, }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, delay: 0.2 }}
        className={cn(
          headlineFont.className,
          'mb-6 not-eko-h1 text-4xl text-white dark:text-white text-left',
        )}
        style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
      >
        <motion.div initial={{ opacity: 0, scale: 0.8, x:"-2.5rem" }} animate={{ opacity: 1, scale: 1, x:0 }} transition={{ duration: 0.8, delay: 0.4 }}   className="text-4xl/tighter text-white tracking-wide"> Determining</motion.div>
        <motion.div initial={{ opacity: 0, scale: 0.8, x:"-2.5rem" }} animate={{ opacity: 1, scale: 1, x:0 }} transition={{ duration: 0.8, delay: 0.6 }}   className="text-5xl/tighter text-green-300 tracking-wide">ekoReputations</motion.div>
        <motion.div initial={{ opacity: 0, scale: 0.8, x:"-3rem" }} animate={{ opacity: 1, scale: 1, x:0 }} transition={{ duration: 0.8, delay: 0.8 }}   className="text-2xl text-white tracking-normal mt-3">Reclaiming ESG</motion.div>
      </motion.h1>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, x:"-3rem" }} animate={{ opacity: 1, scale: 1, x:0 }} transition={{ duration: 1.2, delay: 2.0 }}
        className="text-white text-left font-normal md:prose-lg lg:prose-lg prose-a:text-white dark:prose-a:text-white mb-8">
        Our ekoReputation Analysis Tools provide behavioural analytics that cut through greenwashing and
        PR spin.
      </motion.div>

    </motion.div>
  )
}

export const

  FrontPageHeroClient: React.FC<Page['hero'] & { headlineWords?: string[] }> = ({
                                                                                  title,
                                                                                  links,
                                                                                  media,
                                                                                  richText,
                                                                                  headlineWords = [],
                                                                                }) => {
    const { setHeaderTheme } = useHeaderTheme()
    const heroRef = useRef<HTMLDivElement>(null)
    // Define your breakpoints
    const isXXs = useMediaQuery('(max-width: 399px)')
    const isXs = useMediaQuery('(min-width: 400px)')
    const isSmall = useMediaQuery('(min-width: 640px)')
    const isMobile = useMediaQuery('(min-width: 768px)')
    const isLg = useMediaQuery('(min-width: 1024px)')
    const isXl = useMediaQuery('(min-width: 1280px)')
    const is2xl = useMediaQuery('(min-width: 1536px)')

    const [isScrolled, setIsScrolled] = useSessionStorage("fp.scrolled", false)  ;


    const [variants, setVariants] = useState<any>({
      initial: { height: isScrolled ? '20dvh': '100dvh', scale: 1.0, translateY: '0', width: '100dvw' },
      expanded: { height:  isScrolled ? '20dvh': '100dvh', scale: 1.0, translateY: '0', width: '100dvw' },
      collapsed: { height:  isScrolled ? '20dvh': '100dvh', scale: 1.0, translateY: '0', width: '100dvw' },
    })


    useEffect(() => {
      // Always use the full viewport height for expanded state on first load
      let expandedHeight = '100vh'
      let initialHeight = '100vh'
      // Determine collapsed height based on screen size
      let collapsedHeight
      if (isXXs) {
        collapsedHeight = '980px'
        expandedHeight = '920px'
        initialHeight = '920px'
      } else if (is2xl) {
        collapsedHeight = '600px'
      } else if (isXl) {
        collapsedHeight = '600px'
      } else if (isLg) {
        collapsedHeight = '592px'
      } else if (isMobile) {
        collapsedHeight = '840px'
      } else if (isSmall) {
        collapsedHeight = '768px'
      } else if (isXs) {
        collapsedHeight = '920px'
        expandedHeight = '920px'
        initialHeight = '920px'
      } else {
        collapsedHeight = '600px'
      }
      const newCollapsed = false ? {
        translateY: '4rem',
        width: '768px',
        marginLeft: 'auto',
        marginRight: 'auto',
        borderRadius: '1.5rem',
        marginBottom: '2rem',
      } : { translateY: '0', width: '100dvw' }
      const newValues = {
        initial: { height: isScrolled ? collapsedHeight : initialHeight, opacity:0.0, scale: 1.0, translateY: '0', width: '100dvw' },
        expanded: { height: expandedHeight, scale: 1.0, opacity:1.0, translateY: '0', width: '100dvw' },
        collapsed: { height: collapsedHeight, scale: 1.0, opacity:1.0, ...newCollapsed },
      }
      setVariants(newValues)
    }, [isScrolled, isXXs, isXs, isMobile, isLg, isXl, is2xl])

    useEffect(() => {
      setHeaderTheme('dark')
    }, [setHeaderTheme])

    const { scrollY } = useScroll()

    useEffect(() => {
      const unsubscribe = scrollY.onChange((latest) => {
        if (!isScrolled && latest > 100) {
          setIsScrolled(true)
        }
      })
      return () => unsubscribe()
    }, [scrollY, isScrolled])

    const scrollToNext = () => {
      if (heroRef.current) {
        const heroHeight = heroRef.current.offsetHeight
        window.scrollTo({
          top: heroHeight,
          behavior: 'smooth',
        })
      }
    }


    return (
      <>
        <motion.div
          ref={heroRef}
          className="relative w-full overflow-hidden pt-8 mx-auto z-[10]  transform-gpu"
          initial={isScrolled ? 'collapsed' : 'initial'}
          animate={isScrolled ? 'collapsed' : 'expanded'}
          variants={variants}
          transition={isScrolled ? { duration: 0.8, delay: 0.2 } : { duration: 0.2, delay: 0.2 }}
          style={{ willChange: 'transform', transform: 'translateZ(0)' }}
        >
          {/* Background with enhanced gradient overlay and better coverage */}
          <div className="absolute inset-0 w-full overflow-hidden">
            <motion.div
              animate={isScrolled ? { opacity: 0.8 } : { opacity: 0.8 }}
              transition={{ duration: 1.2, delay: 0.2 }}
              className="absolute inset-0 bg-gradient-to-r from-green-950/90 to-green-950/60 z-10"
              style={{ willChange: 'opacity', transform: 'translateZ(0)' }}
            ></motion.div>
            <AuroraBackground
              className="absolute inset-0 will-change-transform"
              animated={true}
              slow={false}
              showRadialGradient={true}
              darkMode={true}
              backgroundImage={media as Media}
              parallax={false}
              parallaxIntensity={0.05}
              showAurora={!isScrolled}
            >
              {/* Required empty children */}
            </AuroraBackground>
            {/* Diagonal accent line */}
          </div>

          {/* Fixed position content container for better positioning */}
          <div className="absolute inset-0 z-20">
            {isScrolled && <div className=" h-8 sm:h-4 md:h-8 lg:h-9"></div>}
            <div className="flex flex-col items-center align-middle justify-center h-full ">
              <div
                className=" container mx-auto  grid lg:gap-16  grid-cols-1 lg:grid-cols-2 justify-center align-middle px-4 md:px-8">
                <div className="col-span-1 px-2 xl:pl-6 lg:pt-4 lg:py-6 ">
                  <motion.div
                    initial={{ opacity: 0.0}}
                    animate={{
                      opacity: 1.0,
                    }}
                    transition={{ duration: 1.2, delay: 0 }}
                    className="sm:mx-auto max-w-full h-fit text-left lg:mt-6 mb-8 sm:mb-4 lg:mb-8 md:min-w-[500px] transition-transform duration-300 translate-y-0 hover:translate-y-1  sm:max-w-[calc(100%_-_4rem)] lg:w-[800px]"
                    style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                  >
                    <AnimatedHeadline text={title || 'ESG Intelligence Platform'} isScrolled={isScrolled} />
                    {/* Enhanced CTA buttons - now with better positioning for collapsed state */}
                    {Array.isArray(links) && links.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: -30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={ isScrolled ? { duration: 0.2, delay: 0.0 } : { duration:3.2, delay: 22.0 }}
                        className="flex flex-col sm:flex-row gap-4 mt-6 "
                        style={{ willChange: 'transform, opacity', transform: 'translateZ(0)' }}
                      >
                        {links.map(({ link }, i) => (
                          <CMSLink
                            key={i}
                            {...link}
                            className={cn(
                              'px-8 py-4 rounded-full text-center text-lg transition-all duration-300  hover:shadow-xl',
                              i === 0
                                ? 'bg-brand-gradient-compliment text-black hover:bg-brand-dark border border-brand-light/20'
                                : 'bg-white/10 backdrop-blur-sm text-white border border-white/20 hover:bg-white/20',
                            )}
                          />
                        ))}
                      </motion.div>
                    )}
                  </motion.div>

                </div>
                {/* Content container that maintains size but adapts position */}
                <div
                  className="col-span-1 sm:mt-8 md:mt-0 sm:mx-8 lg:mx-0 relative flex flex-row h-full justify-end align-middle xl:pr-8 ">
                  <div className="flex flex-col h-full justify-center align-middle ">
                    <motion.div
                      className="flex flex-col overflow-visible sm:px-8 glass-effect drop-shadow-lg rounded-2xl transform-gpu h-fit lg:max-w-[480px]"
                      initial={{ y: 30, opacity: 0.0 }}
                      animate={isScrolled ? { y: 0, opacity: 1.0 } : { y: 0, opacity: 1.0 }}
                      transition={!isScrolled ? { duration: 0.8, delay: 6.2 } : { duration: 0.0, delay: 0.0 }}
                      style={{ willChange: 'transform', transform: 'translateZ(0)' }}
                    >
                      {/* Main headline and content - simpler structure */}
                      {richText && (
                        <div
                          className={cn('py-6 md:pt-8 md:pb-3 px-6  transform-gpu ')}>
                          <RichText
                            className="text-white tracking-wide prose font-light md:prose-md lg:prose-md prose-a:text-white dark:prose-a:text-white prose-a:underline prose-a:font-medium"
                            data={richText}
                            enableGutter={false}
                            enableProse={false}
                          />
                        </div>
                      )}


                    </motion.div>
                  </div>
                </div>

                {/* Improved scroll indicator with pulse effect */}
                {!isScrolled && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 3.4, duration: 1 }}
                    className=" absolute bottom-4 left-1/2 transform -translate-x-1/2"
                    style={{ willChange: 'opacity', transform: 'translateZ(0) translate(-50%, 0)' }}
                  >
                    <div className="relative hidden lg:block">
                      <div className="absolute  -inset-1 bg-brand/20 rounded-full blur-sm animate-pulse"></div>
                      <Button
                        onClick={scrollToNext}
                        variant="ghost"
                        size="icon"
                        className="relative w-14 h-14 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-white/20 animate-bounce"
                      >
                        <ChevronDown className="w-6 h-6 text-white" />
                      </Button>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced solid bottom edge to ensure no background or content bleed-through */}
          {/*<div className="absolute bottom-0 left-0 right-0 h-16 bg-background z-30"></div>*/}

          {/* Extra hidden layer to fill any gaps during transitions */}
        </motion.div>
      </>
    )
  }
