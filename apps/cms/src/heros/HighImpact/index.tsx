import React, { Suspense } from 'react'
import { HighImpactHeroClient } from '@/heros/HighImpact/client'
import type { Page } from '@/payload-types'

export const HighImpactHero: React.FC<Page['hero']> = (props) => {
  return (
    <Suspense fallback={
      <div className="w-full h-96 bg-brand-dark flex items-center justify-center">
        <div className="animate-pulse">Loading...</div>
      </div>
    }>
      <HighImpactHeroClient {...props} />
    </Suspense>
  )
}
