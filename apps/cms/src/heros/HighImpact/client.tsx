'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'

import type { Media as MediaType, Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import RichText from '@/components/RichText'
import { motion } from 'framer-motion'
import { cn } from '@utils/lib/utils'
import { Roboto } from 'next/font/google'
import { AuroraBackground } from '@/components/eko/aurora'

const headlineFont = Roboto({
  subsets: ['latin'],
  weight: ['700'],
})

const AnimatedHeadline = ({ text }: { text: string }) => {
  return (
    <motion.h1
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className={cn(
        headlineFont.className,
        "text-4xl sm:text-5xlmd:text-6xl lg:text-7xl font-bold tracking-tight leading-tight mb-8",
        "text-white light-heading"
      )}
    >
      {text}
    </motion.h1>
  )
}

export const HighImpactHeroClient: React.FC<Page['hero']> = ({ title, links, media, richText }) => {
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    setHeaderTheme('dark')
    // Add dependency array to prevent continuous re-rendering
    return () => {
      // Clean up if needed
    }
  }, [setHeaderTheme])

  return (
    <div
      className="relative  min-h-96 flex flex-col items-center justify-center text-white overflow-hidden"
      data-theme="dark"
    >
      {/* Background with AuroraBackground */}
      <div className="absolute inset-0 w-full overflow-hidden">
        <div className="absolute opacity-60 inset-0 bg-brand-gradient-dark dark:bg-brand-gradient-dark z-10"></div>
        <AuroraBackground
          className="absolute inset-0 will-change-transform"
          animated={true}
          slow={true}
          showRadialGradient={true}
          darkMode={true}
          backgroundImage={media as MediaType}
          parallax={true}
          parallaxIntensity={0.25}
        >
          {/* Required empty children */}
        </AuroraBackground>
      </div>

      <div className="mx-auto lg:max-w-[1000px] px-4 sm:px-16 sm:m-16 z-20 flex flex-col items-center justify-center py-16 md:standard-rounding glass-effect-lit">
        {/* Animated headline */}
        <AnimatedHeadline text={title || ''} />

        {/* Content section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="sm:max-w-[50rem] mt-4"
        >
          {richText && (
            <RichText
              className="mb-8 w-full text-white/90"
              data={richText}
              enableGutter={false}
              enableProse={false}
            />
          )}

          {Array.isArray(links) && links.length > 0 && (
            <motion.ul
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="flex justify-center gap-4 flex-wrap"
            >
              {links.map(({ link }, i) => {
                return (
                  <li key={i}>
                    <CMSLink {...link} />
                  </li>
                )
              })}
            </motion.ul>
          )}
        </motion.div>
      </div>
    </div>
  )
}
