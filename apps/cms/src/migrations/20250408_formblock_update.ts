import type { MigrateDownArgs, MigrateUpArgs } from '@payloadcms/db-postgres'

export async function up({ payload, req }: MigrateUpArgs): Promise<void> {
  // Add background field to FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    ADD COLUMN IF NOT EXISTS "background" BOOLEAN;
  `)

  // Add backgroundMedia field to FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    ADD COLUMN IF NOT EXISTS "backgroundMedia" INTEGER;
  `)

  // Add darkMode field to FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    ADD COLUMN IF NOT EXISTS "darkMode" BOOLEAN;
  `)

  // Add the same fields to pages__blocks_formBlock if it exists
  try {
    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "background" BOOLEAN;
    `)

    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "backgroundMedia" INTEGER;
    `)

    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "darkMode" BOOLEAN;
    `)
  } catch (error) {
    // Table might not exist, which is fine
    console.log('Note: pages__blocks_formBlock table does not exist, skipping')
  }

  // Add the same fields to posts__blocks_formBlock if it exists
  try {
    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "background" BOOLEAN;
    `)

    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "backgroundMedia" INTEGER;
    `)

    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      ADD COLUMN IF NOT EXISTS "darkMode" BOOLEAN;
    `)
  } catch (error) {
    // Table might not exist, which is fine
    console.log('Note: posts__blocks_formBlock table does not exist, skipping')
  }
}

export async function down({ payload, req }: MigrateDownArgs): Promise<void> {
  // Remove background field from FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    DROP COLUMN IF EXISTS "background";
  `)

  // Remove backgroundMedia field from FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    DROP COLUMN IF EXISTS "backgroundMedia";
  `)

  // Remove darkMode field from FormBlock
  await payload.db.pool.query(`
    ALTER TABLE "company_reports__blocks_formBlock"
    DROP COLUMN IF EXISTS "darkMode";
  `)

  // Remove the same fields from pages__blocks_formBlock if it exists
  try {
    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      DROP COLUMN IF EXISTS "background";
    `)

    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      DROP COLUMN IF EXISTS "backgroundMedia";
    `)

    await payload.db.pool.query(`
      ALTER TABLE "pages__blocks_formBlock"
      DROP COLUMN IF EXISTS "darkMode";
    `)
  } catch (error) {
    // Table might not exist, which is fine
    console.log('Note: pages__blocks_formBlock table does not exist, skipping')
  }

  // Remove the same fields from posts__blocks_formBlock if it exists
  try {
    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      DROP COLUMN IF EXISTS "background";
    `)

    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      DROP COLUMN IF EXISTS "backgroundMedia";
    `)

    await payload.db.pool.query(`
      ALTER TABLE "posts__blocks_formBlock"
      DROP COLUMN IF EXISTS "darkMode";
    `)
  } catch (error) {
    // Table might not exist, which is fine
    console.log('Note: posts__blocks_formBlock table does not exist, skipping')
  }
}
