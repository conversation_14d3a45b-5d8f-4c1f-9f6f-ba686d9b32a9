import { FormSubmission } from '@payloadcms/plugin-form-builder/types'
import type { PayloadRequest } from 'payload'
import { sendFormSubmissionToSlack } from '../utilities/slack'

// Define the parameters that will be passed to the function
interface SendFormToSlackParams {
  doc: FormSubmission & { id?: string; createdAt?: string | Date };
  req?: PayloadRequest;
  operation: 'create' | 'update' | 'delete';
  // Add other fields as needed
}

/**
 * <PERSON>ler function for Slack notifications of form submissions
 * Can be used directly from a Payload hook
 */
export const sendFormToSlack = async ({
  doc,
  req = {} as PayloadRequest,
  operation
}: SendFormToSlackParams) => {
  // Only send to Slack for new form submissions
  if (operation !== 'create') {
    return doc
  }

  try {
    const payload = req.payload

    if (!payload) {
      console.warn('Payload instance not found in request')
      return doc
    }

    // Check if form is a string or already an object
    let formData = null;

    // If doc.form is a string and looks like a JSON object, parse it
    if (typeof doc.form === 'string' && doc.form.startsWith('{')) {
      try {
        formData = JSON.parse(doc.form);
      } catch (e) {
        console.warn('Could not parse form data as JSON', e);
      }
    } else if (typeof doc.form === 'object') {
      // If it's already an object, use it directly
      formData = doc.form;
    } else {
      // If it's a string ID, fetch the form
      try {
        formData = await payload.findByID({
          collection: 'forms',
          id: doc.form,
        });
      } catch (e) {
        console.warn(`Error fetching form with ID ${doc.form}`, e);
      }
    }

    if (!formData) {
      console.warn(`Form data not available for submission ${doc.id}`);
      return doc;
    }

    // Send to Slack
    await sendFormSubmissionToSlack(doc, formData)
  } catch (error) {
    console.error('Error in sendFormToSlack hook:', error)
  }

  return doc
}
