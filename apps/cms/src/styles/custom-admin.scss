@import '~@payloadcms/ui/scss';

/* Login page styling */
.payload-login {
  .logo {
    display: none !important;
  }
  
  .app-header {
    display: none !important;
  }
}

/* PayloadCMS auth form styling */
.auth-fields {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: 100% !important;
  
  // Hide the default PayloadCMS logo
  .logo {
    display: none !important;
  }
  
  // Hide the form header since we have our own logo
  h1 {
    display: none !important;
  }
  
  .field-type {
    margin-bottom: 1rem;
    
    &__label {
      font-size: 0.875rem !important;
      font-weight: 500 !important;
      color: var(--neutral-700) !important;
      margin-bottom: 0.25rem !important;
    }
    
    input {
      height: 2.5rem !important;
      width: 100% !important;
      border-radius: 0.375rem !important;
      border: 1px solid var(--neutral-300) !important;
      background-color: white !important;
      padding: 0.5rem 0.75rem !important;
      font-size: 0.875rem !important;
      transition: all 0.25s ease-in-out !important;
      
      &:focus {
        outline: none !important;
        border-color: var(--brand-green) !important;
        box-shadow: 0 0 0 2px var(--brand-green-shadow) !important;
      }
    }
  }
  
  .btn {
    height: 2.5rem !important;
    width: 100% !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 0.375rem !important;
    background-color: var(--brand-green) !important;
    color: white !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    transition: all 0.25s ease-in-out !important;
    margin-top: 0.5rem !important;
    border: none !important;
    cursor: pointer !important;
    
    &:hover {
      background-color: var(--brand-green-dark) !important;
      transform: translateY(-1px) !important;
    }
  }
  
  .form-submit {
    margin-top: 1.5rem !important;
  }
  
  .auth-wrap {
    a {
      display: block !important;
      text-align: center !important;
      margin-top: 1rem !important;
      font-size: 0.875rem !important;
      color: var(--brand-green) !important;
      text-decoration: none !important;
      transition: color 0.25s ease-in-out !important;
      
      &:hover {
        color: var(--brand-green-dark) !important;
        text-decoration: underline !important;
      }
    }
  }
  
  .form-wrap {
    max-width: 100% !important;
  }
  
  // Error message styling
  .error-message {
    color: var(--error) !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
  }
}

/* === VARIABLES === */
:root {
  /* Brand Colors */
  --brand-green: #54926e;
  --brand-green-light: #6dab87;
  --brand-green-dark: #417155;
  --brand-green-shadow: rgba(84, 146, 110, 0.15);
  --brand-red: #d63031;

  /* Brand Theme */
  --theme-primary: var(--brand-green);
  --theme-primary-light: var(--brand-green-light);
  --theme-primary-dark: var(--brand-green-dark);
  --theme-primary-shadow: var(--brand-green-shadow);

  /* Neutral Colors */
  --neutral-900: #111827;
  --neutral-800: #1f2937;
  --neutral-700: #374151;
  --neutral-600: #4b5563;
  --neutral-500: #6b7280;
  --neutral-400: #9ca3af;
  --neutral-300: #d1d5db;
  --neutral-200: #e5e7eb;
  --neutral-100: #f3f4f6;
  --neutral-50: #f9fafb;

  /* Accents */
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --purple-500: #8b5cf6;
  --purple-600: #7c3aed;

  /* System */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Brand Theme */
  --theme-primary: var(--brand-green);
  --theme-primary-light: var(--brand-green-light);
  --theme-primary-dark: var(--brand-green-dark);
  --theme-primary-shadow: var(--brand-green-shadow);

  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* === GLOBAL RESETS === */
.payload-admin {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--neutral-900);
  background-color: var(--neutral-50);
  font-feature-settings: "ss03", "cv11", "cv01", "cv02", "cv03", "cv04";
  box-sizing: border-box;

  *, *:before, *:after {
    box-sizing: inherit;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 600;
  }

  button {
    cursor: pointer;
  }

  a {
    color: var(--theme-primary);
    text-decoration: none;
    transition: color var(--transition-fast);

    &:hover {
      color: var(--theme-primary-dark);
      text-decoration: underline;
    }
  }
}

/* === HEADER === */
.app-header {
  background: white !important;
  border-bottom: 1px solid var(--neutral-200) !important;
  height: 60px !important;
  box-shadow: var(--shadow-sm) !important;
  position: fixed !important;
  width: 100% !important;
  z-index: 40 !important;
  backdrop-filter: blur(8px) !important;

  .app-header__content {
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    height: 100%;
  }

  .app-header__title {
    margin-left: var(--space-4);
    font-weight: 600;
  }

  .admin-logo {
    display: flex;
    align-items: center;
  }
}

/* === SIDEBAR MENU === */
.nav-menu {
  background-color: white !important;
  border-right: 1px solid var(--neutral-200) !important;
  position: fixed !important;
  top: 60px !important;
  left: 0 !important;
  bottom: 0 !important;
  width: 260px !important;
  z-index: 30 !important;
  overflow-y: auto !important;

  &__header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
  }

  &__toggle {
    background-color: var(--neutral-100);
    border-radius: var(--radius);
    border: 1px solid var(--neutral-200);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 16px;
      height: 16px;
      color: var(--neutral-600);
    }

    &:hover {
      background-color: var(--neutral-200);
    }
  }
}

/* === NAVIGATION === */
.nav {
  background-color: white !important;
  padding: var(--space-2) 0;

  &__wrap {
    border-right: none !important;
  }

  &__group {
    h3 {
      color: var(--neutral-500);
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      padding: var(--space-5) var(--space-6) var(--space-2);
      margin: 0;
    }
  }

  &__link {
    font-size: 14px;
    font-weight: 500;
    color: var(--neutral-700);
    padding: var(--space-2) var(--space-6);
    margin: 1px var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);

    .nav__icon {
      color: var(--neutral-500);
      width: 18px;
      height: 18px;
      margin-right: var(--space-3);
    }

    &:hover {
      background-color: var(--neutral-100);
      color: var(--neutral-900);
    }

    &--active {
      background-color: var(--brand-green-shadow) !important;
      color: var(--brand-green-dark) !important;
      font-weight: 600;

      .nav__icon {
        color: var(--brand-green);
      }
    }
  }
}

/* === MAIN CONTENT === */
.app-gutter {
  margin-left: 260px !important;
}

.app-wrapper {
  padding-top: 60px !important;
}

.dashboard {
  padding: var(--space-6);
  min-height: calc(100vh - 60px);
  background-color: var(--neutral-50);
}

/* === BUTTONS === */
.btn {
  height: 38px;
  font-size: 14px;
  font-weight: 500;
  padding: 0 var(--space-4);
  border-radius: var(--radius);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &--style-primary {
    background: var(--theme-primary) !important;
    border: 1px solid var(--theme-primary-dark) !important;
    color: white !important;

    &:hover {
      background: var(--theme-primary-dark) !important;
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    &:active {
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }
  }

  &--style-secondary {
    background: white !important;
    border: 1px solid var(--neutral-300) !important;
    color: var(--neutral-700) !important;

    &:hover {
      border-color: var(--neutral-400) !important;
      color: var(--neutral-900) !important;
      background: var(--neutral-50) !important;
    }
  }

  &--icon-style-with-border {
    &.btn--style-primary {
      svg {
        width: 16px;
        height: 16px;
        margin-right: var(--space-2);
      }
    }

    &.btn--style-secondary {
      svg {
        width: 16px;
        height: 16px;
        margin-right: var(--space-2);
        color: var(--neutral-500);
      }

      &:hover svg {
        color: var(--neutral-700);
      }
    }
  }

  &--icon-style-only {
    width: 38px;
    padding: 0;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  &--style-danger {
    background: var(--error) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: white !important;

    &:hover {
      background: #d32f2f !important;
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
}

.btn-group {
  gap: var(--space-2);
  display: flex;

  .btn {
    margin: 0 !important;
  }
}

/* === CARDS === */
.card {
  background: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast), transform var(--transition-fast);

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  &__header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
    background: var(--neutral-50);
    display: flex;
    align-items: center;
    justify-content: space-between;

    h2, h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--neutral-800);
      display: flex;
      align-items: center;

      svg {
        margin-right: var(--space-2);
        color: var(--theme-primary);
      }
    }
  }

  &__body {
    padding: var(--space-6);
  }
}

/* === FORMS === */
.field-type {
  margin-bottom: var(--space-6);

  &__label {
    font-size: 14px;
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: var(--space-2);
  }

  input, textarea, select, .relationship__input {
    font-family: inherit;
    font-size: 14px;
    color: var(--neutral-900);
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius);
    background-color: white;
    transition: all var(--transition-fast);
    width: 100%;

    &:focus {
      outline: none;
      border-color: var(--theme-primary) !important;
      box-shadow: 0 0 0 2px var(--theme-primary-shadow) !important;
    }

    &:hover:not(:focus) {
      border-color: var(--neutral-400);
    }
  }

  &__description {
    font-size: 12px;
    color: var(--neutral-500);
    margin-top: var(--space-1);
  }

  &__error {
    font-size: 12px;
    color: var(--error);
    margin-top: var(--space-2);
  }

  .checkbox-input {
    &:checked {
      background-color: var(--theme-primary) !important;
      border-color: var(--theme-primary-dark) !important;
    }
  }
}

/* === TABLES === */
.table {
  width: 100%;
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  background: white;
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  &__header {
    background-color: var(--neutral-50);

    .table__header-cell {
      font-size: 12px;
      font-weight: 600;
      color: var(--neutral-600);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      padding: var(--space-3) var(--space-4);
      border-bottom: 1px solid var(--neutral-200);
    }
  }

  &__body {
    .row {
      border-bottom: 1px solid var(--neutral-200);
      transition: background var(--transition-fast);

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: var(--neutral-50);
      }

      .cell {
        padding: var(--space-4);
        font-size: 14px;
        color: var(--neutral-700);
      }

      .cell-actions {
        .btn-group {
          opacity: 0;
          transition: opacity var(--transition-fast);
        }
      }

      &:hover .cell-actions .btn-group {
        opacity: 1;
      }
    }
  }
}

/* === PAGE HEADERS === */
.collection-list__header, .edit__header {
  margin-bottom: var(--space-8);

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
  }

  .header-description {
    font-size: 14px;
    color: var(--neutral-600);
  }
}

/* === DASHBOARD === */
.before-dashboard {
  .welcome-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
    margin-bottom: var(--space-8);

    .stat-card {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--space-6);
      border: 1px solid var(--neutral-200);
      box-shadow: var(--shadow-sm);

      .stat-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--neutral-600);
        margin-bottom: var(--space-2);
      }

      .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: var(--neutral-900);
        margin-bottom: var(--space-1);
      }

      .stat-description {
        font-size: 12px;
        color: var(--neutral-500);
      }
    }
  }

  .dashboard-section {
    margin-bottom: var(--space-8);

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: var(--neutral-800);
      margin-bottom: var(--space-4);
      display: flex;
      align-items: center;

      svg {
        width: 18px;
        height: 18px;
        color: var(--brand-green);
        margin-right: var(--space-2);
      }
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--space-4);

    .action-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: var(--space-6);
      background: white;
      border: 1px solid var(--neutral-200);
      border-radius: var(--radius-lg);
      transition: all var(--transition);
      box-shadow: var(--shadow-sm);
      text-decoration: none;

      &:hover {
        border-color: var(--brand-green-light);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
        text-decoration: none;
      }

      .action-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius);
        background: var(--brand-green-shadow);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: var(--space-4);

        svg {
          width: 20px;
          height: 20px;
          color: var(--brand-green);
        }
      }

      .action-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--neutral-800);
      }
    }
  }

  .recent-activity {
    .activity-list {
      background: white;
      border: 1px solid var(--neutral-200);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-sm);
      overflow: hidden;

      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: var(--space-4) var(--space-6);
        border-bottom: 1px solid var(--neutral-200);

        &:last-child {
          border-bottom: none;
        }

        .activity-icon {
          width: 32px;
          height: 32px;
          border-radius: var(--radius);
          background: var(--neutral-100);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--space-4);

          svg {
            width: 16px;
            height: 16px;
            color: var(--neutral-600);
          }

          &.edit {
            background-color: var(--brand-green-shadow);
            svg { color: var(--brand-green); }
          }

          &.add {
            background-color: rgba(59, 130, 246, 0.1);
            svg { color: var(--blue-600); }
          }

          &.delete {
            background-color: rgba(239, 68, 68, 0.1);
            svg { color: var(--error); }
          }
        }

        .activity-content {
          flex: 1;

          .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-800);
            margin-bottom: var(--space-1);
          }

          .activity-meta {
            font-size: 12px;
            color: var(--neutral-500);
          }
        }
      }
    }
  }
}

/* === USER ACTIONS === */
.user-actions {
  &__toggle {
    background: var(--neutral-100) !important;
    border: 1px solid var(--neutral-200) !important;
    width: 38px !important;
    height: 38px !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all var(--transition-fast) !important;

    &:hover {
      background: var(--neutral-200) !important;
    }

    img {
      width: 28px !important;
      height: 28px !important;
      border-radius: 50% !important;
      object-fit: cover !important;
    }
  }

  &__dropdown {
    margin-top: var(--space-2) !important;
    border-radius: var(--radius-lg) !important;
    border: 1px solid var(--neutral-200) !important;
    box-shadow: var(--shadow-lg) !important;
    overflow: hidden !important;
    background: white !important;

    .user-actions__button {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: var(--neutral-700) !important;
      padding: var(--space-3) var(--space-4) !important;
      transition: all var(--transition-fast) !important;

      &:hover {
        background: var(--neutral-100) !important;
        color: var(--neutral-900) !important;
      }
    }
  }
}

/* === TABS === */
.tabs-field {
  &__tabs-header {
    background: white;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border: 1px solid var(--neutral-200);
    border-bottom: none;
    display: flex;
    padding: var(--space-1);

    .tabs-field__tab-button {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius);
      font-size: 14px;
      font-weight: 500;
      color: var(--neutral-600);
      transition: all var(--transition-fast);
      margin: var(--space-1);

      &:hover {
        background: var(--neutral-100);
        color: var(--neutral-900);
      }

      &--active {
        background: var(--brand-green-shadow) !important;
        color: var(--brand-green-dark) !important;
        font-weight: 600;
      }
    }
  }

  &__tabs-content {
    background: white;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    border: 1px solid var(--neutral-200);
    border-top: none;
    padding: var(--space-6);
  }
}

/* === PAGINATION === */
.paginator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--space-8);

  .btn {
    border-radius: var(--radius);

    &:not(:last-child) {
      margin-right: var(--space-2);
    }

    &.btn--pagination--active {
      background: var(--brand-green) !important;
      color: white !important;
    }
  }
}

/* === TOAST NOTIFICATIONS === */
.toast {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: none;
  padding: var(--space-4);

  &--success {
    background: var(--success);
    color: white;
  }

  &--error {
    background: var(--error);
    color: white;
  }

  &--info {
    background: var(--info);
    color: white;
  }

  &--warning {
    background: var(--warning);
    color: white;
  }

  .toast__details {
    display: flex;
    align-items: center;

    .toast__icon {
      margin-right: var(--space-3);

      svg {
        width: 20px;
        height: 20px;
      }
    }

    .toast__message {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .toast__dismiss {
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

/* === CUSTOM DASHBOARD COMPONENTS === */
.before-dashboard {
  // Create new dashboard with minimal styling first
  padding: var(--space-8);


  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
  }

  .welcome-text {
    font-size: 16px;
    color: var(--neutral-600);
    max-width: 800px;
    margin-bottom: var(--space-8);
  }

  .main-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }
  }

  .card {
    height: 100%;
    display: flex;
    flex-direction: column;

    .card__header {
      h2 {
        display: flex;
        align-items: center;

        svg {
          margin-right: var(--space-2);
          color: var(--brand-green);
        }
      }
    }

    .card__body {
      flex: 1;
    }

    .card__footer {
      padding: var(--space-4) var(--space-6);
      border-top: 1px solid var(--neutral-200);
      text-align: right;

      a {
        font-size: 14px;
        font-weight: 500;
        color: var(--brand-green);

        &:hover {
          color: var(--brand-green-dark);
        }
      }
    }
  }

  .quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);

    @media (max-width: 640px) {
      grid-template-columns: 1fr;
    }

    .action-item {
      background: white;
      border: 1px solid var(--neutral-200);
      border-radius: var(--radius);
      padding: var(--space-4);
      transition: all var(--transition);
      text-decoration: none;
      display: flex;
      align-items: center;

      &:hover {
        border-color: var(--brand-green);
        transform: translateY(-1px);
        box-shadow: var(--shadow);
        text-decoration: none;
      }

      .action-icon {
        width: 36px;
        height: 36px;
        background: var(--brand-green-shadow);
        border-radius: var(--radius);
        margin-right: var(--space-3);
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          width: 18px;
          height: 18px;
          color: var(--brand-green);
        }
      }

      .action-text {
        flex: 1;

        .action-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--neutral-800);
        }

        .action-desc {
          font-size: 12px;
          color: var(--neutral-500);
        }
      }
    }
  }
}

/* === DARK MODE === */
.theme--dark {
  --neutral-50: #111827;
  --neutral-100: #1f2937;
  --neutral-200: #374151;
  --neutral-300: #4b5563;
  --neutral-400: #6b7280;
  --neutral-500: #9ca3af;
  --neutral-600: #d1d5db;
  --neutral-700: #e5e7eb;
  --neutral-800: #f3f4f6;
  --neutral-900: #f9fafb;

  /* Dark Theme Adjustments */
  --theme-primary: #6dab87;
  --theme-primary-light: #7fb99a;
  --theme-primary-dark: #54926e;
  --theme-primary-shadow: rgba(109, 171, 135, 0.25);

  background-color: var(--neutral-50);

  .app-header {
    background: var(--neutral-100) !important;
    border-bottom-color: var(--neutral-200) !important;
  }

  .nav-menu {
    background-color: var(--neutral-100) !important;
    border-right-color: var(--neutral-200) !important;

    &__toggle {
      background-color: var(--neutral-50);
      border-color: var(--neutral-200);

      svg {
        color: var(--neutral-500);
      }
    }
  }

  .nav {
    background-color: var(--neutral-100) !important;

    &__link {
      color: var(--neutral-600);

      &:hover {
        background-color: var(--neutral-50);
      }

      .nav__icon {
        color: var(--neutral-500);
      }

      &--active {
        background-color: rgba(109, 171, 135, 0.2) !important;
        color: var(--theme-primary) !important;

        .nav__icon {
          color: var(--theme-primary);
        }
      }
    }
  }

  .btn {
    &--style-secondary {
      background: var(--neutral-100) !important;
      border-color: var(--neutral-200) !important;
      color: var(--neutral-600) !important;

      &:hover {
        border-color: var(--neutral-300) !important;
        color: var(--neutral-700) !important;
      }
    }
  }

  .card, .table, .tabs-field__tabs-header, .tabs-field__tabs-content,
  .action-item, .welcome-grid .stat-card {
    background: var(--neutral-100);
    border-color: var(--neutral-200);
  }

  .table__header {
    background-color: var(--neutral-50);

    .table__header-cell {
      border-bottom-color: var(--neutral-200);
    }
  }

  .table__body .row {
    border-bottom-color: var(--neutral-200);

    &:hover {
      background-color: var(--neutral-50);
    }
  }

  .card__header, .tabs-field__tabs-header {
    border-bottom-color: var(--neutral-200);
  }

  .card__footer {
    border-top-color: var(--neutral-200);
  }

  .field-type {
    input, textarea, select, .relationship__input {
      background: var(--neutral-100);
      border-color: var(--neutral-300);
      color: var(--neutral-700);

      &:hover:not(:focus) {
        border-color: var(--neutral-400);
      }

      &:focus {
        border-color: var(--theme-primary) !important;
        box-shadow: 0 0 0 2px rgba(109, 171, 135, 0.25) !important;
      }

      &::placeholder {
        color: var(--neutral-500);
      }
    }

    .checkbox-input {
      &:checked {
        background-color: var(--theme-primary) !important;
        border-color: var(--theme-primary-dark) !important;
      }
    }
  }

  .user-actions {
    &__toggle {
      background: var(--neutral-50) !important;
      border-color: var(--neutral-200) !important;
    }

    &__dropdown {
      background: var(--neutral-100) !important;
      border-color: var(--neutral-200) !important;

      .user-actions__button {
        &:hover {
          background: var(--neutral-50) !important;
        }
      }
    }
  }
}
