/**
 * Centralized theme variables for consistent styling across the application
 * These variables should be used in conjunction with Tailwind CSS
 */

export const themeColors = {
  // Brand colors
  brand: {
    primary: 'hsl(var(--brand-primary))',
    primaryDark: 'hsl(var(--brand-primary-dark))',
    contrast: 'hsl(var(--brand-contrast))',
  },

  // Semantic colors
  semantic: {
    success: 'hsl(var(--success))',
    warning: 'hsl(var(--warning))',
    error: 'hsl(var(--error))',
  },

  // UI colors
  ui: {
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    card: 'hsl(var(--card))',
    cardForeground: 'hsl(var(--card-foreground))',
    border: 'hsl(var(--border))',
    input: 'hsl(var(--input))',
  }
};

export const spacing = {
  // Section spacing
  section: {
    small: 'section-spacing-small',
    medium: 'section-spacing-medium',
    large: 'section-spacing-large',
  },

  // Content spacing
  content: {
    small: 'content-spacing-small',
    medium: 'content-spacing-medium',
    large: 'content-spacing-large',
  },

  // Container classes
  container: {
    small: 'container-small',
    medium: 'container-medium',
    large: 'container-large',
    xlarge: 'container-xlarge',
    padding: {
      sm: '1rem',
      md: '2rem',
      lg: '2rem',
      xl: '2rem',
      '2xl': '2rem',
    },
  },

  // Grid layouts
  grid: {
    cols2: 'grid-2-cols',
    cols3: 'grid-3-cols',
    cols4: 'grid-4-cols',
  },
};

export const borderRadius = {
  standard: 'rounded-standard',
  small: 'rounded-small',
  medium: 'rounded-medium',
  large: 'rounded-large',
  full: 'rounded-full',
};

export const typography = {
  // Heading styles
  headings: {
    h1: 'heading-1',
    h2: 'heading-2',
    h3: 'heading-3',
    h4: 'heading-4',
    h5: 'heading-5',
    h6: 'heading-6',
  },

  // Text styles
  text: {
    lead: 'text-lead',
    body: 'text-body',
    small: 'text-small',
    tiny: 'text-tiny',
  },

  // Text colors
  colors: {
    primary: 'text-primary',
    muted: 'text-muted',
    foreground: 'text-foreground',
    background: 'text-background',
    accent: 'text-accent',
    success: 'text-success',
    warning: 'text-warning',
    error: 'text-error',
  },

  // Legacy values (kept for backward compatibility)
  fontSizes: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
    '5xl': '3rem',
    '6xl': '3.75rem',
  },
  lineHeights: {
    tight: '1.2',
    normal: '1.5',
    relaxed: '1.65',
  },
};

export const effects = {
  // Glass effects
  glassMorphism: 'glass-effect',
  glassMorphismLit: 'glass-effect-lit',
  glassMorphismSubtle: 'glass-effect-subtle',
  glassMorphismSubtleLit: 'glass-effect-subtle-lit',
  glassMorphismStrong: 'glass-effect-strong',
  glassMorphismStrongLit: 'glass-effect-strong-lit',
  glassMorphismBrand: 'glass-effect-brand',
  glassMorphismBrandLit: 'glass-effect-brand-lit',
  glassMorphismBrandStrong: 'glass-effect-brand-strong',
  glassMorphismBrandStrongLit: 'glass-effect-brand-strong-lit',
  glassMorphismBrandAltStrong: 'glass-effect-brand-alt-strong',
  glassMorphismBrandAltStrongLit: 'glass-effect-brand-alt-strong-lit',

  // Shadows
  shadowSubtle: 'shadow-subtle',
  shadowSoft: 'shadow-soft',
  shadowMedium: 'shadow-medium',
  shadowStrong: 'shadow-strong',

  // Transitions
  transitionFast: 'transition-fast',
  transitionStandard: 'transition-standard',
  transitionSlow: 'transition-slow',

  // Hover effects
  hoverLift: 'hover-lift',
  hoverLiftSubtle: 'hover-lift-subtle',
  hoverScale: 'hover-scale',
  hoverScaleSubtle: 'hover-scale-subtle',

  // Backgrounds
  noisyBackground: 'bg-noise opacity-[0.03] mix-blend-overlay',
};

export default {
  themeColors,
  spacing,
  borderRadius,
  typography,
  effects,
};
