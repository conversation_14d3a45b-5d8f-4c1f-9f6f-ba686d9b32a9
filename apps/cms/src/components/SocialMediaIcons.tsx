'use client'
import React from 'react'
import { Post } from '@/payload-types'
import { cn } from '@utils/lib/utils'
import Link from 'next/link'
import { Facebook, Github, Instagram, Linkedin, Youtube } from 'lucide-react'

type SocialLink = {
  platform: string
  url: string
}

type SocialMediaIconsProps = {
  socialLinks: SocialLink[]
  className?: string
  variant: string | null
}

// X (Twitter) icon SVG
const XIcon = ({ className }: { className: string }) => (
  <svg
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <title>X</title>
    <path
      d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
      fill="currentColor"
    />
  </svg>
);


// Helper to get the social icon component
const getSocialIcon = (platform: string, className: string) => {
  switch (platform.toLowerCase()) {
    case 'facebook':
      return <Facebook className={className} />;
    case 'twitter':
      return <XIcon className={className} />;
    case 'linkedin':
      return <Linkedin className={className} />;
    case 'instagram':
      return <Instagram className={className} />;
    case 'github':
      return <Github className={className} />;
    case 'youtube':
      return <Youtube className={className} />;
    default:
      return <span className="text-xs font-semibold">{platform.charAt(0).toUpperCase()}</span>;
  }
};

export const SocialMediaIcons: React.FC<SocialMediaIconsProps> = ({
  socialLinks,
  className = '',
  variant = null
}) => {
  if (!socialLinks || socialLinks.length === 0) return null;

  return (
    <span className="ml-4">
      {socialLinks.map((link, index) => (
        <Link
          key={index}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={link.platform}
          className={cn(
            "inline-flex items-center justify-center rounded-full mr-2 bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors",
            variant === "large" ? "w-10 h-10" : "w-6 h-6"
          )}
          title={`${link.platform.charAt(0).toUpperCase() + link.platform.slice(1)}`}
        >
          {getSocialIcon(link.platform, cn(
            variant === "large" ? "w-5 h-5" : "w-3 h-3"
          ))}
        </Link>
      ))}
    </span>
  )
}

export const AuthorSocialIcons: React.FC<{
  authors: NonNullable<Post['populatedAuthors']>,
  variant?: string | null
}> = ({
  authors,
  variant = null
}) => {
  if (!authors || authors.length === 0) return null;

  return (
    <>
      {authors.map((author, index) => {
        if (!author.socialLinks || author.socialLinks.length === 0) return null;

        return (
          <SocialMediaIcons
            key={index}
            variant={variant}
            socialLinks={author.socialLinks as SocialLink[]}
          />
        )
      })}
    </>
  )
}
