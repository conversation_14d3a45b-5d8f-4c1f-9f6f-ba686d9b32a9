import React from 'react'
import { cn } from '@utils/lib/utils'
import { AlertCircle, AlertTriangle, CheckCircle, Info, X } from 'lucide-react'

interface AlertProps {
  children: React.ReactNode;
  title?: string;
  variant?: 'info' | 'success' | 'warning' | 'error';
  className?: string;
  icon?: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
}

/**
 * A standardized alert component for consistent messaging
 */
export const Alert: React.FC<AlertProps> = ({
  children,
  title,
  variant = 'info',
  className,
  icon,
  dismissible = false,
  onDismiss,
}) => {
  // Determine variant classes
  const variantClasses = {
    info: 'bg-blue-50 text-blue-800 border-blue-200',
    success: 'bg-green-50 text-green-800 border-green-200',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
    error: 'bg-red-50 text-red-800 border-red-200',
  };

  // Determine icon based on variant
  const variantIcon = icon || {
    info: <Info className="h-5 w-5 text-blue-500" />,
    success: <CheckCircle className="h-5 w-5 text-green-500" />,
    warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
    error: <AlertCircle className="h-5 w-5 text-red-500" />,
  }[variant];

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4',
        variantClasses[variant],
        className
      )}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">{variantIcon}</div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className="text-sm font-medium">{title}</h3>
          )}
          <div className={cn("text-sm", title && "mt-2")}>
            {children}
          </div>
        </div>
        {dismissible && (
          <button
            type="button"
            className={cn(
              'ml-auto flex-shrink-0 rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
              {
                info: 'text-blue-500 hover:bg-blue-100 focus:ring-blue-500',
                success: 'text-green-500 hover:bg-green-100 focus:ring-green-500',
                warning: 'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-500',
                error: 'text-red-500 hover:bg-red-100 focus:ring-red-500',
              }[variant]
            )}
            onClick={onDismiss}
          >
            <span className="sr-only">Dismiss</span>
            <X className="h-5 w-5" />
          </button>
        )}
      </div>
    </div>
  );
};
