import React from 'react'
import { cn } from '@utils/lib/utils'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/ui/tabs'

interface Tab {
  id: string;
  label: React.ReactNode;
  content: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface TabsStyledProps {
  tabs: Tab[];
  defaultValue?: string;
  variant?: 'default' | 'pills' | 'underline' | 'minimal';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  tabsListClassName?: string;
  tabsContentClassName?: string;
  onChange?: (value: string) => void;
}

/**
 * A standardized tabs component for consistent navigation
 */
export const TabsStyled: React.FC<TabsStyledProps> = ({
  tabs,
  defaultValue,
  variant = 'default',
  orientation = 'horizontal',
  className,
  tabsListClassName,
  tabsContentClassName,
  onChange,
}) => {
  // Set default value to first tab if not provided
  const defaultTab = defaultValue || tabs[0]?.id;

  // Variant classes for the tabs list
  const variantClasses = {
    default: 'bg-muted p-1 rounded-lg',
    pills: 'space-x-2',
    underline: 'border-b border-border',
    minimal: 'space-x-6',
  };

  // Variant classes for the tab triggers
  const triggerVariantClasses = {
    default: 'data-[state=active]:bg-background data-[state=active]:shadow-sm',
    pills: 'rounded-full border border-border data-[state=active]:bg-background data-[state=active]:border-brand data-[state=active]:text-brand',
    underline: 'border-b-2 border-transparent data-[state=active]:border-brand rounded-none',
    minimal: 'text-muted-foreground data-[state=active]:text-foreground',
  };

  // Orientation classes
  const orientationClasses = {
    horizontal: '',
    vertical: 'flex flex-row space-x-0 space-y-0',
  };

  // Orientation classes for the tabs list
  const tabsListOrientationClasses = {
    horizontal: '',
    vertical: 'flex-col space-y-2 space-x-0',
  };

  return (
    <Tabs
      defaultValue={defaultTab}
      className={cn(orientationClasses[orientation], className)}
      orientation={orientation === 'vertical' ? 'vertical' : 'horizontal'}
      onValueChange={onChange}
    >
      <TabsList
        className={cn(
          variantClasses[variant],
          tabsListOrientationClasses[orientation],
          tabsListClassName
        )}
      >
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className={cn(
              triggerVariantClasses[variant],
              'flex items-center',
              tab.disabled && 'opacity-50 cursor-not-allowed'
            )}
            disabled={tab.disabled}
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      {tabs.map((tab) => (
        <TabsContent
          key={tab.id}
          value={tab.id}
          className={cn('mt-4', tabsContentClassName)}
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};
