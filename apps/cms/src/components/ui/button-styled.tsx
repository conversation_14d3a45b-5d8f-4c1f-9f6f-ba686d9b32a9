import React from 'react'
import { But<PERSON> } from '@ui/components/ui/button'
import { cn } from '@utils/lib/utils'
import Link from 'next/link'

interface ButtonStyledProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  external?: boolean;
  className?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

/**
 * A styled button component that extends the base Button component
 * with additional styling options and link functionality
 */
export const ButtonStyled: React.FC<ButtonStyledProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  href,
  external = false,
  className,
  icon,
  iconPosition = 'left',
  ...props
}) => {
  // Map our custom variants to the base Button variants
  const variantMap = {
    primary: 'default',
    secondary: 'secondary',
    outline: 'outline',
    ghost: 'ghost',
    link: 'link',
  };

  // Map our custom sizes to Tailwind classes
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
  };

  // Additional styling based on variant
  const additionalClasses = {
    primary: 'bg-brand hover:bg-brand-dark text-neutral-subtle shadow-subtle hover:shadow-medium transition-standard',
    secondary: 'bg-secondary hover:bg-secondary/80 text-secondary-foreground transition-standard',
    outline: 'border-primary text-primary hover:bg-brand/10 transition-standard',
    ghost: 'hover:bg-brand/10 text-primary transition-standard',
    link: 'text-primary underline-offset-4 hover:underline p-0 h-auto transition-fast',
  };

  const content = (
    <>
      {icon && iconPosition === 'left' && <span className="mr-2">{icon}</span>}
      {children}
      {icon && iconPosition === 'right' && <span className="ml-2">{icon}</span>}
    </>
  );

  // If href is provided, render as a Link
  if (href) {
    return (
      <Link
        href={href}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
        className={cn(
          'inline-flex items-center justify-center rounded-medium font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50',
          sizeClasses[size],
          additionalClasses[variant],
          className
        )}
      >
        {content}
      </Link>
    );
  }

  // Otherwise, render as a Button
  return (
    <Button
      variant={variantMap[variant] as "default" | "secondary" | "outline" | "ghost" | "link"}
      className={cn(
        sizeClasses[size],
        additionalClasses[variant],
        className
      )}
      {...props}
    >
      {content}
    </Button>
  );
};
