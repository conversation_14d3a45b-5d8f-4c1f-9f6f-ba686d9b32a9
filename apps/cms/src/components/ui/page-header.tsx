import React from 'react'
import { cn } from '@utils/lib/utils'
import { Breadcrumb } from './breadcrumb'
import { Typography } from '../Typography'

interface PageHeaderProps {
  title: string;
  description?: string;
  breadcrumbs?: {
    label: string;
    href?: string;
    icon?: React.ReactNode;
  }[];
  actions?: React.ReactNode;
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  actionsClassName?: string;
  breadcrumbsClassName?: string;
}

/**
 * A standardized page header component for consistent page headers
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  breadcrumbs,
  actions,
  className,
  titleClassName,
  descriptionClassName,
  actionsClassName,
  breadcrumbsClassName,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {breadcrumbs && (
        <Breadcrumb
          items={breadcrumbs}
          className={cn('mb-4', breadcrumbsClassName)}
        />
      )}

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-2">
          <Typography
            variant="h1"
            className={cn('text-3xl font-bold tracking-tight', titleClassName)}
          >
            {title}
          </Typography>

          {description && (
            <Typography
              variant="p"
              className={cn('text-muted-foreground', descriptionClassName)}
            >
              {description}
            </Typography>
          )}
        </div>

        {actions && (
          <div className={cn('mt-4 sm:mt-0 flex-shrink-0', actionsClassName)}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};
