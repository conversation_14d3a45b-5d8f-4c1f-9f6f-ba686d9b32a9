import React from 'react'
import NextImage, { ImageProps as NextImageProps } from 'next/image'
import { cn } from '@utils/lib/utils'

interface ImageProps extends Omit<NextImageProps, 'alt'> {
  alt: string;
  className?: string;
  rounded?: boolean | 'sm' | 'md' | 'lg' | 'full';
  aspectRatio?: '1:1' | '4:3' | '16:9' | '21:9';
  objectFit?: 'contain' | 'cover' | 'fill';
  overlay?: boolean;
  overlayColor?: string;
  overlayOpacity?: number;
}

/**
 * A standardized image component that wraps Next.js's Image component
 * with consistent props and styling
 */
export const Image: React.FC<ImageProps> = ({
  alt,
  className,
  rounded = false,
  aspectRatio,
  objectFit = 'cover',
  overlay = false,
  overlayColor = 'black',
  overlayOpacity = 0.5,
  ...props
}) => {
  // Determine rounded corners class
  const roundedClass = rounded
    ? rounded === true
      ? 'rounded-lg'
      : rounded === 'sm'
      ? 'rounded-sm'
      : rounded === 'md'
      ? 'rounded-md'
      : rounded === 'lg'
      ? 'rounded-lg'
      : rounded === 'full'
      ? 'rounded-full'
      : ''
    : '';

  // Determine aspect ratio class
  const aspectRatioClass = aspectRatio
    ? aspectRatio === '1:1'
      ? 'aspect-square'
      : aspectRatio === '4:3'
      ? 'aspect-[4/3]'
      : aspectRatio === '16:9'
      ? 'aspect-[16/9]'
      : aspectRatio === '21:9'
      ? 'aspect-[21/9]'
      : ''
    : '';

  // Determine object fit class
  const objectFitClass = objectFit
    ? objectFit === 'contain'
      ? 'object-contain'
      : objectFit === 'cover'
      ? 'object-cover'
      : objectFit === 'fill'
      ? 'object-fill'
      : ''
    : '';

  return (
    <div className={cn('relative', aspectRatioClass, roundedClass, className)}>
      <NextImage
        alt={alt}
        className={cn(objectFitClass, roundedClass)}
        {...props}
      />

      {overlay && (
        <div
          className={cn('absolute inset-0', roundedClass)}
          style={{
            backgroundColor: overlayColor,
            opacity: overlayOpacity
          }}
        />
      )}
    </div>
  );
};
