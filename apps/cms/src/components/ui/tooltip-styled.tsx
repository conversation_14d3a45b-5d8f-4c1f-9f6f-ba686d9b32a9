import React from 'react'
import { cn } from '@utils/lib/utils'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'

interface TooltipStyledProps {
  children: React.ReactNode;
  content: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  variant?: 'default' | 'info' | 'success' | 'warning' | 'error';
  className?: string;
  contentClassName?: string;
  delayDuration?: number;
  skipDelayDuration?: number;
}

/**
 * A standardized tooltip component for consistent help text
 */
export const TooltipStyled: React.FC<TooltipStyledProps> = ({
  children,
  content,
  side = 'top',
  align = 'center',
  variant = 'default',
  className,
  contentClassName,
  delayDuration = 300,
  skipDelayDuration = 300,
}) => {
  // Variant classes for the tooltip content
  const variantClasses = {
    default: '',
    info: 'bg-blue-800 text-neutral-subtle',
    success: 'bg-green-800 text-neutral-subtle',
    warning: 'bg-yellow-800 text-neutral-subtle',
    error: 'bg-red-800 text-neutral-subtle',
  };

  return (
    <TooltipProvider
      delayDuration={delayDuration}
      skipDelayDuration={skipDelayDuration}
    >
      <Tooltip>
        <TooltipTrigger asChild className={className}>
          {children}
        </TooltipTrigger>
        <TooltipContent
          side={side}
          align={align}
          className={cn(variantClasses[variant], contentClassName)}
        >
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
