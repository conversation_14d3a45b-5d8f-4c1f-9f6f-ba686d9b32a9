import React from 'react'
import { cn } from '@utils/lib/utils'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
  homeIcon?: boolean;
}

/**
 * A standardized breadcrumb component for consistent navigation
 */
export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  className,
  separator = <ChevronRight className="h-4 w-4 text-muted-foreground" />,
  homeIcon = true,
}) => {
  return (
    <nav className={cn('flex', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => {
          const isFirst = index === 0;
          const isLast = index === items.length - 1;

          // Determine if we should show the home icon
          const showHomeIcon = isFirst && homeIcon && !item.icon;

          // Create the breadcrumb item content
          const itemContent = (
            <div className="flex items-center">
              {showHomeIcon && <Home className="h-4 w-4 mr-1" />}
              {item.icon && <span className="mr-1">{item.icon}</span>}
              <span
                className={cn(
                  isLast ? 'font-medium text-foreground' : 'text-muted-foreground'
                )}
              >
                {item.label}
              </span>
            </div>
          );

          return (
            <li
              key={index}
              className={cn('flex items-center', !isLast && 'text-sm')}
            >
              {index > 0 && <span className="mx-2">{separator}</span>}

              {item.href && !isLast ? (
                <Link
                  href={item.href}
                  className="hover:text-foreground transition-colors"
                >
                  {itemContent}
                </Link>
              ) : (
                itemContent
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};
