import React from 'react'
import { cn } from '@utils/lib/utils'

interface CardGenericProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'outline' | 'glass' | 'highlight';
  hover?: boolean;
  onClick?: () => void;
}

/**
 * A standardized generic card component using Tailwind
 * This is different from the Card component which is specifically for blog posts
 */
export const CardGeneric: React.FC<CardGenericProps> = ({
  children,
  className,
  variant = 'default',
  hover = true,
  onClick,
}) => {
  const variantClasses = {
    default: 'bg-card text-card-foreground border-standard',
    outline: 'border-subtle bg-transparent',
    glass: 'glass-effect-lit relative group',
    highlight: 'border-primary-subtle bg-brand/5',
  };

  const hoverClasses = hover
    ? 'transition-standard hover-lift-subtle shadow-subtle hover:shadow-medium'
    : '';

  return (
    <div
      className={cn(
        'rounded-standard p-6',
        variantClasses[variant],
        hoverClasses,
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {variant === 'glass' && (
        <>
          {/* Additional lighting effect on hover */}
          <div className="absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-0" style={{ background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 70%)' }}></div>
        </>
      )}
      <div className="relative z-10">{children}</div>
    </div>
  );
};
