import React from 'react'
import { cn } from '@utils/lib/utils'
import { Badge } from '@ui/components/ui/badge'

interface BadgeStyledProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'outline' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  icon?: React.ReactNode;
}

/**
 * A standardized badge component for consistent labeling
 */
export const BadgeStyled: React.FC<BadgeStyledProps> = ({
  children,
  variant = 'default',
  size = 'md',
  rounded = false,
  icon,
  className,
  ...props
}) => {
  // Map our custom variants to base Badge variants
  const variantMap = {
    default: 'default',
    secondary: 'secondary',
    outline: 'outline',
    success: 'outline',
    warning: 'outline',
    error: 'outline',
    info: 'outline',
  };

  // Additional styling based on variant
  const additionalClasses = {
    default: '',
    secondary: '',
    outline: '',
    success: 'border-green-500 text-green-700 bg-green-50',
    warning: 'border-yellow-500 text-yellow-700 bg-yellow-50',
    error: 'border-red-500 text-red-700 bg-red-50',
    info: 'border-blue-500 text-blue-700 bg-blue-50',
  };

  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1',
  };

  return (
    <Badge
      variant={variantMap[variant] as "default" | "secondary" | "outline" | "destructive"}
      className={cn(
        sizeClasses[size],
        additionalClasses[variant],
        rounded && 'rounded-full',
        'inline-flex items-center',
        className
      )}
      {...props}
    >
      {icon && <span className="mr-1">{icon}</span>}
      {children}
    </Badge>
  );
};
