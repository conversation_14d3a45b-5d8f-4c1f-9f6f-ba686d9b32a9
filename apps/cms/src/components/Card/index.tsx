'use client'
import { cn } from '@/utilities/ui'
import useClickableCard from '@/utilities/useClickableCard'
import Link from 'next/link'
import React, { Fragment } from 'react'

import type { Post } from '@/payload-types'
import NextImage from 'next/image'

export type CardPostData = Pick<Post, 'slug' | 'categories' | 'meta' | 'title' | 'pinned'>

export const Card: React.FC<{
  alignItems?: 'center'
  className?: string
  doc?: CardPostData
  relationTo?: 'posts'
  showCategories?: boolean
  title?: string
}> = (props) => {
  const { card, link } = useClickableCard({})
  const { className, doc, relationTo, showCategories, title: titleFromProps } = props

  const { slug, categories, meta, title, pinned } = doc || {}
  const { description, image: metaImage } = meta || {}

  const hasCategories = categories && Array.isArray(categories) && categories.length > 0
  const titleToUse = titleFromProps || title
  const sanitizedDescription = description?.replace(/\s/g, ' ') // replace non-breaking space with white space
  const href = `/${relationTo}/${slug}`

  return (
    <article
      className={cn(
        'border border-border rounded-lg overflow-hidden bg-card hover:cursor-pointer transition-all duration-300 hover:shadow-md hover:-translate-y-1 hover:border-border/80',
        className,
      )}
      ref={card.ref}
    >
      <div className="relative w-full text-center overflow-hidden group">
        {!metaImage && <div className="p-4 opacity-75">Please supply an Image for this Post</div>}
        {metaImage && typeof metaImage !== 'string' && (
          <div className="overflow-hidden">
            <NextImage src={(metaImage as any).sizes.medium.url} alt={titleToUse||''} fill className="absolute object-cover inset-0 " />
          </div>
        )}
      </div>
      <div className="p-5">
        <div className="flex justify-between items-start">
          {showCategories && hasCategories && (
            <div className="uppercase text-sm mb-4">
              {showCategories && hasCategories && (
                <div>
                  {categories?.map((category, index) => {
                    if (typeof category === 'object') {
                      const { title: titleFromCategory } = category

                      const categoryTitle = titleFromCategory || 'Untitled category'

                      const isLast = index === categories.length - 1

                      return (
                        <Fragment key={index}>
                          {categoryTitle}
                          {!isLast && <Fragment>, &nbsp;</Fragment>}
                        </Fragment>
                      )
                    }

                    return null
                  })}
                </div>
              )}
            </div>
          )}
          {pinned && (
            <div className="bg-brand/10 text-brand px-2 py-1 rounded-md text-xs font-medium">
              Pinned
            </div>
          )}
        </div>
        {titleToUse && (
          <div className="prose">
            <h3>
              <Link className="not-prose hover:text-brand transition-colors duration-300" href={href} ref={link.ref}>
                {titleToUse}
              </Link>
            </h3>
          </div>
        )}
        {description && <div className="mt-2 text-foreground/80 text-sm">{description && <p>{sanitizedDescription}</p>}</div>}
      </div>
    </article>
  )
}
