// app/actions/subscribeAction.ts
'use server'

import dns from 'dns/promises'

export async function hasMxRecords(email: string) {
  // Basic check for '@'
  if (!email.includes('@')) return false

  // Extract the domain from the email
  const domain = email.split('@')[1]

  try {
    // Resolve MX records for the domain
    const mxRecords = await dns.resolveMx(domain!)
    // If there is at least one MX record, the domain is set up to receive email
    return mxRecords && mxRecords.length > 0
  } catch (error) {
    console.error('Error during MX lookup:', error)
    // In case of an error (e.g., domain does not exist), consider it as no MX records found
    return false
  }
}

export async function subscribeAction(formData: FormData): Promise<{
  status: 'signed-up' | 'already-signed-up' | 'error' | 'invalid'
}> {
  const email = formData.get('email')

  if (!email || typeof email !== 'string' || !await hasMxRecords(email)) {
    return { status: 'invalid' }
  }

  // Retrieve Mailchimp credentials from environment variables
  const MAILCHIMP_API_KEY = process.env.MAILCHIMP_API_KEY
  const MAILCHIMP_LIST_ID = process.env.MAILCHIMP_LIST_ID
  if (!MAILCHIMP_API_KEY || !MAILCHIMP_LIST_ID) {
    throw new Error('Mailchimp credentials are not configured')
  }

  // Extract the Mailchimp datacenter from the API key (format: <key>-<dc>)
  const DATACENTER = MAILCHIMP_API_KEY.split('-')[1]
  const url = `https://${DATACENTER}.api.mailchimp.com/3.0/lists/${MAILCHIMP_LIST_ID}/members`

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `apikey ${MAILCHIMP_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email_address: email,
      status: 'subscribed', // Use "pending" if you want a double-opt-in flow
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    if (errorData.detail.indexOf('already a list') > -1) {
      return { status: 'already-signed-up' }
    } else {
      console.error('Error subscribing to Mailchimp:', errorData)
      return { status: 'error' }
    }
  }

  return { status: 'signed-up' }
}
