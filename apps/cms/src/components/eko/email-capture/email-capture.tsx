'use client'
import React, { useEffect, useState } from 'react'
import { Input } from '@ui/components/ui/input'
import { Button } from '@ui/components/ui/button'
import { subscribeAction } from '@/components/eko/email-capture/server-actions'
import { useToast } from '@ui/hooks/use-toast'
import { usePlausible } from 'next-plausible'

interface EmailCaptureProps {
  buttonLabel?: string;
  placeholder?: string;
  signUpMessage?: string;
  onSuccess?: (email: string) => void;

}

export const EmailCapture: React.FC<EmailCaptureProps> = ({
                                                            buttonLabel,
                                                            placeholder,
                                                            signUpMessage,
  onSuccess,

                                                          }) => {

  const [email, setEmail] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [hasGivenEmail, setHasGivenEmail] = useState<string|null>(null);

  useEffect(() => {
    setHasGivenEmail(localStorage.getItem('has-given-email'))
  }, [])
  const { toast } = useToast()
  const plausible = usePlausible();

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/
    return re.test(email)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!validateEmail(email)) {
      setError('Please enter a valid email address')
      return
    }
    setError('')
    setLoading(true)

    // Create FormData from the form. Note that the input "name" attribute must match what the server action expects.
    const formData = new FormData(e.currentTarget)

    try {
      // Call the server action which subscribes the user to Mailchimp
      const result = await subscribeAction(formData)
      if (result.status === 'already-signed-up' || result.status === 'signed-up') {
        setError('')
        setLoading(false)
        setHasGivenEmail(email)
        toast({
          title: 'Thank you!',
          description: signUpMessage || 'You\'re all signed up.',
        })
        plausible('Email Capture', { props: { email } })
        onSuccess?.(email)
      } else if (result.status === 'invalid') {
        toast({
          title: 'Email Address Invalid',
          description: 'Please enter a valid email address.',
          variant: 'destructive',
        })
        setError('Please enter a valid email address')
      } else {
        toast({
          title: 'Error',
          description: 'There was an error signing you up. Please try again later.',
          variant: 'destructive',
        })
      }
    } catch (err: any) {
      console.error(err)
      setError(err.message || 'An error occurred. Please try again later.')
    } finally {
      setLoading(false)
    }
  }


  return (
    <div className="mx-auto w-full max-w-md space-y-3">
      <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <Input
            type="email"
            name="email"
            value={email}
            onChange={(e) => {
              setError('')
              setEmail(e.target.value)
            }}
            disabled={hasGivenEmail !== null}
            placeholder={placeholder}
            className="w-full rounded-md border border-foreground/10 bg-background/70 px-4 py-3 text-foreground shadow-sm focus:outline-none focus:ring-1 focus:ring-brand focus:border-transparent h-12"
          />
        </div>

        <Button
          type="submit"
          variant="default"
          className="h-12 rounded-md bg-brand-gradient-accent hover:bg-brand-dark text-white transition-all duration-200 shadow-sm hover:shadow-md px-6 whitespace-nowrap font-medium"
          disabled={!!loading || hasGivenEmail !== null}
        >
          {loading ? (
            <span className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Sending...</span>
            </span>
          ) : hasGivenEmail ? (
            <span className="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Signed Up</span>
            </span>
          ) : buttonLabel}
        </Button>
      </form>

      {error && (
        <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}

      <p className="text-xs text-foreground/60 text-center mt-2">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  )
}
