'use client'
import { cn } from '@utils/lib/utils'
import React, { ReactNode, useEffect, useState } from 'react'
import { Media } from '@/payload-types'
import Image from 'next/image'
import { motion } from 'framer-motion'

interface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {
  children?: ReactNode;
  showRadialGradient?: boolean;
  animated?: boolean;
  slow?: boolean;
  show?: boolean;
  darkMode?: boolean;
  backgroundImage?: Media;
  subtle?: boolean;
  darkOverlay?: boolean;
  colorScheme?: React.CSSProperties;
  parallax?: boolean;
  parallaxIntensity?: number;
  showAurora?: boolean;
}

const blurDataURL = 'data:image/webp;base64,UklGRoADAABXRUJQVlA4WAoAAAAgAAAAjQAATwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDggkgEAAJAQAJ0BKo4AUAA+3VKeTb05rSY2vHzroBuJZwb458LHmQ3HfEiP+9lsmA3GlMA0zyvD0NpsDJ8MjbhyEgZq9uLU+XlzLO2xSG274se6lohE9/AvxsYMikFcawuIYzq8v4J+CdSK0HTSugGfWCICVO5r+ztU8Impu2W7WqojbvMPEeSfqw9NWXbGUeYKAAD+x33Z9LvZIPozXxZBjqeu2IHGBrDsMmFDDNFwXfSlYYzGmxFAsmDMr2DBXQkM1PDrvm5te/sNqwyDM0XmfVQWBAQE/Wj33dv92JaxxKoGbFLaCFi65/SJffm9c3tpcXhyUP/xi0a+jT6uxbZSS/zWipy2FSldi0/0Eed2dtVZiA2W2cDLbEhbmKYG4G1TSaVj/Fr5oCKBybVPJYdk5odFWmEnlvN1lNocGHTVTxHi1BxyxoqyBtrHK+gUTNCEwN9pfC7aONn8qhJXr2raozSet/8Hqq+f8YKG7kDeVuMWisCVrbrm6ykTi0KbkHFuSd3w+QmRN1YeCrc6ONR/ckXpYw4AAA=='

export const AuroraBackground = ({
                                   className,
                                   children,
                                   showRadialGradient = false,
                                   animated = false,
                                   slow = false,
                                   show = true,
                                   darkMode = false,
                                   showAurora = true,
                                   subtle = false,
                                   backgroundImage,
                                   darkOverlay = false,
                                   parallax = false,
                                   parallaxIntensity = 0.3,
                                   colorScheme = {
                                     '--aurora-color-1': 'hsl(145 27% 20%)',
                                     '--aurora-color-2': 'hsl(145 27% 20%)',
                                     '--aurora-color-3': 'hsl(145 27% 30%)',
                                     '--aurora-color-4': 'hsl(145 27% 40%)',
                                     '--aurora-color-5': 'hsl(145 27% 50%)',
                                     '--black': 'var(--slate-900)',
                                     '--white': 'var(--slate-50)',
                                     '--transparent': 'transparent',
                                   } as React.CSSProperties,
                                 }: AuroraBackgroundProps) => {
  const [currentImage, setCurrentImage] = useState<Media | null>(backgroundImage || null)
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false)
  const [thumbnailLoaded, setThumbnailLoaded] = useState<boolean>(false)
  const [scrollY, setScrollY] = useState<number>(0)
  const [initialScrollY, setInitialScrollY] = useState<number>(0)
  const blockRef = React.useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (!backgroundImage || currentImage?.url) return

    // If this is a new image, store the current one as previous and update current
    if (backgroundImage?.url !== currentImage?.url) {
      setCurrentImage(backgroundImage)
      setIsTransitioning(true)

      // Preload all image sizes for smoother transitions
      const preloadImages = () => {
        // Preload thumbnail for quick initial display
        const thumbnailImage = new window.Image()
        thumbnailImage.src = backgroundImage.sizes?.thumbnail?.url || ''

        // Preload high quality image
        const highQualityImage = new window.Image()
        highQualityImage.src = backgroundImage.sizes?.large?.url || backgroundImage.url || ''

        return [highQualityImage, thumbnailImage]
      }

      const [highQualityImage, thumbnailImage] = preloadImages()

      const completeTransition = () => {
        // Match the transition duration with the animation duration
        setTimeout(() => {
          setIsTransitioning(false)
        }, 2000) // 2 seconds to match the motion.div transition duration
      }

      // Handle both cached and non-cached scenarios
      if (thumbnailImage!.complete) {
        // Image is already cached - still delay slightly for smooth transition
        setThumbnailLoaded(true)
      } else {
        // Image needs to be loaded
        thumbnailImage!.onload = () => {
          setThumbnailLoaded(true)
        }
      }
      // Handle both cached and non-cached scenarios
      if (highQualityImage!.complete) {
        // Image is already cached - still delay slightly for smooth transition
        setTimeout(completeTransition, 100)
      } else {
        // Image needs to be loaded
        highQualityImage!.onload = completeTransition
      }
    }
  }, [backgroundImage, currentImage])

  useEffect(() => {
    setInitialScrollY(window.scrollY)

  }, [])
  // Add parallax scroll effect with optimized performance
  useEffect(() => {
    if (!parallax) return

    // Use a throttled scroll handler to reduce calculations
    let ticking = false
    let lastKnownScrollPosition = 0

    const handleScroll = () => {
      lastKnownScrollPosition = window.scrollY

      window.requestAnimationFrame(() => {
        if (blockRef?.current) {
          const rect = blockRef.current.getBoundingClientRect()
          setScrollY(rect.top)
        }
      })
    }


    // Add scroll event listener with passive flag for better performance
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Initial scroll position
    handleScroll()

    // Clean up event listener
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [parallax, scrollY])

  return show ? (
    <motion.div
      ref={blockRef}
      data-aurora="true"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, ease: [0.2, 0, 0.2, 1] }}
      className={cn(
        'relative max-h-full text-slate-950 transition-bg pointer-events-none overflow-hidden',
        className,
      )}
      style={{
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        WebkitBackfaceVisibility: 'hidden',
      }}
    >

      <div
        className={cn('overflow-hidden absolute inset-0 transform-gpu', 'z-0')}
        style={parallax ? {
          transform: `translate3d(0, ${-scrollY * parallaxIntensity}px, 0) scale(1.5)`, // Keep parallax effect for background image
          transition: 'transform 0.2s cubic-bezier(0.2, 0, 0.2, 1)', // Smoother easing curve
          willChange: 'transform',
          backfaceVisibility: 'hidden',
          WebkitBackfaceVisibility: 'hidden',
          perspective: 1000,
          WebkitPerspective: 1000,
          transformStyle: 'preserve-3d',
        } : { transform: 'translate3d(0, 0, 0) scale(1.1)', willChange: 'transform' }}
      >
        <div className="absolute inset-0 bg-black/20 z-1"></div>

        {/* Initial blurred placeholder - always present */}
        <motion.div
          className="absolute inset-0 z-[1]   scale-110"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2, ease: [0.2, 0, 0.2, 1] }}
          style={{
            backgroundImage: backgroundImage ? blurDataURL : 'none',
            transform: 'translate3d(0, 0, 0)',
            willChange: 'transform, opacity',
          }}
        >
          {backgroundImage && thumbnailLoaded && scrollY == 0 &&
            <motion.div initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2, ease: [0.2, 0, 0.2, 1] }}
            >
              <Image
                priority
                fetchPriority="high"
                src={currentImage?.sizes?.thumbnail?.url || blurDataURL}
                alt="Background placeholder"
                fill
                sizes="100vw"
                quality={30}
                className="absolute object-cover scale-110 blur-sm transform-gpu"
                style={{
                  objectPosition: 'center center',
                  objectFit: 'cover',
                  transform: 'translate3d(0, 0, 0)',
                  transition: 'filter 0.5s cubic-bezier(0.2, 0, 0.2, 1)',
                  willChange: 'transform, filter',
                }}
              />
            </motion.div>
          }
        </motion.div>

        {/* Current image (fades in over previous) */}
        {currentImage && (
          <motion.div
            className="absolute inset-0 z-[3] scale-110 transform-gpu"
            initial={{ opacity: 0 }}
            animate={{ opacity: isTransitioning ? 0 : 1 }}
            transition={{
              duration: 2,
              ease: [0.2, 0, 0.2, 1],
              delay: isTransitioning ? 0.5 : 0, // Slight delay for smoother transition
            }}
            style={{
              willChange: 'opacity, transform',
              transform: 'translate3d(0, 0, 0)',
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
            }}
          >
            {currentImage &&
              <Image
                src={currentImage.url!}
                alt={currentImage.alt || ''}
                fill
                sizes="100vw"
                quality={90}
                className="absolute object-cover scale-125 "
                style={{
                  objectPosition: 'center center',
                  objectFit: 'cover',
                  transform: 'translate3d(0, 0, 0)',
                  transition: 'transform 0.3s cubic-bezier(0.2, 0, 0.2, 1)',
                  willChange: 'transform',
                  backfaceVisibility: 'hidden',
                  WebkitBackfaceVisibility: 'hidden',
                }}
              />
            }
          </motion.div>
        )}
      </div>

      {/* Enhanced Aurora effect overlay with stronger, more vibrant gradient */}
      {/*  <div*/}
      {/*    className={cn(*/}
      {/*      `*/}
      {/*        [--white-gradient:repeating-linear-gradient(100deg,var(--green-500)_0%,var(--green-500)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--green-500)_16%)]*/}
      {/*        [--dark-gradient:repeating-linear-gradient(100deg,var(--green-800)_0%,var(--green-800)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--green-800)_16%)]*/}
      {/*        [--aurora:repeating-linear-gradient(100deg,var(--green-300)_10%,var(--green-200)_15%,var(--green-400)_20%,var(--green-500)_25%,var(--green-300)_30%)]*/}
      {/*      [--white-gradient-subtle:repeating-linear-gradient(100deg,var(--green-50)_0%,var(--green-50)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--green-50)_16%)]*/}
      {/*      [--dark-gradient-subtle:repeating-linear-gradient(100deg,var(--green-800)_0%,var(--green-950)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--green-950)_16%)]*/}
      {/*      [--aurora-subtle:repeating-linear-gradient(100deg,var(--green-50)_10%,var(--green-100)_15%,var(--green-50)_20%,var(--green-100)_25%,var(--green-50)_30%)]`,*/}

      {/*      (darkMode ? `[background-image:var(--dark-gradient),var(--aurora)]` :*/}
      {/*        (subtle ? `[background-image:var(--white-gradient-subtle),var(--aurora-subtle)] dark:[background-image:var(--dark-gradient-subtle),var(--aurora-subtle)]` : `[background-image:var(--white-gradient),var(--aurora)] dark:[background-image:var(--dark-gradient),var(--aurora)]`)),*/}
      {/*      (darkMode ? `after:[background-image:var(--dark-gradient),var(--aurora)]`: `after:[background-image:var(--white-gradient),var(--aurora)] after:dark:[background-image:var(--dark-gradient),var(--aurora)]`),*/}
      {/*      (darkMode ? `after:mix-blend-normal` : 'after:mix-blend-difference dark:after:mix-blend-normal'),*/}
      {/*      (darkMode ? `invert-0` :`invert dark:invert-0` ),*/}
      {/*      `[background-size:250%,_200%]*/}
      {/*        [background-position:50%_50%,50%_50%]*/}
      {/*        filter blur-[12px]*/}
      {/*        after:content-[""] after:absolute after:inset-0*/}
      {/*        after:[background-size:200%,_100%]*/}
      {/*        after:[background-attachment:fixed]*/}
      {/*        pointer-events-none*/}
      {/*        absolute -inset-0 will-change-transform`,*/}
      {/*      subtle ? 'opacity-10' : 'opacity-50',*/}


      {/*    showRadialGradient &&*/}
      {/*      `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,var(--transparent)_80%)]`,*/}

      {/*      animated && (slow ? 'after:animate-aurora-slow' : 'after:animate-aurora '),*/}
      {/*    )}*/}
      {/*  >*/}
      {/*  </div>*/}


      <div
        className="absolute inset-0 overflow-hidden"
        style={{
          ...colorScheme as React.CSSProperties,
          transform: 'translateZ(0) scale(1.05)', // Aurora effect should remain fixed during parallax
          willChange: 'transform',
        }}
      >
        {showAurora && (
          <motion.div
            className={cn(
              ` after:animate-aurora
                pointer-events-none
                absolute -inset-0
                [background-size:300%,_200%]
                [background-position:50%_50%,50%_50%]
                opacity-50 blur-[10px] invert filter will-change-transform will-change-opacity
                [--aurora:repeating-linear-gradient(100deg,var(--aurora-color-5)_10%,var(--aurora-color-1)_15%,var(--aurora-color-3)_20%,var(--aurora-color-2)_25%,var(--aurora-color-4)_30%)]
                [--dark-gradient:repeating-linear-gradient(100deg,var(--black)_0%,var(--black)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--black)_16%)]
                [--white-gradient:repeating-linear-gradient(100deg,var(--white)_0%,var(--white)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--white)_16%)]
                after:absolute
                after:inset-0
                after:[background-size:200%,_100%]
                after:[background-attachment:fixed] after:will-change-transform after:[transform:translate3d(0,0,0)]
                after:mix-blend-difference after:content-[""]
                dark:invert-0
                [background-image:var(--white-gradient),var(--aurora)]
                dark:[background-image:var(--dark-gradient),var(--aurora)]
                after:[background-image:var(--white-gradient),var(--aurora)]
                after:dark:[background-image:var(--dark-gradient),var(--aurora)]
                `, subtle ? 'opacity-40' : 'opacity-70',

              showRadialGradient &&
              `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,var(--transparent)_70%)]`,
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: subtle ? 0.4 : 0.7 }}
            transition={{ duration: 1, ease: [0.2, 0, 0.2, 1] }}
          ></motion.div>)}
      </div>

      {darkOverlay && (
        <div className="absolute inset-0 bg-brand-gradient-dark opacity-50 transition-standard z-2"></div>
      )}
      <div className="pointer-events-auto">
        {children}
      </div>
    </motion.div>
  ) : (<div className={className}>{children}</div>)
}
