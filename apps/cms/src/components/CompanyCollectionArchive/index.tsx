import { cn } from '@/utilities/ui'
import React from 'react'

import { CardPostData } from '@/components/Card'
import CompanyReportCard from '@/components/CompanyReportCard'
import { getPayload } from 'payload'
import configPromise from '@payload-config'

export type Props = {
  companyReports: CardPostData[]
}

export const CompanyCollectionArchive: React.FC<Props> = async (props) => {
  const { companyReports } = props
  const payload = await getPayload({ config: configPromise })

  const companyReportsUpdated= companyReports.map(async (report) => {
    return await payload.find({
      collection: 'company-reports',
      depth: 4, // We need sufficient depth to get nested data
      limit: 1,
      overrideAccess: false,
      where: {
        slug: {
          equals: report.slug,
        },
      },
    }).then((result) => result.docs[0]);

  })
  return (
    <div className={cn('container')}>
      <div>
        <div className="grid grid-cols-4 sm:grid-cols-8 lg:grid-cols-12 gap-y-4 gap-x-4 lg:gap-y-8 lg:gap-x-8 xl:gap-x-8">
          {companyReportsUpdated?.map(async (result, index) => {
            const asyncResult = await result
            if (typeof asyncResult === 'object' && asyncResult !== null) {
              return (
                <div className="col-span-4" key={index}>
                  <CompanyReportCard  report={asyncResult}  />
                </div>
              )
            }

            return null
          })}
        </div>
      </div>
    </div>
  )
}
