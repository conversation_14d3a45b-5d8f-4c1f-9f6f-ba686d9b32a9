import React, { CSSProperties } from 'react'
import { cn } from '@utils/lib/utils'
import { AuroraBackground } from '@/components/eko/aurora'
import type { Media } from '@/payload-types'

interface BlockWrapperProps {
  children: React.ReactNode,
  className?: string,
  inset?: boolean | null,
  background?: boolean | null,
  backgroundMedia?: Media | null,
  darkMode?: boolean | null,
  subtle?: boolean | null,
  id?: string,
  containerClassName?: string,
  noPadding?: boolean | null,
  sectionSpacingClassName?: string,
  enableParallax?: boolean,
  parallaxIntensity?: number,
  showAurora?: boolean,
  colorScheme?: CSSProperties,
  showRadialGradient?: boolean,
  slow?: boolean,
}

/**
 * A standardized wrapper for all content blocks
 * Provides consistent spacing, background handling, and inset styling
 */
export const BlockWrapper: React.FC<BlockWrapperProps> = ({
                                                            children,
                                                            className,
                                                            inset = false,
                                                            background = false,
                                                            backgroundMedia = null,
                                                            darkMode = false,
                                                            subtle = true,
                                                            id,
                                                            containerClassName,
                                                            noPadding = false,
                                                            sectionSpacingClassName,
                                                            enableParallax = false,
                                                            parallaxIntensity = 0.05,
                                                            showAurora = true,
                                                            colorScheme,
                                                            showRadialGradient = false,
                                                            slow = false,
                                                          }) => {
  // Determine if we need dark text based on background
  const needsDarkText = !!backgroundMedia || (background && darkMode)

  return (
    <AuroraBackground
      className={cn(
        'relative',
        inset ? 'bg-gradient-to-b from-neutral-subtle/5 via-transparent to-neutral-subtle/5' : '',
        sectionSpacingClassName || 'section-spacing-medium',
        className,
      )}
      animated
      showRadialGradient={showRadialGradient}
      show={background === true || !!backgroundMedia}
      darkMode={darkMode === true}
      subtle={subtle === true || !backgroundMedia}
      slow={slow === true}
      backgroundImage={backgroundMedia || undefined}
      darkOverlay={!!backgroundMedia}
      parallax={enableParallax}
      parallaxIntensity={parallaxIntensity}
      showAurora={showAurora}
      id={id}
      colorScheme={colorScheme}
    >

      <section className={cn(
        'w-full z-10 transition-standard',
        !noPadding && 'sm:container',
        needsDarkText ? 'text-background' : 'text-foreground',
        containerClassName,
      )}>
        <div className={cn(
          !noPadding && 'p-2 sm:px-4 md:px-6',
          'transition-standard',
        )}>
          <div className="absolute inset-0 bg-noise opacity-[0.02] mix-blend-darken pointer-events-none"></div>

          {children}
        </div>
      </section>
    </AuroraBackground>
  )
}
