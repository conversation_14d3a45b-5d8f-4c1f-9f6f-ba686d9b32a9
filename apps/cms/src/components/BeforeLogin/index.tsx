import React from 'react'

/**
 * Clean login component that just wraps the PayloadCMS login form
 */
const BeforeLogin: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="flex min-h-screen w-full items-center justify-center p-4 md:p-8">
      <div className="flex w-full max-w-md overflow-hidden rounded-large shadow-medium bg-background p-8">
        <div className="flex flex-1 flex-col">
          <div className="w-full">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BeforeLogin
