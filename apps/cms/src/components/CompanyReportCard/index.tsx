'use client'
import React from 'react'
import Link from 'next/link'
import { CompanyReport } from '@/payload-types'

// Define the expected structure of a company report
export interface CompanyReportCardProps {
  report:CompanyReport
}

export const CompanyReportCard: React.FC<CompanyReportCardProps> = ({ report }) => {
  // Find the company profile block to get company info
  const profileBlock = report.blocks?.find(block => block.blockType === 'cr-profile')
  const companyData = profileBlock?.company_data
  const graphBlock = report.blocks?.find(block => block.blockType === 'cr-overview-graphs')

  return (
    <Link href={"/company-reports/"+report.slug} className="block h-full">
      <article className="h-full group relative flex flex-col overflow-hidden transition-all duration-300 bg-white dark:bg-gray-800
                        rounded-xl border border-foreground/10 backdrop-blur-sm hover:border-brand/20
                        shadow-sm hover:shadow-md transform hover:-translate-y-1 hover:cursor-pointer">
        {/* Glass effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-transparent opacity-0
                       group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Decorative accent line */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-brand/30 to-transparent
                       transform translate-y-0 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

        <div className="px-6 pt-6 flex flex-col flex-grow z-10">
          {report.title && (
            <div className="mb-2">
              <h3 className="text-xl font-bold text-neutral-dark dark:text-neutral-subtle group-hover:text-brand transition-colors duration-300">
                {report.title}
              </h3>
            </div>
          )}

          {/* Company metadata */}
          <div className="flex flex-wrap items-center gap-2 mb-3">
            {companyData?.url && (
              <span className="inline-flex items-center bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-xs text-gray-700 dark:text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9" />
                </svg>
                {new URL(companyData.url).hostname.replace('www.', '')}
              </span>
            )}

            {companyData?.cn && (
              <span className="inline-flex items-center bg-brand/10 px-2 py-1 rounded text-xs text-brand-dark dark:text-brand">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                {companyData.cn}
              </span>
            )}

            {/* ESG Metrics */}
            {graphBlock && (
              <div className="flex gap-2 flex-wrap">
                {(() => {
                  try {
                    // Get the graph data (might be string or already parsed)
                    const graphData = typeof graphBlock.data === 'string'
                      ? JSON.parse(graphBlock.data)
                      : (graphBlock.data || {});

                    // Extract metrics from the actual data structure
                    const finalScore = graphData?.final_score;
                    const ratingText = graphData?.rating_text;
                    const minorMajorText = graphData?.minor_major_text;
                    const redFlags = Array.isArray(graphData?.red_flags) ? graphData.red_flags.length : 0;
                    const greenFlags = Array.isArray(graphData?.green_flags) ? graphData.green_flags.length : 0;

                    return (
                      <>
                        {/* Combined Score and Rating */}
                        {finalScore !== undefined && (
                          <span className={`inline-flex items-center px-2 py-1 rounded text-xs
                                         ${finalScore >= 70 ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                                          finalScore >= 50 ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300' :
                                          'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            Score: {finalScore} {ratingText && `(${ratingText})`}
                          </span>
                        )}

                        {/* Flag counts if available */}
                        {redFlags > 0 && (
                          <span className="inline-flex items-center bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded text-xs text-red-800 dark:text-red-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
                            </svg>
                            {redFlags} Red
                          </span>
                        )}

                        {greenFlags > 0 && (
                          <span className="inline-flex items-center bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded text-xs text-green-800 dark:text-green-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
                            </svg>
                            {greenFlags} Green
                          </span>
                        )}

                        {/* Fallback if no specific metrics found */}
                        {!finalScore && !redFlags && !greenFlags && (
                          <span className="inline-flex items-center bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded text-xs text-teal-800 dark:text-teal-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            ESG Analysis
                          </span>
                        )}
                      </>
                    );
                  } catch (error) {
                    // Fallback for any parsing errors
                    return (
                      <span className="inline-flex items-center bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded text-xs text-teal-800 dark:text-teal-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        ESG Analysis
                      </span>
                    );
                  }
                })()}
              </div>
            )}
          </div>

          <div className="mt-2 text-gray-700 dark:text-gray-300 line-clamp-3 text-sm">
            {companyData?.description || report.extract}
          </div>

          {/* Company domains or other metadata */}
          {companyData?.domains && companyData.domains.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
              <h4 className="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 mb-2">Web Presence</h4>
              <div className="flex flex-wrap gap-2">
                {companyData.domains.slice(0, 3).map((domain, i) => (
                  <span key={i} className="text-xs text-gray-600 dark:text-gray-400 truncate max-w-[130px]">
                    {domain.url && new URL(domain.url).hostname.replace('www.', '')}
                  </span>
                ))}
                {companyData.domains.length > 3 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    +{companyData.domains.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}

          <div className="mt-auto pt-4 flex justify-end">
            <span className="text-sm font-medium text-brand opacity-0 group-hover:opacity-100 transition-opacity duration-300
                            flex items-center">
              View full profile
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300"
                  viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </span>
          </div>
        </div>
      </article>
    </Link>
  )
}

export default CompanyReportCard
