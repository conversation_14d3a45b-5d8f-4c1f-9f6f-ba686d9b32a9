# CMS Components

This directory contains standardized components for use throughout the CMS application.

## BlockWrapper

The `BlockWrapper` component provides a consistent wrapper for all content blocks with standardized spacing, background handling, and inset styling.

### Usage

```tsx
import { BlockWrapper } from '@/components/BlockWrapper';

<BlockWrapper
  inset={true}
  background={true}
  backgroundMedia={mediaObject}
  darkMode={true}
  subtle={true}
>
  {/* Your content here */}
</BlockWrapper>
```

### Props

- `inset`: Adds a subtle gradient inset effect
- `background`: Enables the brand gradient background
- `backgroundMedia`: Optional media object for background image
- `darkMode`: Enables dark mode styling
- `subtle`: Makes the background effect more subtle
- `className`: Additional CSS classes
- `containerClassName`: Additional CSS classes for the container
- `noPadding`: Disables default padding
- `id`: Optional ID for the section

## Section

The `Section` component provides consistent spacing for content sections.

### Usage

```tsx
import { Section } from '@/components/Section';

<Section spacing="medium" container={true}>
  {/* Your content here */}
</Section>
```

### Props

- `spacing`: Controls vertical spacing (none, small, medium, large)
- `container`: Enables the container class for horizontal spacing
- `className`: Additional CSS classes
- `id`: Optional ID for the section

## CardGeneric

The `CardGeneric` component provides a consistent card styling for UI elements.

### Usage

```tsx
import { CardGeneric } from '@/components/ui/card-generic';

<CardGeneric variant="default" hover={true}>
  {/* Your content here */}
</CardGeneric>
```

### Props

- `variant`: Card style variant (default, outline, glass, highlight)
- `hover`: Enables hover effects
- `className`: Additional CSS classes
- `onClick`: Optional click handler

## ButtonStyled

The `ButtonStyled` component extends the base Button component with additional styling options and link functionality.

### Usage

```tsx
import { ButtonStyled } from '@/components/ui/button-styled';

<ButtonStyled
  variant="primary"
  size="md"
  href="/some-page"
  external={false}
>
  Button Text
</ButtonStyled>
```

### Props

- `variant`: Button style variant (primary, secondary, outline, ghost, link)
- `size`: Button size (sm, md, lg)
- `href`: Optional URL for link buttons
- `external`: Whether the link should open in a new tab
- `className`: Additional CSS classes
- `icon`: Optional icon component
- `iconPosition`: Icon position (left, right)

## Image

The `Image` component provides a standardized wrapper around Next.js's Image component with consistent styling options.

### Usage

```tsx
import { Image } from '@/components/ui/image';

<Image
  src="/path/to/image.jpg"
  alt="Image description"
  width={500}
  height={300}
  rounded="md"
  aspectRatio="16:9"
  objectFit="cover"
/>
```

### Props

- `src`: Image source path
- `alt`: Image alt text (required)
- `width`: Image width
- `height`: Image height
- `rounded`: Rounded corners (true, 'sm', 'md', 'lg', 'full')
- `aspectRatio`: Aspect ratio ('1:1', '4:3', '16:9', '21:9')
- `objectFit`: Object fit ('contain', 'cover', 'fill')
- `overlay`: Whether to show an overlay
- `overlayColor`: Overlay color
- `overlayOpacity`: Overlay opacity

## Alert

The `Alert` component provides a standardized alert component for consistent messaging.

### Usage

```tsx
import { Alert } from '@/components/ui/alert';

<Alert
  variant="info"
  title="Information"
  dismissible={true}
  onDismiss={() => console.log('Dismissed')}
>
  This is an informational alert.
</Alert>
```

### Props

- `variant`: Alert style variant ('info', 'success', 'warning', 'error')
- `title`: Optional alert title
- `dismissible`: Whether the alert can be dismissed
- `onDismiss`: Callback when the alert is dismissed
- `icon`: Optional custom icon
- `className`: Additional CSS classes

## BadgeStyled

The `BadgeStyled` component provides a standardized badge component for consistent labeling.

### Usage

```tsx
import { BadgeStyled } from '@/components/ui/badge-styled';

<BadgeStyled variant="success" size="md" rounded={true}>
  Completed
</BadgeStyled>
```

### Props

- `variant`: Badge style variant ('default', 'secondary', 'outline', 'success', 'warning', 'error', 'info')
- `size`: Badge size ('sm', 'md', 'lg')
- `rounded`: Whether to use rounded corners
- `icon`: Optional icon component
- `className`: Additional CSS classes

## TabsStyled

The `TabsStyled` component provides a standardized tabs component for consistent navigation.

### Usage

```tsx
import { TabsStyled } from '@/components/ui/tabs-styled';

<TabsStyled
  tabs={[
    {
      id: 'tab1',
      label: 'Tab 1',
      content: <div>Tab 1 content</div>,
    },
    {
      id: 'tab2',
      label: 'Tab 2',
      content: <div>Tab 2 content</div>,
    },
  ]}
  variant="pills"
  orientation="horizontal"
/>
```

### Props

- `tabs`: Array of tab objects with id, label, content, icon, and disabled properties
- `defaultValue`: Default selected tab ID
- `variant`: Tab style variant ('default', 'pills', 'underline', 'minimal')
- `orientation`: Tab orientation ('horizontal', 'vertical')
- `onChange`: Callback when the selected tab changes
- `className`: Additional CSS classes
- `tabsListClassName`: Additional CSS classes for the tabs list
- `tabsContentClassName`: Additional CSS classes for the tabs content

## TooltipStyled

The `TooltipStyled` component provides a standardized tooltip component for consistent help text.

### Usage

```tsx
import { TooltipStyled } from '@/components/ui/tooltip-styled';

<TooltipStyled
  content="This is a tooltip"
  side="top"
  align="center"
  variant="info"
>
  <button>Hover me</button>
</TooltipStyled>
```

### Props

- `content`: Tooltip content
- `side`: Tooltip position ('top', 'right', 'bottom', 'left')
- `align`: Tooltip alignment ('start', 'center', 'end')
- `variant`: Tooltip style variant ('default', 'info', 'success', 'warning', 'error')
- `delayDuration`: Delay before showing the tooltip
- `skipDelayDuration`: Delay before hiding the tooltip
- `className`: Additional CSS classes
- `contentClassName`: Additional CSS classes for the tooltip content

## Breadcrumb

The `Breadcrumb` component provides a standardized breadcrumb component for consistent navigation.

### Usage

```tsx
import { Breadcrumb } from '@/components/ui/breadcrumb';

<Breadcrumb
  items={[
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: 'Product Name' },
  ]}
  homeIcon={true}
/>
```

### Props

- `items`: Array of breadcrumb items with label, href, and icon properties
- `separator`: Custom separator component
- `homeIcon`: Whether to show the home icon for the first item
- `className`: Additional CSS classes

## PageHeader

The `PageHeader` component provides a standardized page header component for consistent page headers.

### Usage

```tsx
import { PageHeader } from '@/components/ui/page-header';

<PageHeader
  title="Page Title"
  description="Page description"
  breadcrumbs={[
    { label: 'Home', href: '/' },
    { label: 'Section', href: '/section' },
    { label: 'Page Title' },
  ]}
  actions={<button>Action</button>}
/>
```

### Props

- `title`: Page title
- `description`: Optional page description
- `breadcrumbs`: Optional breadcrumb items
- `actions`: Optional action components
- `className`: Additional CSS classes
- `titleClassName`: Additional CSS classes for the title
- `descriptionClassName`: Additional CSS classes for the description
- `actionsClassName`: Additional CSS classes for the actions
- `breadcrumbsClassName`: Additional CSS classes for the breadcrumbs

## Best Practices

1. Use these standardized components instead of creating custom implementations
2. Maintain consistent spacing and styling across the application
3. Follow the style guidelines in the STYLE_GUIDELINES.md file
4. Use the theme variables from theme-variables.ts for consistent colors and spacing
