'use client'
import { cn } from '@/utilities/ui'
import useClickableCard from '@/utilities/useClickableCard'
import Link from 'next/link'
import React, { Fragment } from 'react'

import type { Post } from '@/payload-types'

// Import Firefox-specific fixes
import './firefox-fix.css'
import NextImage from 'next/image'

export type CardPostData = Pick<Post, 'slug' | 'categories' | 'meta' | 'title' | 'pinned'>

export const GlassCard: React.FC<{
  alignItems?: 'center'
  className?: string
  doc?: CardPostData
  relationTo?: 'posts'
  showCategories?: boolean
  title?: string
}> = (props) => {
  const { card, link } = useClickableCard({})
  const { className, doc, relationTo, showCategories, title: titleFromProps } = props

  const { slug, categories, meta, title, pinned } = doc || {}
  const { description, image: metaImage } = meta || {}

  const hasCategories = categories && Array.isArray(categories) && categories.length > 0
  const titleToUse = titleFromProps || title
  const sanitizedDescription = description?.replace(/\s/g, ' ') // replace non-breaking space with white space
  const href = `/${relationTo}/${slug}`

  return (
    <article
      className={cn(
        'border min-h-[36rem] border-border rounded-3xl bg-card hover:cursor-pointer transition-all duration-300 drop-shadow-xl hover:drop-shadow-2xl hover:-translate-y-1 hover:border-border/80',
        className,
      )}
      ref={card.ref}
    >
      <div className="relative w-full h-full text-center overflow-hidden group">
        {!metaImage && <div className="p-4 opacity-75">Please supply an Image for this Post</div>}
        {metaImage && typeof metaImage !== 'string' && (
          <div className="overflow-hidden w-full h-full relative rounded-3xl">
            {/* Subtle lighting overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-40 mix-blend-overlay pointer-events-none z-10 transition-opacity duration-300 group-hover:opacity-60"></div>
            {/* Additional lighting effect on hover */}
            <div className="absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10" style={{ background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 70%)' }}></div>
            <NextImage src={(metaImage as any).sizes.medium.url} alt={titleToUse||''} fill className="absolute object-cover inset-0 " />

            {/* Pinned indicator */}
            {pinned && (
              <div className="absolute top-4 right-4 z-20">
                <div className="glass-effect-lit px-3 py-1 rounded-full text-xs font-medium text-white drop-shadow-md">
                  Pinned
                </div>
              </div>
            )}

            {/* Glass morphism overlay for text - fixed at 50% height */}
            <div className="absolute inset-0 flex flex-col justify-end">
              <div className="glass-effect-lit p-5 h-[50%] overflow-hidden rounded-t-3xl transition-all duration-500 group-hover:backdrop-blur-lg">
                <div className="h-full overflow-hidden">
                <div className="flex justify-between items-start">
                  {showCategories && hasCategories && (
                    <div className="uppercase text-sm mb-2 overflow-hidden">
                      {showCategories && hasCategories && (
                        <div className="text-white">
                          {categories?.map((category, index) => {
                            if (typeof category === 'object') {
                              const { title: titleFromCategory } = category

                              const categoryTitle = titleFromCategory || 'Untitled category'

                              const isLast = index === categories.length - 1

                              return (
                                <Fragment key={index}>
                                  {categoryTitle}
                                  {!isLast && <Fragment>, &nbsp;</Fragment>}
                                </Fragment>
                              )
                            }

                            return null
                          })}
                        </div>
                      )}
                    </div>
                  )}
                </div>
                {titleToUse && (
                  <div className="prose overflow-hidden">
                    <h4 className="text-white drop-shadow-md text-shadow-sm">
                      <Link className="not-prose text-white hover:text-white/90 transition-colors duration-300" href={href} ref={link.ref}>
                        {titleToUse}
                      </Link>
                    </h4>
                  </div>
                )}
                {description && <div className="mt-2 text-white/90 text-sm drop-shadow-sm overflow-ellipsis">{description && <p>{sanitizedDescription}</p>}</div>}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </article>
  )
}
