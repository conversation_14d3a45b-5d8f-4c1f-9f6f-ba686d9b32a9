@-moz-document url-prefix() {
  .card-overlay,
  .glass-effect, .glass-effect-lit,
  .glass-effect-subtle, .glass-effect-subtle-lit,
  .glass-effect-strong, .glass-effect-strong-lit,
  .glass-effect-brand, .glass-effect-brand-lit,
  .glass-effect-brand-strong, .glass-effect-brand-strong-lit,
  .glass-effect-brand-alt-strong, .glass-effect-brand-alt-strong-lit,
  .glass-effect-brand-compliment, .glass-effect-brand-compliment-lit,
  .glass-effect-brand-compliment-strong, .glass-effect-brand-compliment-strong-lit {
    background: rgba(35, 35, 35, 0.85) !important;
    backdrop-filter: none !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25) !important;
  }

  /* Adjust brand-specific glass effects in Firefox */
  .glass-effect-brand-strong, .glass-effect-brand-strong-lit {
    background: linear-gradient(197deg, rgba(46, 125, 50, 0.85), rgba(39, 107, 43, 0.85), rgba(46, 125, 50, 0.85)) !important;
  }

  .glass-effect-brand-alt-strong, .glass-effect-brand-alt-strong-lit {
    background: linear-gradient(197deg, rgba(192, 202, 51, 0.85), rgba(202, 211, 57, 0.85), rgba(195, 204, 53, 0.85)) !important;
  }

  .glass-effect-brand-compliment-strong, .glass-effect-brand-compliment-strong-lit {
    background: linear-gradient(197deg, rgba(204, 51, 51, 0.85), rgba(214, 59, 59, 0.85), rgba(204, 51, 51, 0.85)) !important;
  }

  .glass-effect-lit::before,
  .glass-effect-subtle-lit::before,
  .glass-effect-strong-lit::before,
  .glass-effect-brand-lit::before,
  .glass-effect-brand-strong-lit::before,
  .glass-effect-brand-alt-strong-lit::before,
  .glass-effect-brand-compliment-lit::before,
  .glass-effect-brand-compliment-strong-lit::before,
  .glass-effect::before,
  .glass-effect-subtle::before,
  .glass-effect-strong::before,
  .glass-effect-brand::before,
  .glass-effect-brand-strong::before,
  .glass-effect-brand-alt-strong::before,
  .glass-effect-brand-compliment::before,
  .glass-effect-brand-compliment-strong::before {
    display: none !important;
  }
}
