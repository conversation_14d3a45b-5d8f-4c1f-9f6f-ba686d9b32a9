import React from 'react'
import { cn } from '@utils/lib/utils'

interface SectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  spacing?: 'none' | 'small' | 'medium' | 'large';
  container?: boolean;
}

/**
 * A standardized section component for consistent spacing
 */
export const Section: React.FC<SectionProps> = ({
  children,
  className,
  id,
  spacing = 'medium',
  container = true,
}) => {
  const spacingClasses = {
    none: '',
    small: 'py-8 md:py-12',
    medium: 'py-12 md:py-16 lg:py-20',
    large: 'py-16 md:py-24 lg:py-32',
  };

  return (
    <section
      id={id}
      className={cn(
        spacingClasses[spacing],
        container && 'container',
        className
      )}
    >
      {children}
    </section>
  );
};
