@import '~@payloadcms/ui/scss';

// Dashboard styles
.dashboard .before-dashboard {
  padding: base(2);
  font-family: var(--font-geist-sans, system-ui, sans-serif);
  max-width: 1400px;
  margin: 0 auto;

  // Define brand color variables
  --brand-color: var(--theme-primary, #54926e);
  --brand-color-light: var(--theme-primary-light, #6dab87);
  --brand-color-dark: var(--theme-primary-dark, #417155);
  --brand-color-shadow: var(--theme-primary-shadow, rgba(84, 146, 110, 0.15));

  // Hero section
  .dashboard-hero {
    display: flex;
    align-items: stretch;
    background: linear-gradient(135deg, rgba(84, 146, 110, 0.08) 0%, rgba(84, 146, 110, 0.03) 100%);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: base(3);
    padding: base(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(84, 146, 110, 0.1);

    &__content {
      flex: 1;
      padding: base(1.5);

      .eko-admin-logo {
        display: flex;
        align-items: center;
        margin-bottom: base(1);

        .mr-2 {
          margin-right: base(1);
        }

        .ml-2 {
          margin-left: base(1);
        }
      }

      h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: base(1);
        color: var(--theme-elevation-800);
        line-height: 1.2;
      }

      p {
        font-size: 1rem;
        line-height: 1.65;
        color: var(--theme-elevation-600);
        max-width: 80%;
        margin-bottom: base(1);
      }

      .dashboard-actions {
        display: flex;
        gap: base(2);

        .btn-dashboard {
          display: inline-flex;
          align-items: center;
          padding: base(2) base(3);
          font-size: 1rem;
          font-weight: 500;
          border-radius: 6px;
          text-decoration: none;
          transition: all 0.2s ease-out;
          border: 1px solid transparent;

          svg {
            margin-right: base(1);
          }

          &.primary {
            background-color: var(--brand-color);
            color: white;

            &:hover {
              background-color: var(--brand-color-dark);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(84, 146, 110, 0.2);
            }
          }

          &.secondary {
            background-color: transparent;
            border-color: var(--brand-color);
            color: var(--brand-color);

            &:hover {
              background-color: rgba(84, 146, 110, 0.05);
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    &__graphic {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: base(4);

      .dashboard-graphic {
        max-width: 100%;
        height: auto;
      }
    }
  }

  // Dashboard grid
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: base(3);
    margin-bottom: base(3);
  }

  // Dashboard cards
  .dashboard-card {
    background-color: var(--theme-elevation-50);
    border-radius: 12px;
    border: 1px solid var(--theme-elevation-100);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin:16px;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    .card-header {
      padding: base(1.5) base(2);
      border-bottom: 1px solid var(--theme-elevation-100);

      h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--theme-elevation-800);
        display: flex;
        align-items: center;

        svg {
          margin-right: base(1.5);
          color: var(--brand-color);
        }
      }
    }

    .card-body {
      padding: base(1.5);
      flex: 1;
    }

    .card-footer {
      padding: base(2) base(3);
      border-top: 1px solid var(--theme-elevation-100);
      text-align: right;

      .card-link {
        color: var(--brand-color);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.2s ease;

        &:hover {
          color: var(--brand-color-dark);
          text-decoration: underline;
        }
      }
    }

    // Analytics card (full width)
    &.analytics-card {
      grid-column: 1 / -1;

      .card-body {
        padding: base(1);
      }
    }
  }

  // Quick stats
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: base(2);

    .stat-item {
      text-align: center;
      padding: base(1.5);
      background-color: var(--theme-elevation-0);
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: base(0.5);
        color: var(--brand-color);
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--theme-elevation-600);
        font-weight: 500;
      }
    }
  }

  // Activity list
  .activity-list {
    list-style: none;
    padding: 0;
    margin: 0;

    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: base(1) 0;
      border-bottom: 1px solid var(--theme-elevation-100);

      &:last-child {
        border-bottom: none;
      }

      .activity-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border-radius: 6px;
        background-color: rgba(84, 146, 110, 0.1);
        margin-right: base(2);

        svg {
          color: var(--brand-color);
        }

        &.edit {
          background-color: rgba(84, 146, 110, 0.1);
          svg { color: var(--brand-color); }
        }

        &.add {
          background-color: rgba(75, 107, 251, 0.1);
          svg { color: #4b6bfb; }
        }

        &.upload {
          background-color: rgba(237, 137, 54, 0.1);
          svg { color: #ed8936; }
        }
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-weight: 500;
          font-size: 0.9375rem;
          margin-bottom: base(0.25);
          color: var(--theme-elevation-800);
        }

        .activity-meta {
          font-size: 0.75rem;
          color: var(--theme-elevation-500);
        }
      }
    }
  }

  // Quick links grid
  .quick-links-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: base(3);

    .quick-link-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: base(1);
      background-color: var(--theme-elevation-0);
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid var(--theme-elevation-100);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
        background-color: var(--theme-elevation-50);

        .quick-link-icon {
          transform: scale(1.1);
        }
      }

      .quick-link-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: base(0.5);
        background-color: rgba(84, 146, 110, 0.1);
        border-radius: 12px;
        transition: transform 0.2s ease;

        svg {
          color: var(--brand-color);
        }
      }

      .quick-link-label {
        font-weight: 500;
        font-size: 0.875rem;
        color: var(--theme-elevation-700);
      }
    }
  }
}

// Dark mode adjustments
.theme--dark {
  .dashboard .before-dashboard {
    .dashboard-hero {
      background: linear-gradient(135deg, rgba(84, 146, 110, 0.12) 0%, rgba(84, 146, 110, 0.04) 100%);
      border-color: rgba(255, 255, 255, 0.05);

      &__content {
        h1 {
          color: var(--theme-elevation-300);
        }

        p {
          color: var(--theme-elevation-400);
        }
      }
    }

    .dashboard-card {
      background-color: var(--theme-elevation-100);
      border-color: var(--theme-elevation-200);

      .card-header {
        border-color: var(--theme-elevation-200);

        h2 {
          color: var(--theme-elevation-300);
        }
      }

      .card-footer {
        border-color: var(--theme-elevation-200);
      }
    }

    .stats-grid {
      .stat-item {
        background-color: var(--theme-elevation-150);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .stat-label {
          color: var(--theme-elevation-400);
        }
      }
    }

    .activity-list {
      .activity-item {
        border-color: var(--theme-elevation-200);

        .activity-content {
          .activity-title {
            color: var(--theme-elevation-300);
          }

          .activity-meta {
            color: var(--theme-elevation-500);
          }
        }
      }
    }

    .quick-links-grid {
      .quick-link-item {
        background-color: var(--theme-elevation-150);
        border-color: var(--theme-elevation-200);

        &:hover {
          background-color: var(--theme-elevation-200);
        }

        .quick-link-label {
          color: var(--theme-elevation-300);
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .dashboard .before-dashboard {
    .dashboard-hero {
      flex-direction: column;

      &__content {
        padding: base(4);

        p {
          max-width: 100%;
        }
      }

      &__graphic {
        padding: 0 base(4) base(4);

        .dashboard-graphic {
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard .before-dashboard {
    padding: base(2);

    .dashboard-grid {
      grid-template-columns: 1fr;
    }

    .stats-grid,
    .quick-links-grid {
      grid-template-columns: 1fr;
    }
  }
}
