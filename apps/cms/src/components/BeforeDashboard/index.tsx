import React from 'react'
import './index.scss'
import { EkoLogoText, EkoSymbolBrand } from '@utils/images'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { formatDistanceToNow } from 'date-fns'
import { User } from '@/payload-types'

const BeforeDashboard: React.FC = async () => {
  // Fetch real data from Payload CMS
  const payload = await getPayload({ config: configPromise })

  // Get collection counts
  const pagesCount = await payload.count({
    collection: 'pages',
  })

  const postsCount = await payload.count({
    collection: 'posts',
  })

  const reportsCount = await payload.count({
    collection: 'company-reports',
  })

  const mediaCount = await payload.count({
    collection: 'media',
  })

  // Get recent activities (latest updated documents across collections)
  const recentPosts = await payload.find({
    collection: 'posts',
    sort: '-updatedAt',
    limit: 2,
    depth: 1,
  })

  const recentPages = await payload.find({
    collection: 'pages',
    sort: '-updatedAt',
    limit: 2,
    depth: 1,
  })

  const recentReports = await payload.find({
    collection: 'company-reports',
    sort: '-updatedAt',
    limit: 2,
    depth: 1,
  })

  const recentMedia = await payload.find({
    collection: 'media',
    sort: '-updatedAt',
    limit: 2,
    depth: 1,
  })

  // Combine and sort all recent activities
  const allRecentActivities = [
    ...recentPosts.docs.map(post => ({
      type: 'post',
      title: post.title,
      updatedAt: post.updatedAt,
      user: post.authors && post.authors[0]  ? (post.authors[0] as User).name || 'Admin' :'Unknown',
    })),
    ...recentPages.docs.map(page => ({
      type: 'page',
      title: page.title,
      updatedAt: page.updatedAt,
      user: 'Someone',
    })),
    ...recentReports.docs.map(report => ({
      type: 'report',
      title: report.title,
      updatedAt: report.updatedAt,
      user: 'Someone',
    })),
    ...recentMedia.docs.map(media => ({
      type: 'media',
      title: media.filename,
      updatedAt: media.updatedAt,
      user: 'Someone',
    }))
  ].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
  .slice(0, 4) // Get the 4 most recent activities

  return (
    <div className="before-dashboard">
      <div className="dashboard-hero">
        <div className="dashboard-hero__content">
          <div className="eko-admin-logo">
            <EkoSymbolBrand height={30} className="mr-2" />
            <EkoLogoText
              height={24}
              className="ml-2"
              ekoColor="#1f1f1f"
              intelligenceColor="#505050"
            />
          </div>
          <h1>Welcome to your Admin Dashboard</h1>
          <p className="welcome-text">
            Manage your website content, analyze data, and monitor your platform&apos;s performance.
            Use the quick actions below to get started or navigate using the sidebar menu.
          </p>
        </div>
      </div>

      <div className="dashboard-card">
        <div className="card-header">
          <h2>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            Quick Actions
          </h2>
        </div>
        <div className="card-body">
          <div className="quick-links-grid">
            <Link href="/admin/collections/pages/create" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="12" y1="18" x2="12" y2="12"></line>
                  <line x1="9" y1="15" x2="15" y2="15"></line>
                </svg>
              </div>
              <div className="quick-link-label">Create New Page</div>
            </Link>
            <Link href="/admin/collections/pages/1" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              </div>
              <div className="quick-link-label">Edit Home Page</div>
            </Link>

            <Link href="/admin/collections/posts/create" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              </div>
              <div className="quick-link-label">Write Blog Post</div>
            </Link>

            <Link href="/admin/collections/media/create" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
              </div>
              <div className="quick-link-label">Upload Media</div>
            </Link>

            <Link href="/admin/collections/company-reports/create" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
              <div className="quick-link-label">New Report</div>
            </Link>

            <Link href="/" className="quick-link-item">
              <div className="quick-link-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
              </div>
              <div className="quick-link-label">View Website</div>
            </Link>
          </div>
        </div>
      </div>

      <div className="dashboard-grid">
        <div className="dashboard-card">
          <div className="card-header">
            <h2>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              Site Statistics
            </h2>
          </div>
          <div className="card-body">
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-value">{pagesCount.totalDocs}</div>
                <div className="stat-label">Pages</div>
              </div>
              <div className="stat-item">
                <div className="stat-value">{postsCount.totalDocs}</div>
                <div className="stat-label">Blog Posts</div>
              </div>
              <div className="stat-item">
                <div className="stat-value">{reportsCount.totalDocs}</div>
                <div className="stat-label">Reports</div>
              </div>
              <div className="stat-item">
                <div className="stat-value">{mediaCount.totalDocs}</div>
                <div className="stat-label">Media Files</div>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-card">
          <div className="card-header">
            <h2>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              Recent Activity
            </h2>
          </div>
          <div className="card-body">
            <div className="activity-list">
              {allRecentActivities.map((activity, index) => {
                // Determine icon based on activity type
                let icon;
                let actionText;

                switch(activity.type) {
                  case 'page':
                    icon = (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                      </svg>
                    );
                    actionText = 'Page updated';
                    break;
                  case 'post':
                    icon = (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                      </svg>
                    );
                    actionText = 'Blog post updated';
                    break;
                  case 'report':
                    icon = (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                    );
                    actionText = 'Report updated';
                    break;
                  case 'media':
                    icon = (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21 15 16 10 5 21"></polyline>
                      </svg>
                    );
                    actionText = 'Media updated';
                    break;
                  default:
                    icon = (
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                    );
                    actionText = 'Item updated';
                }

                return (
                  <div className="activity-item" key={index}>
                    <div className={`activity-icon ${activity.type === 'media' ? 'add' : 'edit'}`}>
                      {icon}
                    </div>
                    <div className="activity-content">
                      <div className="activity-title">{actionText}: {activity.title}</div>
                      <div className="activity-meta">
                        {formatDistanceToNow(new Date(activity.updatedAt), { addSuffix: true })} by {activity.user}
                      </div>
                    </div>
                  </div>
                );
              })}

              {allRecentActivities.length === 0 && (
                <div className="activity-item">
                  <div className="activity-content">
                    <div className="activity-title">No recent activity</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="dashboard-card analytics-card">
        <div className="card-header">
          <h2>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path>
              <path d="M22 12A10 10 0 0 0 12 2v10z"></path>
            </svg>
            Analytics Dashboard
          </h2>
        </div>
        <div className="card-body">
          <iframe
            title="Analytics Dashboard"
            style={{width:"100%", height:"800px", borderRadius: "0.5rem", border: "1px solid var(--theme-elevation-100)"}}
            src="https://plausible.io/share/ekointel.com?auth=J-6l7BzYp7no29k4lYx6M&embed=true&theme=auto"
            scrolling="yes"
            frameBorder="0"
            loading="lazy"
          ></iframe>
        </div>
      </div>
    </div>
  )
}

export default BeforeDashboard
