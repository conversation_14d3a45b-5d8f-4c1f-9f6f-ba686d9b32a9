import React from 'react'
// Add error handling for the imports
let EkoLogoText: any;
let EkoSymbolBrand: any;

try {
  const images = require('@utils/images');
  EkoLogoText = images.EkoLogoText;
  EkoSymbolBrand = images.EkoSymbolBrand;
} catch (error) {
  console.error('Error importing logo components:', error);
  // Fallback components
  EkoLogoText = (props: any) => <div className={props.className}>Eko Intelligence</div>;
  EkoSymbolBrand = (props: any) => <div className={props.className} style={{ width: props.height*2, height: props.height }}>Eko</div>;
}

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
  dark?: boolean
  height?: number
}

export const Logo = ({
                       loading: loadingFromProps,
                       priority: priorityFromProps,
                       className,
                       dark,
                       height = 28,
                     }: Props) => {

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  return (
    <div className="flex items-center justify-center">
      <EkoSymbolBrand height={height * 0.9} className="" />
      {dark ? (
        // Dark mode explicitly specified - use light colored text
        <EkoLogoText
          height={height}
          className="ml-4 inline-block"
          ekoColor="var(--neutral-50)"
          intelligenceColor="var(--neutral-200)"
        />
      ) : (
        // Auto-switching based on dark mode class
        <>
          <EkoLogoText
            height={height}
            className="ml-4 inline-block dark:hidden"
            ekoColor="var(--neutral-900)"
            intelligenceColor="var(--neutral-700)50"
          />
          <EkoLogoText
            height={height}
            className="ml-4 hidden dark:inline-block"
            ekoColor="#f0f0f0"
            intelligenceColor="#d0d0d0"
          />
        </>
      )}
    </div>
  )
}

export const LogoForPayload = () => {
  try {
    return <div className="flex flex-col items-center justify-center text-center">
      <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
        <div style={{ display: 'block', textAlign: 'center' }}>
          <EkoSymbolBrand height={96} className="inline-block"/>
        </div>
      </div>

      <div className="flex justify-center w-full mt-8">
        <EkoLogoText
          height={64}
          className="mx-auto"
          ekoColor="#666666"
          intelligenceColor="#444444"
        />
      </div>
    </div>
  } catch (error) {
    console.error('Error rendering LogoForPayload:', error);
    // Fallback simple text logo
    return <div className="text-center p-4">
      <div style={{ fontSize: '2rem', fontWeight: 'bold' }}>Eko Intelligence</div>
    </div>
  }
}
