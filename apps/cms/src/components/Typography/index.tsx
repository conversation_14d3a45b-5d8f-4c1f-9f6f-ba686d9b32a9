import React from 'react'
import { cn } from '@utils/lib/utils'

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'lead' | 'small' | 'muted';
  as?: React.ElementType;
}

/**
 * A standardized typography component for consistent text styling
 */
export const Typography: React.FC<TypographyProps> = ({
  children,
  className,
  variant = 'p',
  as,
}) => {
  // Determine the component to render based on variant and as prop
  let ComponentTag: React.ElementType = 'p';

  if (as) {
    ComponentTag = as;
  } else if (variant.startsWith('h')) {
    ComponentTag = variant as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  } else if (variant === 'p') {
    ComponentTag = 'p';
  }

  const variantClasses = {
    h1: 'heading-1',
    h2: 'heading-2',
    h3: 'heading-3',
    h4: 'heading-4',
    h5: 'heading-5',
    h6: 'heading-6',
    p: 'text-body',
    lead: 'text-lead',
    small: 'text-small',
    muted: 'text-muted',
  };

  return (
    <ComponentTag className={cn(variantClasses[variant], className)}>
      {children}
    </ComponentTag>
  );
};
