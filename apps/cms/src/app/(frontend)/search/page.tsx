import type { <PERSON>ada<PERSON> } from 'next/types'

import { CollectionArchive } from '@/components/CollectionArchive'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import { Search } from '@/search/Component'
import PageClient from './page.client'
import { CardPostData } from '@/components/Card'
import { CompanyCollectionArchive } from '@/components/CompanyCollectionArchive'
import { FormBlock } from '@/blocks/Form/Component'

type Args = {
  searchParams: Promise<{
    q: string
  }>
}
export default async function Page({ searchParams: searchParamsPromise }: Args) {
  const { q: query } = await searchParamsPromise
  const payload = await getPayload({ config: configPromise })
  // Fetch the form with ID 2
  const formResult = await payload.findByID({
    collection: 'forms',
    id: '2',
    depth: 2, // Ensure we get all nested fields
  })

  // Ensure the form has the required properties for FormBlock
  const form = {
    ...formResult,
    confirmationType: formResult.confirmationType || 'message', // Default to message if undefined
  }


  const posts = await payload.find({
    collection: 'search',
    depth: 1,
    limit: 12,
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
      doc:true,
    },
    // pagination: false reduces overhead if you don't need totalDocs
    pagination: false,
    ...(query
      ? {
          where: {
            or: [
              {
                title: {
                  like: query,
                },
              },
              {
                'meta.description': {
                  like: query,
                },
              },
              {
                'meta.title': {
                  like: query,
                },
              },
              {
                slug: {
                  like: query,
                },
              },
            ],
            "doc.relationTo": {
              equals: 'posts',
            },
          },
        }
      : {}),
  })

  const companies = await payload.find({
    collection: 'search',
    depth: 1,
    limit: 12,
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
      doc:true,
    },
    // pagination: false reduces overhead if you don't need totalDocs
    pagination: false,
    ...(query
      ? {
        where: {
          or: [
            {
              title: {
                like: query,
              },
            },
            {
              'meta.description': {
                like: query,
              },
            },
            {
              'meta.title': {
                like: query,
              },
            },
            {
              slug: {
                like: query,
              },
            },
          ],
          "doc.relationTo": {
            equals: 'company-reports',
          },
        },
      }
      : {}),
  })

  return (
    <div className="pt-24 pb-24">
      <PageClient />
      <div className="container mb-16">
        <div className="prose dark:prose-invert max-w-none text-center">
          <h1 className="mb-8 lg:mb-16">Search</h1>

          <div className="max-w-[50rem] mx-auto">
            <Search />
          </div>
        </div>
      </div>

      {query && companies.totalDocs > 0 && (
        <CompanyCollectionArchive companyReports={companies.docs as CardPostData[]} />
      ) }
      {query && posts.totalDocs > 0 && (
        <CollectionArchive posts={posts.docs as CardPostData[]} />
      )}
      {query && posts.totalDocs === 0 && companies.totalDocs === 0 && (
        <div className="container mb-16">
          <div className="prose dark:prose-invert max-w-none">
            {/* Display the form with ID 2 */}
            <div className="container mt-16">
              <h3>Sorry we couldn&apos;t find that. Would you like to suggest a company for us to profile?</h3>
              <FormBlock
                form={form as any}
                enableIntro={true}
                inset={true}
                background={false}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export function generateMetadata(): Metadata {
  return {
    title: `ekoIntelligence Search`,
  }
}
