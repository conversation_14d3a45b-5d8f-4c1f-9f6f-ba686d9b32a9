@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 5%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 5%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 5%;
    --primary: 145 27% 45%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 11%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 47%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 91%;
    --input: 0 0% 91%;
    --ring: 145 27% 45%;
    --radius: 0.5rem;
    --brand-primary: 145 27% 45%;
    --brand-primary-dark: 145 27% 30%;
    --brand-contrast: 4 27% 51%;
    --success: 142 71% 45%;
    --warning: 38 92% 50%;
    --error: 0 84% 60%;
  }
  [data-theme='dark'] {
    --background: 0 0% 5%;
    --foreground: 0 0% 95%;
    --card: 0 0% 5%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 95%;
    --primary: 145 27% 30%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 17%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 17%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 17%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 17%;
    --input: 0 0% 17%;
    --ring: 145 27% 30%;
    --brand-primary: 145 27% 30%;
    --brand-primary-dark: 145 27% 20%;
    --brand-contrast: 4 27% 51%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Noise texture */
.bg-noise {
  color: #050A18;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 200px;
  background-repeat: repeat;
}

/* Standard rounding for elements */
.standard-rounding {
  @apply rounded-standard;
}

/* Animations for the aurora background */
@keyframes aurora {
  0% {
    background-position: 50% 50%, 50% 50%;
    transform: translate3d(0, 0, 0);
  }
  50% {
    background-position: 200% 50%, 200% 50%;
    transform: translate3d(0, 0, 0);
  }
  100% {
    background-position: 350% 50%, 350% 50%;
    transform: translate3d(0, 0, 0);
  }
}

/* Prose overrides */
.prose {
  max-width: 65ch;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-bottom: 1.25em;
}


/* All utility classes have been migrated to the Tailwind configuration */

/******** Special H1 styling ******/

.prose h1:not(.not-eko-h1), h1:not(.not-eko-h1) {
  display: block;
  text-align: center;

  font-family: 'Roboto', 'Roboto Fallback', sans-serif;

  /* Spacing and text styling */
  padding-bottom: 1rem; /* py-4 */

  color: var(--neutral-900); /* text-neutral-dark */
  font-size: 1.5rem;
  letter-spacing: -0.02em;

  /* Dark mode */

  line-height: 1.3;
}

/* Medium breakpoint adjustment (md:text-6xl) */
@media (min-width: 768px) {
  .prose h1:not(.not-eko-h1), h1:not(.not-eko-h1) {
    font-size: 3rem;
  }
}

/* Light text for headings on Aurora backgrounds */
.aurora-section h1,
.aurora-bg h1,
.post-hero h1,
section[data-aurora="true"] h1,
div[data-aurora="true"] h1,
h1.light-heading {
  color: var(--neutral-50); /* text-neutral-dark in dark mode */
  text-shadow: 0 2px 4px var(--neutral-900, rgba(15, 23, 42, 0.3));
}

/* Dark mode heading styles */
[data-theme='dark'] .prose h1:not(.not-eko-h1),
[data-theme='dark'] h1:not(.not-eko-h1) {
  color: var(--neutral-50); /* dark:text-neutral-dark */
  text-shadow: none;
}

/****** End of Special H1 styling ******/
