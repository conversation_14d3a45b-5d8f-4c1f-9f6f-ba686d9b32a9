import type { Metadata } from 'next/types'

import { CollectionArchive } from '@/components/CollectionArchive'
import { PageRange } from '@/components/PageRange'
import { Pagination } from '@/components/Pagination'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import PageClient from '../../page.client'
import { notFound } from 'next/navigation'

export const dynamic = 'force-dynamic'
export const revalidate = 600

// Define the proper types for Next.js 15 params
type PageProps = {
  params: Promise<any> | any
  searchParams?: Promise<any> | any
}

export default async function Page(props: PageProps) {
  // Await both params and searchParams
  const { params, searchParams } = props

  // Resolve params
  const resolvedParams = params instanceof Promise ? await params : params
  const { pageNumber } = resolvedParams

  const payload = await getPayload({ config: configPromise })

  const sanitizedPageNumber = Number(pageNumber)

  if (!Number.isInteger(sanitizedPageNumber)) notFound()

  // Get the sort parameter from the URL - ensure searchParams is awaited
  const resolvedSearchParams = searchParams instanceof Promise ? await searchParams : searchParams || {}
  const sortParam = resolvedSearchParams.sort || 'newest'

  // Define sort configurations
  let sortConfig: string[] = ['-publishedAt']

  // Set sort configuration based on the parameter
  if (sortParam === 'oldest') {
    sortConfig = ['publishedAt']
  } else if (sortParam === 'title-asc') {
    sortConfig = ['title']
  } else if (sortParam === 'title-desc') {
    sortConfig = ['-title']
  }

  // Always add pinned as the first sort criteria
  const finalSort = ['-pinned', ...sortConfig]

  const posts = await payload.find({
    collection: 'posts',
    depth: 1,
    limit: 12,
    page: sanitizedPageNumber,
    overrideAccess: false,
    sort: finalSort,
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
      pinned: true,
      publishedAt: true,
    },
  })

  return (
    <div className="pt-24 pb-24">
      <div className="container mb-16">
        <div className="prose dark:prose-invert max-w-none">
          <h1>Insights</h1>
        </div>
      </div>

      <div className="container mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <PageRange
            collection="posts"
            currentPage={posts.page}
            limit={12}
            totalDocs={posts.totalDocs}
          />
          <PageClient />
        </div>
      </div>

      <CollectionArchive posts={posts.docs} />

      <div className="container">
        {posts?.page && posts?.totalPages > 1 && (
          <Pagination page={posts.page} totalPages={posts.totalPages} />
        )}
      </div>
    </div>
  )
}

export async function generateMetadata(props: PageProps): Promise<Metadata> {
  const { params } = props
  const resolvedParams = params instanceof Promise ? await params : params
  const { pageNumber } = resolvedParams

  return {
    title: `ekoIntelligence Insights - Page ${pageNumber || ''}`,
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const { totalDocs } = await payload.count({
    collection: 'posts',
    overrideAccess: false,
  })

  const totalPages = Math.ceil(totalDocs / 10)

  // Return params in the format expected by Next.js
  return Array.from({ length: totalPages }, (_, i) => ({
    params: { pageNumber: String(i + 1) }
  }))
}
