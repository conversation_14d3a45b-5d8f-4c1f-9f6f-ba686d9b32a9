'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'
import { SortDropdown } from '@/components/SortDropdown'

export const sortOptions = [
  { label: 'Newest First', value: 'newest' },
  { label: 'Oldest First', value: 'oldest' },
  { label: 'A-Z', value: 'title-asc' },
  { label: 'Z-A', value: 'title-desc' },
]

const PageClient: React.FC = () => {
  /* Force the header to be dark mode while we have an image behind it */
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    setHeaderTheme('light')
  }, [setHeaderTheme])

  return (
    <div>
      <SortDropdown options={sortOptions} defaultValue="newest" />
    </div>
  )
}

export default PageClient
