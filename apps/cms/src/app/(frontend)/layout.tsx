import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { <PERSON>er } from '@/Footer/Component'
import { Header } from '@/Header/Component'
import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'
import { draftMode } from 'next/headers'
import { Roboto } from 'next/font/google'
import './globals.css'
import { getServerSideURL } from '@/utilities/getURL'
import { Toaster } from '@ui/components/ui/toaster'
import PlausibleProvider from 'next-plausible'

const mainFont = Roboto({
  subsets: ['latin'],
  weight: ['200','300','400', '500', '600', '700'], // specify the weights you need
})


export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const { isEnabled } = await draftMode()


  return (
    <html className={mainFont.className} lang="en" suppressHydrationWarning>
    <head>
      <InitTheme />
      <link href="/favicon.ico" rel="icon" sizes="32x32" />
      <link href="/favicon.svg" rel="icon" type="image/svg+xml" />

    </head>
    <body>
    <Providers>
      <PlausibleProvider domain="ekointel.com" trackOutboundLinks={true} trackFileDownloads={true}
                         trackLocalhost={false}>


        <Header isPreviewEnabled={isEnabled} />
        {children}
        <Footer />
      </PlausibleProvider>
    </Providers>
    <Toaster />
    </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(getServerSideURL()),
  openGraph: mergeOpenGraph(),
  twitter: {
    card: 'summary_large_image',
    creator: '@payloadcms',
  },
}
