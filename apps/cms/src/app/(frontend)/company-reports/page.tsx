import type { <PERSON>ada<PERSON> } from 'next/types'
import { PageRange } from '@/components/PageRange'
import { Pagination } from '@/components/Pagination'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import PageClient from './page.client'
import { cn } from '@/utilities/ui'
import CompanyReportCard from '@/components/CompanyReportCard'
import { FormBlock } from '@/blocks/Form/Component'

export const dynamic = 'force-static'
export const revalidate = 600

export default async function Page() {
  const payload = await getPayload({ config: configPromise })

  const reports = await payload.find({
    collection: 'company-reports',
    depth: 4, // We need sufficient depth to get nested data
    limit: 12,
    overrideAccess: false,
  });



  // Fetch the form with ID 2
  const formResult = await payload.findByID({
    collection: 'forms',
    id: '2',
    depth: 2, // Ensure we get all nested fields
  })

  // Ensure the form has the required properties for FormBlock
  const form = {
    ...formResult,
    confirmationType: formResult.confirmationType || 'message', // Default to message if undefined
  }




  console.log(JSON.stringify(reports, null, 2));

  return (
    <div className="px-4 sm:px-0 pt-12 pb-24">
      <PageClient />
      <div className="container mb-16">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 dark:text-white mb-6">
            Company Profiles
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            The following are some example profiles, these are greatly simplified versions of the analysis we perform but provide a taste of what we do.
          </p>
          <div className="w-20 h-1 bg-brand mx-auto mb-12 rounded-full"></div>
        </div>
      </div>

      <div className="container mb-8">
        <PageRange
          collection="posts"
          currentPage={reports.page}
          limit={12}
          totalDocs={reports.totalDocs}
        />
      </div>

      <div className={cn('container')}>
        <div>
          <div className="grid grid-cols-4 sm:grid-cols-8 lg:grid-cols-12 gap-8 sm:gap-y-4 sm:gap-x-4 lg:gap-y-8 lg:gap-x-8 xl:gap-x-8">
            {reports.docs?.map((result, index) => {
              if (typeof result === 'object' && result !== null) {
                return (
                  <div className="col-span-4" key={index}>
                    <CompanyReportCard report={result} />
                  </div>
                )
              }

              return null
            })}
          </div>
        </div>
      </div>

      <div className="container">
        {reports.totalPages > 1 && reports.page && (
          <Pagination page={reports.page} totalPages={reports.totalPages} />
        )}
      </div>
      {/* Display the form with ID 2 */}
      <div className="container mt-16">
        <h3 className="heading-2 -mb-4">Would you like to suggest a company for us to profile?</h3>

        <FormBlock
          form={form as any}
          enableIntro={true}
          inset={true}
          background={false}
        />
      </div>
    </div>
  )
}

export function generateMetadata(): Metadata {
  return {
    title: `ekoIntelligence Posts`,
  }
}
