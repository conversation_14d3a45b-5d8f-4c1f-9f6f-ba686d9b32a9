'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'
import { usePlausible } from 'next-plausible'

const PageClient: React.FC<{slug:string}> = ({slug}) => {
  /* Force the header to be dark mode while we have an image behind it */
  const { setHeaderTheme } = useHeaderTheme()
  const plausible= usePlausible();
  useEffect(() => {
    setHeaderTheme('dark')
  }, [setHeaderTheme])
  useEffect(() => {
    plausible('Company Report Viewed', { props: {slug} })
  }, [plausible,slug])
  return <React.Fragment />
}

export default PageClient
