/**
 * Plugins for Tailwind CSS
 */
import shadowsPlugin from './shadows.mjs'
import glassEffectsPlugin from './glass-effects.mjs'
import transitionsPlugin from './transitions.mjs'
import spacingPlugin from './spacing.mjs'
import typographyPlugin from './typography.mjs'
import richTextPlugin from './rich-text.mjs'
import bordersPlugin from './borders.mjs'
import layoutPlugin from './layout.mjs'
import textShadowsPlugin from './text-shadows.mjs'
import neutralColorsPlugin from './neutral-colors.mjs'

export const customPlugins = [
  // Add custom plugins
  function(params) {
    shadowsPlugin(params);
    glassEffectsPlugin(params);
    transitionsPlugin(params);
    spacingPlugin(params);
    typographyPlugin(params);
    richTextPlugin(params);
    bordersPlugin(params);
    layoutPlugin(params);
    textShadowsPlugin(params);
    neutralColorsPlugin(params);
  },
];
