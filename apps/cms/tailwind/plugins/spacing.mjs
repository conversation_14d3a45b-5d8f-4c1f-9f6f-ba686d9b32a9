/**
 * Spacing utilities plugin for Tailwind CSS
 */
export default function spacingPlugin({ addUtilities }) {
  // Section spacing utilities
  const sectionSpacingUtilities = {
    '.section-spacing-small': {
      paddingTop: '2rem',
      paddingBottom: '2rem',
      '@media (min-width: 768px)': {
        paddingTop: '3rem',
        paddingBottom: '3rem',
      },
    },
    '.section-spacing-medium': {
      paddingTop: '3rem',
      paddingBottom: '3rem',
      '@media (min-width: 768px)': {
        paddingTop: '5rem',
        paddingBottom: '5rem',
      },
    },
    '.section-spacing-large': {
      paddingTop: '4rem',
      paddingBottom: '4rem',
      '@media (min-width: 768px)': {
        paddingTop: '6rem',
        paddingBottom: '6rem',
      },
    },
  };
  
  // Content spacing utilities
  const contentSpacingUtilities = {
    '.content-spacing-small': {
      marginBottom: '1rem',
      '@media (min-width: 768px)': {
        marginBottom: '1.5rem',
      },
    },
    '.content-spacing-medium': {
      marginBottom: '2rem',
      '@media (min-width: 768px)': {
        marginBottom: '3rem',
      },
    },
    '.content-spacing-large': {
      marginBottom: '3rem',
      '@media (min-width: 768px)': {
        marginBottom: '4rem',
      },
    },
  };
  
  // Container utilities
  const containerUtilities = {
    '.container-small': {
      width: '100%',
      maxWidth: '640px',
      marginLeft: 'auto',
      marginRight: 'auto',
      paddingLeft: '1rem',
      paddingRight: '1rem',
      '@media (min-width: 768px)': {
        paddingLeft: '2rem',
        paddingRight: '2rem',
      },
    },
    '.container-medium': {
      width: '100%',
      maxWidth: '768px',
      marginLeft: 'auto',
      marginRight: 'auto',
      paddingLeft: '1rem',
      paddingRight: '1rem',
      '@media (min-width: 768px)': {
        paddingLeft: '2rem',
        paddingRight: '2rem',
      },
    },
    '.container-large': {
      width: '100%',
      maxWidth: '1024px',
      marginLeft: 'auto',
      marginRight: 'auto',
      paddingLeft: '1rem',
      paddingRight: '1rem',
      '@media (min-width: 768px)': {
        paddingLeft: '2rem',
        paddingRight: '2rem',
      },
    },
    '.container-xlarge': {
      width: '100%',
      maxWidth: '1280px',
      marginLeft: 'auto',
      marginRight: 'auto',
      paddingLeft: '1rem',
      paddingRight: '1rem',
      '@media (min-width: 768px)': {
        paddingLeft: '2rem',
        paddingRight: '2rem',
      },
    },
  };
  
  addUtilities(sectionSpacingUtilities, ['responsive']);
  addUtilities(contentSpacingUtilities, ['responsive']);
  addUtilities(containerUtilities, ['responsive']);
}
