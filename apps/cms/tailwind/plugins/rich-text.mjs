/**
 * Rich text styling plugin for Tailwind CSS
 */
export default function richTextPlugin({ addComponents }) {
  const richTextStyles = {
    '.rich-text h1': {
      fontSize: '2.5rem',
      lineHeight: '1.2',
      fontWeight: '600',
      letterSpacing: '-0.015em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '3.5rem',
      },
    },
    '.rich-text h2': {
      fontSize: '2rem',
      lineHeight: '1.3',
      fontWeight: '600',
      letterSpacing: '-0.015em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '2.5rem',
      },
    },
    '.rich-text h3': {
      fontSize: '1.5rem',
      lineHeight: '1.3',
      fontWeight: '600',
      letterSpacing: '-0.01em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '2rem',
      },
    },
    '.rich-text h4': {
      fontSize: '1.25rem',
      lineHeight: '1.4',
      fontWeight: '600',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '1.5rem',
      },
    },
    '.rich-text h5': {
      fontSize: '1.125rem',
      lineHeight: '1.4',
      fontWeight: '600',
      marginBottom: '0.5em',
    },
    '.rich-text h6': {
      fontSize: '1rem',
      lineHeight: '1.5',
      fontWeight: '600',
      marginBottom: '0.5em',
    },
    '.rich-text p': {
      fontSize: '1rem',
      lineHeight: '1.65',
      marginBottom: '1.25em',
      maxWidth: '70ch',
      '@media (min-width: 768px)': {
        fontSize: '1.125rem',
      },
    },
    '.rich-text ul, .rich-text ol': {
      marginBottom: '1.5rem',
      marginLeft: '1.5rem',
    },
    '.rich-text ul': {
      listStyleType: 'disc',
    },
    '.rich-text ol': {
      listStyleType: 'decimal',
    },
    '.rich-text li': {
      marginBottom: '0.5rem',
    },
    '.rich-text a': {
        textDecoration: 'underline',
        textDecorationThickness: '1px',
        textDecorationColor: 'color-mix(in srgb, currentColor 50%, transparent)',
        textUnderlineOffset: '2px',
        transition: 'all 250ms ease-in-out',
        '&:hover': {
          textDecoration: 'underline',
          textDecorationStyle: 'solid',
        },

    },
    '.rich-text blockquote': {
      borderLeftWidth: '4px',
      borderLeftColor: 'hsl(var(--brand-primary))',
      paddingLeft: '1rem',
      fontStyle: 'italic',
      marginTop: '1.5rem',
      marginBottom: '1.5rem',
    },
    '.rich-text pre': {
      backgroundColor: 'hsl(var(--muted))',
      padding: '1rem',
      borderRadius: '0.5rem',
      overflowX: 'auto',
      marginTop: '1.5rem',
      marginBottom: '1.5rem',
    },
    '.rich-text code': {
      backgroundColor: 'hsl(var(--muted))',
      padding: '0.125rem 0.25rem',
      borderRadius: '0.25rem',
      fontSize: '0.875rem',
    },
    '.rich-text img': {
      borderRadius: '0.75rem',
      marginTop: '1.5rem',
      marginBottom: '1.5rem',
    },
    '.rich-text hr': {
      borderTopWidth: '1px',
      borderColor: 'hsl(var(--border))',
      marginTop: '2rem',
      marginBottom: '2rem',
    },
    '.rich-text table': {
      width: '100%',
      borderCollapse: 'collapse',
      marginTop: '1.5rem',
      marginBottom: '1.5rem',
    },
    '.rich-text th, .rich-text td': {
      borderWidth: '1px',
      borderColor: 'hsl(var(--border))',
      padding: '0.5rem',
    },
    '.rich-text th': {
      backgroundColor: 'hsl(var(--muted))',
      fontWeight: '600',
    },
  };

  addComponents(richTextStyles);
}
