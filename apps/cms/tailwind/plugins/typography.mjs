/**
 * Typography utilities plugin for Tailwind CSS
 */
export default function typographyPlugin({ addComponents }) {
  // Heading styles
  const headingStyles = {
    '.heading-1': {
      fontSize: '2.5rem',
      lineHeight: '1.2',
      fontWeight: '600',
      letterSpacing: '-0.015em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '3.5rem',
      },
    },
    '.heading-2': {
      fontSize: '2rem',
      lineHeight: '1.3',
      fontWeight: '600',
      letterSpacing: '-0.015em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '2.5rem',
      },
    },
    '.heading-3': {
      fontSize: '1.5rem',
      lineHeight: '1.3',
      fontWeight: '600',
      letterSpacing: '-0.01em',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '2rem',
      },
    },
    '.heading-4': {
      fontSize: '1.25rem',
      lineHeight: '1.4',
      fontWeight: '600',
      marginBottom: '0.5em',
      '@media (min-width: 768px)': {
        fontSize: '1.5rem',
      },
    },
    '.heading-5': {
      fontSize: '1.125rem',
      lineHeight: '1.4',
      fontWeight: '600',
      marginBottom: '0.5em',
    },
    '.heading-6': {
      fontSize: '1rem',
      lineHeight: '1.5',
      fontWeight: '600',
      marginBottom: '0.5em',
    },
  };
  
  // Text styles
  const textStyles = {
    '.text-lead': {
      fontSize: '1.25rem',
      lineHeight: '1.65',
      fontWeight: '400',
      marginBottom: '1.25em',
      maxWidth: '70ch',
      '@media (min-width: 768px)': {
        fontSize: '1.5rem',
      },
    },
    '.text-body': {
      fontSize: '1rem',
      lineHeight: '1.65',
      fontWeight: '400',
      marginBottom: '1.25em',
      maxWidth: '70ch',
      '@media (min-width: 768px)': {
        fontSize: '1.125rem',
      },
    },
    '.text-small': {
      fontSize: '0.875rem',
      lineHeight: '1.6',
      fontWeight: '400',
    },
    '.text-tiny': {
      fontSize: '0.75rem',
      lineHeight: '1.5',
      fontWeight: '400',
    },
  };
  
  // Text colors
  const textColors = {
    '.text-primary': {
      color: 'hsl(var(--brand-primary))',
    },
    '.text-muted': {
      color: 'hsl(var(--muted-foreground))',
    },
    '.text-foreground': {
      color: 'hsl(var(--foreground))',
    },
    '.text-background': {
      color: 'hsl(var(--background))',
    },
    '.text-accent': {
      color: 'hsl(var(--accent))',
    },
    '.text-success': {
      color: 'hsl(var(--success))',
    },
    '.text-warning': {
      color: 'hsl(var(--warning))',
    },
    '.text-error': {
      color: 'hsl(var(--error))',
    },
  };
  
  // Add all typography components
  addComponents(headingStyles, ['responsive']);
  addComponents(textStyles, ['responsive']);
  addComponents(textColors);
}
