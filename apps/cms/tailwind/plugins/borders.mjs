/**
 * Border utilities plugin for Tailwind CSS
 */
export default function bordersPlugin({ addUtilities }) {
  // Border utilities
  const borderUtilities = {
    '.border-standard': {
      border: '1px solid hsl(var(--border))',
    },
    '.border-subtle': {
      border: '1px solid hsla(var(--border), 0.5)',
    },
    '.border-strong': {
      border: '2px solid hsl(var(--border))',
    },
    '.border-primary': {
      border: '1px solid hsl(var(--brand-primary))',
    },
    '.border-primary-subtle': {
      border: '1px solid hsla(var(--brand-primary), 0.3)',
    },
  };
  
  addUtilities(borderUtilities, ['responsive']);
}
