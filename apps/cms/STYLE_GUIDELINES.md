# CMS Style Guidelines

This document outlines the style guidelines and best practices for the CMS application.

## Design Philosophy

The CMS follows a glass-morphism design language with heavily rounded elements:

1. **Glass-Morphism**: Translucent, frosted glass-like surfaces with subtle backdrop blur effects
2. **Rounded Corners**: Generous border radii for a modern, approachable feel
3. **Subtle Shadows**: Light shadows to create depth without heaviness
4. **Layered Elements**: Creating depth through overlapping translucent elements
5. **Clean Typography**: Clear, readable text with proper spacing and hierarchy

This design approach creates a modern, premium feel while maintaining clarity and usability. The glass-morphism design will be applied to other projects in the ecosystem over time.

## Component Structure

### Block Components

All block components should:

1. Use the `BlockWrapper` component for consistent spacing and styling
2. Follow a consistent structure:
   ```tsx
   <BlockWrapper
     inset={inset}
     background={background}
     backgroundMedia={backgroundMedia}
     darkMode={darkMode}
   >
     <div className="...">
       {/* Component content */}
     </div>
   </BlockWrapper>
   ```

3. Use the `Section` component for consistent spacing when needed
4. Use the `CardGeneric` component for card-like UI elements

### Typography

Use consistent typography styles:

- Headings: Use the `heading-1` through `heading-6` classes or the `Typography` component with appropriate variant
- Body text: Use the `text-lead`, `text-body`, `text-small`, or `text-tiny` classes
- Text colors: Use the `text-primary` or `text-muted` classes

See the [Visual Polish Guide](./VISUAL_POLISH.md) for more details.

## Spacing

Follow these spacing guidelines:

- Section spacing: Use the `section-spacing-small`, `section-spacing-medium`, or `section-spacing-large` classes
- Content spacing: Use the `content-spacing-small`, `content-spacing-medium`, or `content-spacing-large` classes
- Container usage: Use the `container-small`, `container-medium`, `container-large`, or `container-xlarge` classes
- Grid layouts: Use the `grid-2-cols`, `grid-3-cols`, or `grid-4-cols` classes

See the [Visual Polish Guide](./VISUAL_POLISH.md) for more details.

## Colors

Use CSS variables for colors:

- Brand colors: `--brand-primary`, `--brand-primary-dark`, `--brand-contrast`
- UI colors: `--background`, `--foreground`, `--card`, etc.
- Semantic colors: `--success`, `--warning`, `--error`

## Effects

Glass-morphism is the primary visual effect used throughout the CMS. Use these consistent visual effects:

- **Glass effects**: Use the `glass-effect`, `glass-effect-subtle`, `glass-effect-strong`, or `glass-effect-brand` classes
  - These create translucent surfaces with backdrop blur and subtle borders
  - Perfect for cards, modals, headers, and overlays
  - Combine with rounded corners for the signature look
- **Shadows**: Use the `shadow-subtle`, `shadow-soft`, `shadow-medium`, or `shadow-strong` classes
  - Subtle shadows work best with glass elements to enhance depth
- **Transitions**: Use the `transition-fast`, `transition-standard`, or `transition-slow` classes
  - Smooth transitions enhance the premium feel of the interface
- **Hover effects**: Use the `hover-lift`, `hover-lift-subtle`, `hover-scale`, or `hover-scale-subtle` classes
  - These create subtle movement on interaction
- **Noise texture**: Use the `bg-noise` class
  - Adds subtle texture to solid backgrounds

See the [Visual Polish Guide](./VISUAL_POLISH.md) for more details.

## Responsive Design

Follow these responsive design principles:

- Mobile-first approach
- Use Tailwind's responsive prefixes consistently
- Test on multiple screen sizes

## Accessibility

Ensure components are accessible:

- Use semantic HTML
- Provide appropriate ARIA attributes
- Ensure sufficient color contrast
- Support keyboard navigation

## Component Imports

Use consistent import patterns:

- UI components: `@ui/components/...`
- Utility functions: `@utils/lib/...`
- Local components: `@/components/...`

## Tailwind Usage

Follow these Tailwind best practices:

- Use utility classes directly in JSX
- Extract common patterns to components
- Use the `cn()` utility for conditional classes
- Follow the project's Tailwind configuration

## Dark Mode

Support dark mode consistently:

- Use the `dark:` prefix for dark mode styles
- Test in both light and dark modes
- Use CSS variables that respect the current theme

# Visual Polish Guide

This document outlines the visual polish improvements made to the CMS application to enhance consistency and aesthetics.

## Utility Classes

### Shadow Utilities

Consistent shadow effects for elevation:

```css
.shadow-subtle
.shadow-soft
.shadow-medium
.shadow-strong
```

**Usage Example:**
```jsx
<div className="shadow-medium">
  This element has a medium shadow
</div>
```

### Glass Effect Utilities

Consistent glass/backdrop blur effects:

```css
.glass-effect
.glass-effect-subtle
.glass-effect-strong
.glass-effect-brand
```

**Usage Example:**
```jsx
<div className="glass-effect">
  This element has a glass effect
</div>
```

### Transition Utilities

Consistent transition animations:

```css
.transition-fast
.transition-standard
.transition-slow
```

**Usage Example:**
```jsx
<button className="transition-standard">
  This button has a standard transition
</button>
```

### Hover Effect Utilities

Consistent hover animations:

```css
.hover-lift
.hover-lift-subtle
.hover-scale
.hover-scale-subtle
```

**Usage Example:**
```jsx
<div className="hover-lift">
  This element lifts on hover
</div>
```

### Spacing Utilities

Consistent section spacing:

```css
.section-spacing-small
.section-spacing-medium
.section-spacing-large
```

Consistent content spacing:

```css
.content-spacing-small
.content-spacing-medium
.content-spacing-large
```

**Usage Example:**
```jsx
<section className="section-spacing-medium">
  <div className="content-spacing-small">
    This content has small spacing
  </div>
</section>
```

### Typography Utilities

Consistent heading styles:

```css
.heading-1  /* 2.5rem/3.5rem, line-height: 1.2, font-weight: 600, letter-spacing: -0.015em */
.heading-2  /* 2rem/2.5rem, line-height: 1.3, font-weight: 600, letter-spacing: -0.015em */
.heading-3  /* 1.5rem/2rem, line-height: 1.3, font-weight: 600, letter-spacing: -0.01em */
.heading-4  /* 1.25rem/1.5rem, line-height: 1.4, font-weight: 600 */
.heading-5  /* 1.125rem, line-height: 1.4, font-weight: 600 */
.heading-6  /* 1rem, line-height: 1.5, font-weight: 600 */
```

Consistent text styles:

```css
.text-lead   /* 1.25rem/1.5rem, line-height: 1.65, font-weight: 400 */
.text-body   /* 1rem/1.125rem, line-height: 1.65, font-weight: 400 */
.text-small  /* 0.875rem, line-height: 1.6, font-weight: 400 */
.text-tiny   /* 0.75rem, line-height: 1.5, font-weight: 400 */
```

Consistent text colors:

```css
.text-primary    /* Brand primary color */
.text-muted      /* Muted foreground color */
.text-foreground /* Main text color */
.text-background /* Background color (for contrast) */
.text-accent     /* Accent color */
.text-success    /* Success color */
.text-warning    /* Warning color */
.text-error      /* Error color */
```

Rich text styling:

```css
.rich-text  /* Applies consistent styling to all elements within rich text content */
```

**Usage Example:**
```jsx
<h1 className="heading-1">
  This is a heading
</h1>
<p className="text-lead">
  This is lead text
</p>
```

### Border Utilities

The CMS design uses heavily rounded corners as a key visual element. Use these consistent border radius utilities:

```css
.rounded-standard /* 1.5rem - This is the default and preferred radius */
.rounded-small    /* 0.5rem */
.rounded-medium   /* 0.75rem */
.rounded-large    /* 1rem */
.rounded-full     /* 9999px - For circular elements */
```

Consistent border styles to complement the rounded corners:

```css
.border-standard      /* 1px solid border using the border color variable */
.border-subtle        /* 1px solid border with reduced opacity */
.border-strong        /* 2px solid border for emphasis */
.border-primary       /* 1px solid border using the primary brand color */
.border-primary-subtle /* 1px solid border using the primary brand color with reduced opacity */
```

**Usage Example:**
```jsx
<div className="rounded-standard border-subtle glass-effect">
  This element has the signature heavily rounded corners with a subtle border and glass effect
</div>
```

### Layout Utilities

Consistent container widths:

```css
.container-small
.container-medium
.container-large
.container-xlarge
```

Consistent grid layouts:

```css
.grid-2-cols
.grid-3-cols
.grid-4-cols
```

**Usage Example:**
```jsx
<div className="container-medium">
  <div className="grid-3-cols">
    <div>Column 1</div>
    <div>Column 2</div>
    <div>Column 3</div>
  </div>
</div>
```

## Theme Variables

All utility classes are available through the theme variables in `theme-variables.ts`:

```typescript
import { effects, spacing, borderRadius, typography } from '@/styles/theme-variables';

// Examples
const glassEffect = effects.glassMorphism;
const sectionSpacing = spacing.section.medium;
const roundedCorners = borderRadius.medium;
const headingStyle = typography.headings.h1;
```

## Best Practices

1. **Use utility classes consistently** - Prefer the utility classes over custom CSS
2. **Combine utility classes** - Combine multiple utility classes for complex styling
3. **Use theme variables** - Use the theme variables for consistent styling
4. **Responsive design** - Use the responsive variants of utility classes
5. **Dark mode** - Test all components in both light and dark mode

## Examples

### Typography Example

```jsx
<div className="container-medium">
  <h1 className="heading-1 content-spacing-small">Main Heading</h1>
  <p className="text-lead content-spacing-medium">This is a lead paragraph that introduces the content. It uses the text-lead class for larger, more prominent text.</p>

  <h2 className="heading-2 content-spacing-small">Secondary Heading</h2>
  <p className="text-body content-spacing-small">This is a regular paragraph using the text-body class. It has a comfortable line height and proper spacing.</p>
  <p className="text-body content-spacing-medium">Another paragraph with the same styling. Notice the consistent spacing between paragraphs.</p>

  <h3 className="heading-3 content-spacing-small">Tertiary Heading</h3>
  <p className="text-body content-spacing-small">More body text with <span className="text-primary">highlighted text</span> using the text-primary class.</p>
  <p className="text-small content-spacing-medium"><span className="text-muted">This is smaller text with muted styling, perfect for captions or notes.</span></p>

  <div className="rich-text content-spacing-medium">
    <h4>Rich Text Content</h4>
    <p>This content is wrapped in the rich-text class, which applies consistent styling to all elements within it.</p>
    <ul>
      <li>List item one</li>
      <li>List item two</li>
      <li>List item three</li>
    </ul>
    <blockquote>This is a blockquote with consistent styling.</blockquote>
  </div>
</div>
```

### Card Example

```jsx
<div className="rounded-standard shadow-soft glass-effect hover-lift-subtle transition-standard p-6">
  <h3 className="heading-3 content-spacing-small">Card Title</h3>
  <p className="text-body">Card content goes here</p>
</div>
```

This creates a card with the signature glass-morphism effect and heavily rounded corners that define the CMS design language.

### Button Example

```jsx
<button className="rounded-standard shadow-subtle hover-lift-subtle transition-standard bg-brand text-white px-4 py-2">
  Click Me
</button>
```

Buttons use the same rounded design language for consistency across interactive elements.

### Section Example

```jsx
<section className="section-spacing-medium">
  <div className="container-large">
    <h2 className="heading-2 content-spacing-medium">Section Title</h2>
    <div className="grid-3-cols">
      <div>Column 1</div>
      <div>Column 2</div>
      <div>Column 3</div>
    </div>
  </div>
</section>
```

##Standardized Components

### BlockWrapper

A standardized wrapper component for all content blocks that provides:
- Consistent spacing and padding
- Background handling (solid colors, gradients, images)
- Inset styling
- Dark mode support
- Responsive design

**Files:**
- `apps/cms/src/components/BlockWrapper/index.tsx`

### Section

A standardized section component that provides:
- Consistent vertical spacing options
- Container support for horizontal spacing
- ID support for anchor links

**Files:**
- `apps/cms/src/components/Section/index.tsx`

### CardGeneric

A standardized card component that provides:
- Multiple style variants including glass-morphism
- Heavily rounded corners (standard 1.5rem radius)
- Hover effects with subtle lift
- Click handling
- Consistent styling

**Files:**
- `apps/cms/src/components/ui/card-generic.tsx`

### ButtonStyled

A styled button component that extends the base Button component with:
- Additional styling options
- Link functionality
- Icon support
- Size variants

**Files:**
- `apps/cms/src/components/ui/button-styled.tsx`

### Typography

A standardized typography component that provides:
- Consistent text styling
- Multiple variants (h1-h6, p, lead, small, muted)
- Custom element type support

**Files:**
- `apps/cms/src/components/Typography/index.tsx`

### Image

A standardized image component that wraps Next.js's Image component with:
- Consistent styling options
- Aspect ratio control
- Rounded corners
- Overlay support
- Object fit control

**Files:**
- `apps/cms/src/components/ui/image.tsx`

### GlassCard

A standardized glass card component that showcases the glass-morphism design language:
- Glass morphism styling with backdrop blur
- Heavily rounded corners (standard 1.5rem radius)
- Image support with overlay text
- Category display
- Title and description
- Hover animations

**Files:**
- `apps/cms/src/components/GlassCard/index.tsx`

### Alert

A standardized alert component that provides:
- Multiple style variants (info, success, warning, error)
- Title support
- Dismissible functionality
- Icon support

**Files:**
- `apps/cms/src/components/ui/alert.tsx`

### BadgeStyled

A standardized badge component that provides:
- Multiple style variants
- Size options
- Rounded corners
- Icon support


**Files:**
- `apps/cms/src/components/ui/badge-styled.tsx`

### TabsStyled

A standardized tabs component that provides:
- Multiple style variants
- Horizontal and vertical orientation
- Icon support
- Disabled state

**Files:**
- `apps/cms/src/components/ui/tabs-styled.tsx`

### TooltipStyled

A standardized tooltip component that provides:
- Multiple style variants
- Position and alignment options
- Delay control

**Files:**
- `apps/cms/src/components/ui/tooltip-styled.tsx`

### Breadcrumb

A standardized breadcrumb component that provides:
- Consistent navigation
- Home icon support
- Custom separator

**Files:**
- `apps/cms/src/components/ui/breadcrumb.tsx`

### PageHeader

A standardized page header component that provides:
- Title and description
- Breadcrumb integration
- Action buttons
- Responsive layout

**Files:**
- `apps/cms/src/components/ui/page-header.tsx`

## Centralized Theme Variables

A centralized theme variables file that provides:
- Consistent color definitions
- Spacing values
- Typography settings
- Border radius values
- Effect definitions

**Files:**
- `apps/cms/src/styles/theme-variables.ts`
