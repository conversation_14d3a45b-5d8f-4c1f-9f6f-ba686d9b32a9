# CMS Knowledge Base for Claude

## Header and <PERSON> Layout

### Header Component

#### Bugfix: FAQ Page Header Spacing

The FAQ page previously had an issue where the header navigation bar appeared down from the top of the page while other pages displayed correctly. This was fixed by:

1. Adding a conditional spacer div after the header in `Component.client.tsx`
2. Making the spacer conditional on the current path (only adding it for non-home pages)
3. Setting the height of the spacer to match the header's height (72px)

The issue occurred because:
- The header uses `position: fixed` which takes it out of the normal document flow
- Without a spacer, content flows underneath the header
- The FAQ block rendered directly under the header without proper spacing

This fix ensures proper spacing on interior pages while maintaining the hero's full viewport coverage on the home page.

The header is implemented in two parts:
- `/src/Header/Component.tsx` - Server component that fetches header data
- `/src/Header/Component.client.tsx` - Client component that handles interactions

The header uses the following key patterns:

1. **Fixed Positioning with Conditional Spacer**:
   - The header is positioned as `fixed` when visible, which takes it out of the normal document flow
   - To prevent content from flowing underneath the header, a spacer div is added only on non-home pages:
   ```tsx
   {showHeader && pathname !== '/' && <div className="h-[72px]"></div>}
   ```

2. **Transparent to Solid Transition**:
   - The header starts transparent on the home page
   - It transitions to a solid background with backdrop blur when scrolled
   - The `showHeader` state controls this transition
   - The header uses conditional classes with Tailwind CSS:
   ```tsx
   className={cn(
     "absolute left-0 right-0 z-50 overflow-visible transition-all duration-300",
     showHeader
       ? "translate-y-0 fixed backdrop-blur-xl bg-background/90 dark:bg-background/85 border-b border-foreground/10 shadow-soft"
       : "-translate-y-32 bg-transparent"
   )}
   ```

3. **Theme Integration**:
   - The header integrates with the theme system through `useHeaderTheme`
   - Different page types can set different header themes

### Front Page Hero

The front page hero component is designed to take the full viewport height on initial load:

1. **Height Settings**:
   - Uses `100vh` for the expanded (initial) state
   - Uses pixel-based heights for the collapsed (scrolled) state
   ```tsx
   setVariants({
     expanded: { height: '100vh' },
     collapsed: { height: collapsedHeight },
   })
   ```

2. **Scroll Detection**:
   - Detects scroll position using Framer Motion's useScroll hook
   - Transitions to the collapsed state when scrolled past a threshold (100px)
   ```tsx
   useEffect(() => {
     const unsubscribe = scrollY.onChange((latest) => {
       if (!isScrolled && latest > 100) {
         setIsScrolled(true)
       }
     })
     return () => unsubscribe()
   }, [scrollY, isScrolled])
   ```

3. **Important CSS Properties**:
   - `overflow-hidden` on the main container to prevent content overflow
   - Uses the `AuroraBackground` component for the animated background
   - Sets `z-index` properly to ensure correct layering

## Form Submissions to Slack

Form submissions from the Payload CMS are automatically sent to a Slack channel using a webhook. When a user submits a form, a notification is posted to the configured Slack channel with all the form fields and values.

### Configuration

1. Create a Slack Incoming Webhook:
   - Go to your Slack workspace → Settings & Administration → Manage Apps
   - Search for "Incoming Webhooks" and add to your workspace
   - Create a new webhook for a specific channel
   - Copy the webhook URL

2. Add the webhook URL to your environment variables:
   ```
   SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
   ```

3. The feature uses the following files:
   - `/src/utilities/slack.ts` - Formats and sends the message to Slack
   - `/src/hooks/sendFormToSlack.ts` - Hook that gets triggered after form submission
   - `/src/plugins/index.ts` - Configures the form-builder plugin to use the hook

## Adding Blocks to Lexical Editor

To add a new block to Payload CMS's Lexical Editor, follow these steps:

1. **Create block files** in `/blocks/[BlockName]/` directory:
   - `Component.tsx` - React component for rendering the block
   - `config.ts` - Block schema configuration
   - `index.ts` - Export file

2. **In config.ts:**
   - Define the block schema with its fields
   - Configure it as a Block type
   - If the block needs to contain rich text, use `textarea` instead of `richText` to avoid recursion issues

```typescript
// config.ts example
import type { Block } from 'payload'

export const YourBlock: Block = {
  slug: 'yourBlockName',
  interfaceName: 'YourBlockInterface',
  fields: [
    {
      name: 'content',
      type: 'textarea',  // Don't use 'richText' within blocks to avoid recursion
      required: true,
    },
    // Other fields...
  ],
}
```

3. **In index.ts:**
   - Export the block config as default

```typescript
// index.ts example
import { YourBlock } from './config'

export default YourBlock
```

4. **In Component.tsx:**
   - Create the React component for rendering the block
   - If it contains a textarea that was converted from richText, be sure to render it correctly

```typescript
// Component.tsx example
export const YourBlockComponent: React.FC<YourBlockProps> = (props) => {
  const { content } = props
  
  return (
    <div>
      <p>{content}</p>
    </div>
  )
}
```

5. **Add to defaultLexical.ts:**
   - Import the block
   - Add it to BlocksFeature

```typescript
import YourBlock from '../blocks/YourBlock'

export const defaultLexical: Config['editor'] = lexicalEditor({
  features: () => {
    return [
      // Other features...
      BlocksFeature({
        blocks: [YourBlock],
      }),
    ]
  },
})
```

6. **Update RichText/index.tsx:**
   - Import the block component
   - Add its type to NodeTypes
   - Add a converter for the block in jsxConverters

```typescript
// Import the component and its type
import { YourBlockComponent } from '@/blocks/YourBlock/Component'
import type { YourBlock as YourBlockProps } from '@/payload-types'

// Add to NodeTypes
type NodeTypes =
  | DefaultNodeTypes
  | SerializedBlockNode<YourBlockProps | OtherBlockProps>

// Add converter
const jsxConverters: JSXConvertersFunction<NodeTypes> = ({ defaultConverters }) => ({
  ...defaultConverters,
  blocks: {
    // Other blocks...
    yourBlockName: ({ node }) => <YourBlockComponent {...node.fields} />,
  },
})
```

7. **Restart the development server** for changes to take effect.

## Adding a New Layout Block

The CMS has a block-based layout system for Pages and Posts. To create a new layout block:

1. **Create the block directory and files:**
   - Create a new directory in `/blocks/` (e.g., `/blocks/ImageTextFeature/`)
   - Create three essential files:
     - `Component.tsx` - React component to render the block
     - `config.ts` - Block schema definition
     - `index.ts` - Export file for easy importing

2. **Define the block schema in config.ts:**
   ```typescript
   import { Block } from 'payload'

   const BlockName: Block = {
     slug: 'blockName',  // Used as identifier in the database
     interfaceName: 'IBlockNameBlock',  // TypeScript interface name
     fields: [
       // Define your fields here - text, media, selects, etc.
       {
         name: 'fieldName',
         type: 'text',  // Or other field types
         label: 'Field Label',
         required: false,
       },
       // Media field example
       {
         name: 'media',
         type: 'upload',
         label: 'Image',
         relationTo: 'media',
         required: true,
       },
     ],
   }
   
   export default BlockName
   ```

3. **Create the React component in Component.tsx:**
   ```typescript
   import React from 'react'
   import { Media } from '@/components/Media'
   import { cn } from '@/utilities/ui'
   
   export const BlockNameComponent: React.FC<IBlockNameBlock> = ({
     fieldName,
     media,
     // Other props from your schema
   }) => {
     return (
       <section className="container py-16">
         {/* Your component JSX */}
       </section>
     )
   }
   
   export default BlockNameComponent
   ```

4. **Create the export file in index.ts:**
   ```typescript
   import BlockNameComponent from './Component'
   import BlockName from './config'
   
   export { BlockNameComponent, BlockName }
   export default BlockName
   ```

5. **Register the block in RenderBlocks.tsx:**
   - Import the component at the top:
     ```typescript
     import BlockNameComponent from '@/blocks/BlockName/Component'
     ```
   - Add it to the blockComponents mapping:
     ```typescript
     const blockComponents = {
       // Other blocks...
       blockName: BlockNameComponent,
     }
     ```

6. **Add the block to Pages/index.ts:**
   - Import the block config at the top:
     ```typescript
     import BlockName from '@/blocks/BlockName/config'
     ```
   - Add it to the blocks array:
     ```typescript
     blocks: [
       // Other blocks...
       BlockName
     ]
     ```

7. **Add to Posts/index.ts if needed:**
   - Same as with Pages/index.ts
   - Also add to the BlocksFeature:
     ```typescript
     BlocksFeature({ 
       blocks: [
         // Other blocks...
         BlockName
       ] 
     }),
     ```

8. **Common styling patterns:**
   - Use Tailwind CSS for styling
   - Use `container` class for standard width with responsive padding
   - Use the `cn()` utility for conditional class names
   - Follow responsive patterns (mobile-first)
   - Use `@/components/Media` for image handling

## Common Issues

- **Recursion errors:** If you're getting strange errors or blocks not rendering, ensure you're not using richText fields within blocks that will be used inside rich text editors
- **Block not showing up:** Ensure the block is properly added to both the defaultLexical.ts and the RichText/index.tsx converters
- **Rendering issues:** Check that the Component is correctly rendering the data it receives from the editor
- **Type errors:** After adding new blocks, you may need to regenerate the Payload types with `npx payload generate:types`
- **Database schema errors:** When adding new blocks or fields, you need to run migrations to update the database schema:
  ```bash
  # Create a migration file
  pnpm payload migrate:create
  
  # Run migrations (this will ask for confirmation)
  pnpm payload migrate
  
  # If you get database errors like "relation does not exist", always run migrations
  ```