import { withPayload } from '@payloadcms/next/withPayload'
import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  :  process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      ...[NEXT_PUBLIC_SERVER_URL /* 'https://example.com' */].map((item) => {
        const url = new URL(item)

        return {
          hostname: url.hostname,
          protocol: url.protocol.replace(':', ''),
        }
      }),
      {
        protocol: 'https',
        hostname: 'ekointel.com',
        pathname: '/api/media/**',
      },
      {
        protocol: 'https',
        hostname: 'ekointel.com',
        pathname: '/media/**',
      },
    ]
  },
  reactStrictMode: true,
  redirects,

  // Add webpack configuration to handle Cloudflare sockets and node modules
  webpack: (config, { webpack }) => {
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^pg-native$|^cloudflare:sockets$/,
      })
    )

    // Handle node: scheme imports
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      'fs/promises': false,
      path: false,
      os: false,
      https: false,
      http: false,
      zlib: false,
      stream: false,
    }

    return config
  },
}

export default withPayload(nextConfig)
