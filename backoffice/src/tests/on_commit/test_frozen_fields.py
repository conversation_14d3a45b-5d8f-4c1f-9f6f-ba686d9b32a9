"""
Test frozen field protection in update_model_recursive function.
"""
import pytest
from pydantic import BaseModel, Field
from eko.util.obj import update_model_recursive


class TestModel(BaseModel):
    """Test model with both frozen and mutable fields."""
    mutable_field: str = Field(..., description="This field can be updated")
    frozen_field: int = Field(..., frozen=True, description="This field cannot be updated")
    another_mutable: str = Field(default="default", description="Another mutable field")


def test_frozen_field_protection():
    """Test that frozen fields are protected from updates."""
    # Create a test model
    original = TestModel(
        mutable_field="original_value",
        frozen_field=42,
        another_mutable="original_mutable"
    )
    
    # Try to update both frozen and mutable fields
    updates = {
        "mutable_field": "updated_value",
        "frozen_field": 999,  # This should be ignored
        "another_mutable": "updated_mutable"
    }
    
    # Apply updates
    updated = update_model_recursive(original, updates)
    
    # Verify mutable fields were updated
    assert updated.mutable_field == "updated_value"
    assert updated.another_mutable == "updated_mutable"
    
    # Verify frozen field was NOT updated
    assert updated.frozen_field == 42  # Should remain original value


def test_effect_flag_impact_protection():
    """Test that EffectFlagModel impact field is protected."""
    # Create a simple test model with frozen impact field to simulate EffectFlagModel
    from pydantic import BaseModel, Field

    class MockEffectFlag(BaseModel):
        title: str = Field(..., description="Title")
        impact: int = Field(..., frozen=True, description="Impact - frozen")
        authentic: int = Field(..., description="Authentic - mutable")

    # Create a test flag
    flag = MockEffectFlag(
        title="Test Flag",
        impact=75,  # This should be protected
        authentic=80
    )

    # Try to update impact and other fields
    updates = {
        "impact": 25,  # This should be ignored due to frozen=True
        "authentic": 95,  # This should be updated
        "title": "Updated Title"  # This should be updated
    }

    updated_flag = update_model_recursive(flag, updates)

    # Verify impact was NOT changed (frozen field)
    assert updated_flag.impact == 75

    # Verify other fields were updated
    assert updated_flag.authentic == 95
    assert updated_flag.title == "Updated Title"


if __name__ == "__main__":
    pytest.main([__file__])
