# Frozen Fields Protection for Effect Flags

## Problem Statement

The impact value calculated by `ImpactMeasurementService` in `effect_flags_helpers.py` (lines 441-442) was being potentially modified by the `verify_analysis` function during the verification process. This could override carefully calculated impact measurements with subjective LLM assessments.

## Root Cause Analysis

The issue was discovered in the following flow:

1. **Initial Calculation**: Impact calculated from `impact_measurement.net_impact_score * 100.0`
2. **Flag Creation**: Impact assigned to the EffectFlagModel
3. **Flag Merging**: Impact from primary flag preserved during merging
4. **🚨 VERIFICATION STEP**: `verify_analysis()` function could modify ANY field including impact
5. **Storage**: The potentially modified flag was stored

The `verify_analysis` function in `citations.py` uses `update_model_recursive` which could update any field when the LLM returned a `REDO` response with corrected field values.

## Solution Implemented

### 1. Frozen Field Protection

Added `frozen=True` to critical fields in `EffectFlagModel`:

```python
# In backoffice/src/eko/models/vector/derived/effect.py
impact: int = Field(..., ge=0, le=100, frozen=True, description="Impact rating (0-100) - immutable after creation")
impact_measurement: EventImpactMeasurement = Field(None, frozen=True, description="The impact measurement for this flag - immutable after creation")
impact_evaluation: EventImpactEvaluation = Field(None, frozen=True, description="The impact evaluation for this flag - immutable after creation")
```

### 2. Respect Frozen Fields in Updates

Modified `update_model_recursive` in `eko/util/obj.py` to respect frozen fields:

```python
# Check if field is frozen (immutable)
field_info = model_fields[field_name]
if hasattr(field_info, 'frozen') and field_info.frozen:
    logger.warning(f"Skipping update to frozen field '{field_name}' in model {type(base).__name__}")
    continue
```

### 3. Fixed Deprecated API Usage

Updated to use class-level `model_fields` instead of instance-level (deprecated in Pydantic 2.11):

```python
# Use class-level model_fields instead of instance-level
model_fields = type(model_copy).model_fields
```

## Protected Fields

The following fields are now protected from modification during verification:

- **`impact`**: The calculated impact score (0-100)
- **`impact_measurement`**: The complete impact measurement from ImpactMeasurementService
- **`impact_evaluation`**: The impact evaluation with quality checks

## Testing

Created comprehensive tests to verify the protection works:

1. **Basic Frozen Field Test**: Verifies frozen fields are skipped during updates
2. **Warning Logging Test**: Ensures warnings are logged when frozen fields are attempted to be updated
3. **Mixed Update Test**: Confirms mutable fields are still updated while frozen fields are protected

## Benefits

1. **Data Integrity**: Impact calculations from ImpactMeasurementService are preserved
2. **Audit Trail**: Clear logging when attempts are made to modify protected fields
3. **Selective Protection**: Only critical fields are protected, allowing other fields to be corrected during verification
4. **Backward Compatibility**: Existing code continues to work, just with additional protection

## Usage

The protection is automatic. When `verify_analysis` attempts to update frozen fields, they will be:

1. **Skipped**: The update is ignored
2. **Logged**: A warning is logged indicating the field was skipped
3. **Preserved**: The original value remains unchanged

## Future Considerations

Consider adding `frozen=True` to other critical fields that should not be modified during verification, such as:

- Entity identifiers
- Trace IDs
- Run IDs
- Other calculated metrics that should remain immutable

## Files Modified

1. `backoffice/src/eko/models/vector/derived/effect.py` - Added frozen=True to critical fields
2. `backoffice/src/eko/util/obj.py` - Added frozen field protection logic
3. `backoffice/src/tests/on_commit/test_frozen_fields.py` - Added comprehensive tests
