from copy import deepcopy
from typing import Any, Type, get_origin, get_args, TypeVar

from loguru import logger
from pydantic import BaseModel


def copy_non_none_attributes(source, target):
    for attr in dir(source):
        # Skip private attributes and methods
        if not attr.startswith('__') and not callable(getattr(source, attr)):
            # Check if the source attribute is not None
            if hasattr(source, attr) and getattr(source, attr) is not None:
                # Check if the target object has the same attribute
                if hasattr(target, attr):
                    setattr(target, attr, getattr(source, attr))

T = TypeVar('T', bound=BaseModel)


def update_model_recursive(base: T, updates: dict[str, Any], max_depth: int = 20) -> T:
    """
    Recursively updates a Pydantic model with nested dictionary values.

    Args:
        base: The base Pydantic model to update
        updates: Dictionary containing the update values
        max_depth: Maximum recursion depth to prevent infinite loops

    Returns:
        Updated Pydantic model

    Raises:
        ValueError: If max_depth is exceeded or invalid updates are provided
    """
    if max_depth < 0:
        raise ValueError("Maximum recursion depth exceeded")

    # Create a working copy to prevent modifying the original
    model_copy = deepcopy(base)
    model_dict = model_copy.model_dump()

    def _process_value(field_type: Type, value: Any, current_depth: int) -> Any:
        # Handle None values
        if value is None:
            return None

        # Get the origin type for generic types
        origin_type = get_origin(field_type)
        base_type = field_type if origin_type is None else origin_type

        # Handle nested Pydantic models
        if isinstance(value, dict):
            # Check if field_type itself is a BaseModel subclass
            if isinstance(field_type, type) and issubclass(field_type, BaseModel):
                nested_model = field_type.model_validate(model_dict.get(field_type.__name__, {}))
                return update_model_recursive(nested_model, value, max_depth=current_depth-1)

        # Handle lists/sequences of Pydantic models
        if isinstance(value, list) and origin_type in (list, set, tuple):
            item_type = get_args(field_type)[0] if get_args(field_type) else Any
            if isinstance(item_type, type) and issubclass(item_type, BaseModel):
                return [
                    update_model_recursive(item_type.model_validate({}), item, max_depth=current_depth-1)
                    if isinstance(item, dict)
                    else item
                    for item in value
                ]

        return value

    # Process updates
    for field_name, new_value in updates.items():
        # Use class-level model_fields instead of instance-level (deprecated in Pydantic 2.11)
        model_fields = type(model_copy).model_fields

        if field_name not in model_fields:
            logger.warning(f"Field '{field_name}' not found in model {type(base).__name__}")
            continue

        # Check if field is frozen (immutable)
        field_info = model_fields[field_name]
        if hasattr(field_info, 'frozen') and field_info.frozen:
            logger.warning(f"Skipping update to frozen field '{field_name}' in model {type(base).__name__}")
            continue

        field_type = field_info.annotation
        processed_value = _process_value(field_type, new_value, max_depth)

        try:
            setattr(model_copy, field_name, processed_value)
        except ValueError as e:
            logger.error(f"Error setting field '{field_name}': {str(e)}")
            raise

    return model_copy
