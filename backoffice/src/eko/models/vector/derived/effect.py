from pathlib import Path
from pydantic import Field
from pydantic.json_schema import SkipJsonSchema
from typing import List, Optional, Dict, Any

from eko.analysis_v2.effects.measure_impact import EventImpactMeasurement, EventImpactEvaluation
from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.base_vector_model import BaseVectorModel
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.categories import EffectCategory
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.to_markdown import view_markdown_in_browser
from eko.models.virtual_entity import VirtualEntityModel


class EffectModel(BaseVectorModel):
    """
    The Effect Model captures the effect of an action on an entity.
    It is used to cluster and analyze similar effects from different statements.
    Effect Models represent grouped statements that have similar ethical impacts,
    domains, and outcomes.
    """

    id: Optional[int] = Field(None, description="The database ID of this effect")
    trace_id: str = Field(..., description="Used to trace it's origin")
    run_id: int = Field(..., description="The analysis run that created this flag.")
    effect_type: EffectType= Field(..., description="Type of effect (red=negative, green=positive)")

    statements: List[StatementAndMetadata] = Field(default_factory=list,
                                                 description="Statements that belong to this effect cluster")
    doc_page_ids: List[int] = Field(default_factory=list,
                                  description="Document page IDs where these statements appear")
    centroid: List[float] = Field(default_factory=list,
                                description="The centroid vector of the effect cluster, derived from effect vectors")
    full_demise_centroid: SkipJsonSchema[DEMISEModel] = Field(DEMISEModel.model_construct(),
                                description="The centroid of the FULL DEMISE model vectors of the statements in this cluster")
    avg_impact: float = Field(0.0,
                                            description="Average impact model calculated from all statements in the cluster")
    entity_ids: List[int] = Field(default_factory=list,
                                 description="Entity IDs that this effect relates to")
    entity_name: str = Field(..., description="Name of the entity this effect relates to")
    virtual_entity_id: Optional[int] = Field(None, description="ID of the virtual entity this effect relates to")
    virtual_entity_short_id: Optional[str] = Field(None, description="Short ID of the virtual entity this effect relates to")
    virtual_entity: Optional[SkipJsonSchema[VirtualEntityModel]] = Field(None, description="The virtual entity this effect relates to")

    start_year: int = Field(..., description="Start year of the action/event. All statements in a single effect model must be from the same start year.")
    end_year: int = Field(..., description="End year of the action/event. Defaults to start_year if not provided.")

    def cited_text(self) -> str:
        """Returns text containing citations for verification."""
        # Collect all cited text from statements
        all_text = []
        for statement in self.statements:
            if hasattr(statement, 'statement_text'):
                all_text.append(statement.statement_text)
        return "\n".join(all_text)

    def clean_dump(self) -> dict:
        """Returns a clean representation of the analysis."""
        domains = self.relevant_domains()
        return {
            "effect_type": self.effect_type.value,
            "entity_name": self.entity_name,
            "statements_count": len(self.statements),
            "domains": domains,
            "start_year": self.start_year,
            "end_year": self.end_year
        }

    def no_confidence(self) -> bool:
        """Returns True if there is no confidence in the analysis."""
        return len(self.statements) == 0

    def relevant_domains(self) -> List[str]:
        """
        Get a list of relevant domains for this effect model
        """
        return [k for k,v in self.full_demise_centroid.to_kv_sparse().items() if v > 0.5]

    def to_vector(self) -> List[float]:
        return self.full_demise_centroid.to_vector()


    def to_hash(self) -> str:
        """Generate a hash for this effect based on the statements it contains"""
        return f"effect_{len(self.statements)}_{','.join(sorted([str(s.statement_text[:20]) for s in self.statements]))}"

    @classmethod
    def generate_docs(cls, depth=1):
        return f"""
{'#' * depth} Effect Model

{cls.__doc__}

"""


class EffectFlagModel(BaseVectorModel):
    """
    The EffectFlag Model represents a flag (positive or negative) created from an Effect cluster.
    It adds LLM-generated analysis and explanation to the raw effect data from an EffectModel.
    """
    id: Optional[int] = Field(None, description="The database ID of this effect flag")
    trace_id: str = Field(..., description="Used to trace it's origin")
    run_id: int = Field(..., description="The analysis run that created this flag.")
    effect_model_trace_ids: List[str] = Field(default_factory=list, description="Trace IDs of the flags this was created from")
    effect_model_ids: List[int] = Field(default_factory=list, description="IDs of the effect models this was created from")
    effect_models: SkipJsonSchema[List[EffectModel]] = Field(default_factory=list, description="IDs of the effect models this was created from")
    effect_flag_ids: List[int] = Field(default_factory=list, description="IDs of the flags this was created from")
    effect_flag_trace_ids: List[str] = Field(default_factory=list, description="Trace IDs of the flags this was created from")
    effect_flags: SkipJsonSchema[List["EffectFlagModel"]]= Field(default_factory=list, description="The flags this was created from")
    effect_type: EffectType = Field(..., description="Type of flag (red=negative, green=positive)")
    category: EffectCategory = Field(..., description="The primary category this flag relates to (Ecological, Social, or Governance)")
    title: str = Field(..., description="Title or short description of this effect flag")
    short_title: Optional[str] = Field(None, description="A concise 2-3 word title suitable for use as a label in UI elements like badges")
    summary: Optional[str] = Field(None, description="One or two sentence summary of the analysis")
    analysis: Optional[str] = Field(None, description="Detailed description of the effect")
    reason: str = Field(..., description="Reason or justification for this flag")
    impact: int = Field(..., ge=0, le=100, frozen=True, description="Impact rating (0-100) - immutable after creation")
    confidence: int = Field(..., ge=0, le=100, description="Confidence in this flag (0-100)")
    credibility: int = Field(0, ge=0, le=100, description="Credibility score (0-100) based on the source report's credibility and domain credibility")
    statement_ids: List[int] = Field(default_factory=list, description="IDs of statements in this flag")
    statements: List[StatementAndMetadata] = Field(default_factory=list, description="Statements that this flag is based on")
    domains: List[str] = Field(default_factory=list, description="Domains this flag relates to")
    citations: List[Citation] = Field(default_factory=list, description="Citation information for pages cited in the analysis")
    is_disclosure_only: bool = Field(False, description="Flag indicating if all source documents are disclosures")
    # The full DEMISE centroid from the source effect model
    full_demise_centroid: SkipJsonSchema[DEMISEModel] = Field(DEMISEModel.model_construct(), description="The centroid of the FULL DEMISE model vectors from the source Effect model")
    # Model section mappings - one section per model
    model_sections: Dict[str, str] = Field(default_factory=dict, description="Mapping of model name to section ID, one section per model")
    # Metadata for visualizing the process flow (not stored in database)
    source_info: Optional[Dict[str, Any]] = Field(default=None, description="Source tracking metadata for visualization")
    entity_ids: List[int] = Field(default_factory=list,
                                  description="Entity IDs that this effect relates to")
    entity_name: str = Field(..., description="Name of the entity this effect relates to")
    virtual_entity_id: Optional[int] = Field(None, description="ID of the virtual entity this effect relates to")
    virtual_entity_short_id: Optional[str] = Field(None, description="Short ID of the virtual entity this effect relates to")
    virtual_entity: Optional[SkipJsonSchema[VirtualEntityModel]] = Field(None, description="The virtual entity this effect relates to")
    start_year: Optional[int] = Field(None, description="Start year of the action/event")
    end_year: Optional[int] = Field(None, description="End year of the action/event. Defaults to start_year if not provided.")
    impact_measurement: EventImpactMeasurement = Field(None, frozen=True, description="The impact measurement for this flag - immutable after creation")
    impact_evaluation: EventImpactEvaluation = Field(None, frozen=True, description="The impact evaluation for this flag - immutable after creation")

    def cited_text(self) -> str:
        """Returns text containing citations for verification."""
        return self.analysis or ""

    def clean_dump(self) -> dict:
        """Returns a clean dictionary representation of the analysis."""
        return {
            "effect_type": self.effect_type.value,
            "category": self.category.value,
            "title": self.title,
            "summary": self.summary,
            "reason": self.reason,
            "analysis": self.analysis,
            "entity_name": self.entity_name,
            "start_year": self.start_year,
            "end_year": self.end_year,
            "impact": self.impact,
            "confidence": self.confidence,
            "domains": sorted(self.domains),
        }

    def no_confidence(self) -> bool:
        """Returns True if there is no confidence in the analysis."""
        return self.confidence == 0

    def to_vector(self) -> List[float]:
        return self.full_demise_centroid.to_vector()

    def to_hash(self) -> str:
        """Generate a hash for this effect flag"""
        entity_id = self.entity_ids[0] if self.entity_ids else 0
        effect_id = self.id or 0
        return f"effect_flag_{entity_id}_{effect_id}_{self.effect_type}"

    @classmethod
    def generate_docs(cls, depth=1):
        return f"""
{'#' * depth} Effect Flag Model

{cls.__doc__}

"""


if __name__ == "__main__":
    docs = EffectModel.generate_docs()
    print(docs)
    file_path = Path(__file__).parent.parent.parent.parent / "docs/models/sam/statement_analysis.md"

    with open(file_path, "w") as f:
        f.write(docs)

    view_markdown_in_browser(docs, "Statement Analysis Model", filename="statement_analysis_model.html")
