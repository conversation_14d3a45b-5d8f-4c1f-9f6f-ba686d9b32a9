"""
Data Access Object (DAO) for transfer-related database operations.

This module provides functions for transferring data between databases.
"""
from datetime import datetime
from loguru import logger
from psycopg import Connection
from typing import List, Optional, Dict

from eko.db import get_cus_conn, get_bo_conn
from eko.db.data.run import RunData
from eko.db.data.virtual_entity import VirtualEntityData
from eko.models.vector.derived.effect import EffectFlagModel
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.models.xfer.xfer_cherry import XferCherryModel
from eko.models.xfer.xfer_effect_flag import XferEffectFlagModel
from eko.models.xfer.xfer_run import XferRunModel
from eko.models.xfer.xfer_vague import XferVagueModel
from eko.score.score_flag import BayesianScoreSync
from eko.settings import settings
from eko.typing import not_none


class XferData:
    """
    Data Access Object for xfer_flags_v2 and xfer_entities_v2 tables.
    """

    @staticmethod
    def sync_model_sections_to_xfer_v2(conn: Connection) -> int:
        """
        Sync model sections from kg_model_sections to xfer_model_sections_v2.

        Args:
            conn: Database connection

        Returns:
            Number of model sections synced
        """
        # Get model sections from kg_model_sections
        model_sections = []

        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT model, section, title, description, level, icon, status
                FROM kg_model_sections
                WHERE status != 'deleted'
            """)

            for row in cursor.fetchall():
                model, section, title, description, level, icon, status = row

                model_sections.append(
                    {
                        "model": model,
                        "section": section,
                        "title": title,
                        "description": description,
                        "level": level,
                        "icon": icon,
                        "status": status,
                    }
                )

        if not model_sections:
            logger.warning("No model sections found in kg_model_sections")
            return 0

        # Insert model sections into xfer_model_sections_v2
        with conn.cursor() as cursor:
            for section in model_sections:
                cursor.execute(
                    """
                    INSERT INTO xfer_model_sections_v2 (
                        model, section, title, description, level, icon, status
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s
                    ) ON CONFLICT (model, section) DO UPDATE SET
                        title = EXCLUDED.title,
                        description = EXCLUDED.description,
                        level = EXCLUDED.level,
                        icon = EXCLUDED.icon,
                        status = EXCLUDED.status,
                        updated_at = now()
                """,
                    (
                        section["model"],
                        section["section"],
                        section["title"],
                        section["description"],
                        section["level"],
                        section["icon"],
                        section["status"],
                    ),
                )

        conn.commit()

        logger.info(f"Successfully synced {len(model_sections)} model sections to xfer_model_sections_v2")
        return len(model_sections)

    @staticmethod
    def convert_and_persist_xfer_flags(
        conn: Connection,
        effect_flags: List[EffectFlagModel],
        run_id: int,
        entity_xid: str
    ) -> List[int]:
        """
        Convert EffectFlagModel instances to XferEffectFlagModel and persist them to xfer_flags_v2 table.
        Flags with only disclosures as sources will not be persisted to the CMS.

        Args:
            conn: Database connection
            effect_flags: List of EffectFlagModel objects to convert and persist
            run_id: ID of the analysis run
            entity_xid: Short ID of the entity

        Returns:
            List of persisted flag IDs
        """
        persisted_ids = []
        skipped_disclosure_only_flags = 0
        skipped_low_credibility_flags = 0

        for flag in effect_flags:
            try:
                if not flag.id:
                    logger.warning(f"Skipping flag without ID: {flag.title}")
                    continue

                # Skip flags with low credibility
                if not flag.is_disclosure_only and flag.credibility < settings.effect_flag_min_credibility:
                    logger.info(f"Skipping flag with low credibility ({flag.credibility}): {flag.title} (ID: {flag.id})")
                    skipped_low_credibility_flags += 1
                    continue

                # Calculate score from flag metrics
                score = (flag.impact / 100.0) * (flag.credibility / 100.0)

                # Prepare impact value analysis data - serialize the pydantic models
                impact_value_analysis = {
                    "impact_measurement": flag.impact_measurement.model_dump(),
                    "impact_evaluation": flag.impact_evaluation.model_dump()
                }
                
                
                # Convert to XferEffectFlagModel
                xfer_flag = XferEffectFlagModel(
                    id=flag.id,
                    entity_xid=entity_xid,
                    flag_type=str(flag.effect_type),
                    flag_title=flag.title,
                    flag_short_title=flag.short_title,
                    year=flag.start_year or 0,
                    start_year=flag.start_year,
                    end_year=flag.end_year,
                    score=score,
                    impact=flag.impact,
                    confidence=flag.confidence,
                    credibility=flag.credibility,
                    flag_summary=flag.summary,
                    flag_analysis=flag.analysis,
                    domains=flag.domains,
                    model_sections=flag.model_sections,  # Include model sections
                    citations=flag.citations,  # To be populated from database
                    flag_statements=flag.statements,  # To be populated from database
                    impact_value_analysis=impact_value_analysis,  # Include impact measurement and evaluation data
                    full_demise_centroid=flag.full_demise_centroid,
                    is_disclosure_only=flag.is_disclosure_only  # Include the is_disclosure_only flag
                )

                # Prepare JSON model for persistence
                model_json = xfer_flag.model_dump_json()

                # Persist to xfer_flags_v2 table
                with get_cus_conn() as cus_conn:
                    with cus_conn.cursor() as cus_cur:
                        cus_cur.execute("""
                            INSERT INTO xfer_flags_v2 (run_id, id, entity_xid, flag_type, model)
                            VALUES (%s, %s, %s, %s, %s)
                            RETURNING id
                        """, (
                            run_id,
                            flag.id,
                            entity_xid,
                            str(flag.effect_type),
                            model_json
                        ))

                        # Get the returned ID
                        result = cus_cur.fetchone()
                        if result:
                            persisted_ids.append(result[0])
                    cus_conn.commit()
                    conn.commit()

            except Exception as e:
                logger.error(f"Error converting/persisting flag {flag.id}: {e}")
                logger.exception(e)
                conn.rollback()

        if skipped_disclosure_only_flags > 0:
            logger.info(f"Skipped {skipped_disclosure_only_flags} flags with only disclosure sources")
        if skipped_low_credibility_flags > 0:
            logger.info(f"Skipped {skipped_low_credibility_flags} flags with low credibility")

        return persisted_ids

    @staticmethod
    def delete_xfer_flags_for_run(conn: Connection, run_id: int) -> int:
        """
        Delete all xfer flags for a specific run.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            Number of deleted flags
        """
        try:
            with conn.cursor() as cur:
                cur.execute("DELETE FROM xfer_flags_v2 WHERE run_id = %s RETURNING id", (run_id,))
                deleted_ids = cur.fetchall()
                conn.commit()
                return len(deleted_ids)
        except Exception as e:
            logger.error(f"Error deleting xfer flags for run {run_id}: {e}")
            logger.exception(e)
            conn.rollback()
            return 0

    @staticmethod
    def sync_virtual_entities(conn: Connection) -> List[str]:
        """
        Sync all virtual entities to the xfer_entities_v2 table.
        Uses VirtualEntityExpandedModel directly without an intermediary object.

        Args:
            conn: Database connection

        Returns:
            List of short_ids of the synced virtual entities
        """
        synced_entities = []

        # Get all virtual entities
        all_virtual_entities = VirtualEntityData.list_all(conn)

        for virtual_entity in all_virtual_entities:
            try:
                # Get the expanded model with base entities
                expanded_entity = VirtualEntityData.get_expanded(conn, virtual_entity.id)
                if not expanded_entity:
                    logger.warning(f"Could not get expanded entity for {virtual_entity.name}")
                    continue

                # Prepare model for persistence
                model_json = expanded_entity.model_dump_json()

                # Persist to xfer_entities_v2 table
                with conn.cursor() as cur:
                    # Get the latest run_id
                    cur.execute("SELECT MAX(id) FROM ana_runs")
                    latest_run = cur.fetchone()
                    latest_run_id = latest_run[0] if latest_run and latest_run[0] else 1
                with get_cus_conn() as cus_conn:
                    with cus_conn.cursor() as cus_cursor:
                        cus_cursor.execute("""
                        INSERT INTO xfer_entities_v2 (entity_xid, run_id, name, type, model)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (entity_xid)
                        DO UPDATE SET
                            name = EXCLUDED.name,
                            run_id = EXCLUDED.run_id,
                            type = EXCLUDED.type,
                            model = EXCLUDED.model
                        RETURNING entity_xid
                    """, (
                        expanded_entity.short_id,
                        latest_run_id,
                        expanded_entity.name,
                        expanded_entity.type,
                        model_json
                    ))

                        # Get the returned entity_xid
                        result = cus_cursor.fetchone()
                    if result:
                        synced_entities.append(result[0])

                    cus_conn.commit()
                conn.commit()

            except Exception as e:
                logger.error(f"Error syncing virtual entity {virtual_entity.id}: {e}")
                logger.exception(e)
                conn.rollback()

        return synced_entities

    @staticmethod
    def sync_runs_to_xfer_v2(conn: Connection, run_ids: Optional[List[int]] = None) -> List[int]:
        if run_ids:
            return [XferData.sync_run(run_id) for run_id in run_ids]
        else:
            # Get all completed runs
            runs = RunData.list_runs(conn, status="completed", limit=1000)
            return [XferData.sync_run(not_none(run.id)) for run in runs]

    @staticmethod
    def sync_run(run_id: int):
        # Convert to XferRunModel
        with get_bo_conn() as conn:
            run=RunData.get_by_id(conn, run_id)
        if not run:
            logger.error(f"Run {run_id} not found")
            raise ValueError(f"Run {run_id} not found")
        xfer_run = XferRunModel(
            id=not_none(run.id),
            run_type=run.run_type,
            scope=run.scope,
            target=run.target,
            start_year=run.start_year,
            end_year=run.end_year,
            models=run.models,
            status=run.status,
            created_at=run.created_at or datetime.now(),
            updated_at=run.updated_at,
            completed_at=run.completed_at,
            prev_run_id=run.prev_run_id,
            version=run.version
        )
        # Prepare JSON model for persistence
        model_json = xfer_run.model_dump_json()
        # Persist to xfer_runs_v2 table
        with get_cus_conn() as cus_conn:
            with cus_conn.cursor() as cus_cur:
                cus_cur.execute("""
                                INSERT INTO xfer_runs_v2 (id, run_type, scope, target, model, completed_at, run_id, run_date, start_year, end_year)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                ON CONFLICT (id)
                                    DO UPDATE SET run_type = EXCLUDED.run_type,
                                                  scope    = EXCLUDED.scope,
                                                  target   = EXCLUDED.target,
                                                  model    = EXCLUDED.model,
                                                  completed_at = EXCLUDED.completed_at,
                                                  run_id = EXCLUDED.run_id,
                                                  run_date = EXCLUDED.run_date,
                                                  start_year = EXCLUDED.start_year,
                                                  end_year = EXCLUDED.end_year
                                RETURNING id
                                """, (
                                    run.id,
                                    run.run_type,
                                    run.scope,
                                    run.target,
                                    model_json,
                                    run.completed_at,
                                    run.id,  # run_id = id
                                    run.completed_at,  # run_date = completed_at
                                    run.start_year,
                                    run.end_year
                                ))

                # Get the returned ID
                result = cus_cur.fetchone()

            cus_conn.commit()
        return result[0]


    @staticmethod
    def sync_promises_to_xfer_v2(conn: Connection, run_id: int) -> List[int]:
        """
        Sync promises to the xfer_gw_promises_v2 table for a specific run.
        Uses PromiseData DAO for type safety and proper data handling.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            List of synced promise IDs
        """
        from eko.db.data.promise import PromiseData
        from eko.models.xfer.xfer_promise import XferPromiseModel
        synced_promise_ids = []

        try:
            # Get promises for the run using PromiseData DAO as PromiseVerdict models
            promise_verdicts = PromiseData.list_by_run(conn, run_id)

            logger.info(f"Syncing {len(promise_verdicts)} promises to xfer_gw_promises_v2 table for run {run_id}")

            for verdict in promise_verdicts:
                try:
                    # Check if we have a valid entity_xid
                    if not verdict.virtual_entity_short_id:
                        logger.warning(f"No entity_xid found for promise {getattr(verdict, 'id', 'unknown')}. Skipping.")
                        continue

                    # Convert PromiseVerdict to XferPromiseModel
                    xfer_promise = XferPromiseModel(
                        id=getattr(verdict, "id", 0),
                        statement_id=verdict.statement_id,
                        entity_xid=verdict.virtual_entity_short_id,
                        promise_kept=verdict.promise_kept,
                        greenwashing=verdict.greenwashing,
                        verdict=verdict.verdict,
                        summary=verdict.summary,
                        conclusion=verdict.conclusion,
                        confidence=verdict.confidence,
                        text=getattr(verdict, "text", ""),  # May not be present in PromiseVerdict
                        statement_text=not_none(verdict.statement).statement_text,  # Get statement text from the statement
                        context=getattr(verdict, "context", None),  # May not be present in PromiseVerdict
                        promise_doc=verdict.doc_title,
                        promise_doc_year=verdict.doc_year,
                        evidence=verdict.evidence,
                        citations=verdict.citations,
                        llm_greenwashing=getattr(verdict, "llm_greenwashing", False),
                        company=verdict.company,
                        company_id=getattr(verdict, "company_id", 0),  # May not be present in PromiseVerdict
                        esg_promise=getattr(verdict, "esg_promise", True),  # May not be present in PromiseVerdict
                        created_at=None,  # Will be set by the database
                    )

                    # Prepare JSON model for persistence
                    model_json = xfer_promise.model_dump_json()

                    # Persist to xfer_gw_promises_v2 table
                    with get_cus_conn() as cus_conn:
                        with cus_conn.cursor() as cus_cur:
                            cus_cur.execute("""
                                INSERT INTO xfer_gw_promises_v2 (id, run_id, statement_id, entity_xid, kept, model)
                                VALUES (%s, %s, %s, %s, %s, %s)
                                ON CONFLICT (id, run_id)
                                DO UPDATE SET
                                    statement_id = EXCLUDED.statement_id,
                                    entity_xid = EXCLUDED.entity_xid,
                                    kept = EXCLUDED.kept,
                                    model = EXCLUDED.model
                                RETURNING id
                            """, (
                                getattr(verdict, 'id', 0),
                                run_id,
                                verdict.statement_id,
                                verdict.virtual_entity_short_id,
                                verdict.promise_kept,
                                model_json
                            ))

                            # Get the returned ID
                            result = cus_cur.fetchone()
                            if result:
                                synced_promise_ids.append(result[0])
                                logger.debug(f"Synced promise {result[0]} to xfer_gw_promises_v2 table")

                        cus_conn.commit()

                except Exception as e:
                    logger.error(f"Error syncing promise {getattr(verdict, 'id', 'unknown')}: {e}")
                    logger.exception(e)
                    continue

            logger.info(f"Successfully synced {len(synced_promise_ids)} promises to xfer_gw_promises_v2 table")

        except Exception as e:
            logger.error(f"Error syncing promises to xfer_gw_promises_v2 for run {run_id}: {e}")
            logger.exception(e)

        return synced_promise_ids

    @staticmethod
    def sync_claims_to_xfer_v2(conn: Connection, run_id: int) -> List[int]:
        """
        Sync claims to the xfer_gw_claims_v2 table for a specific run.
        Uses ClaimData DAO for type safety and proper data handling.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            List of synced claim IDs
        """
        from eko.db.data.claim import ClaimData
        from eko.models.xfer.xfer_claim import XferClaimModel
        synced_claim_ids = []

        try:
            # Get claims for the run using ClaimData DAO as ClaimVerdict models
            claim_verdicts = ClaimData.list_by_run(conn, run_id)

            logger.info(f"Syncing {len(claim_verdicts)} claims to xfer_gw_claims_v2 table for run {run_id}")

            for verdict in claim_verdicts:
                try:
                    # Check if we have a valid entity_xid
                    if not verdict.virtual_entity_short_id:
                        logger.warning(f"No entity_xid found for claim {getattr(verdict, 'id', 'unknown')}. Skipping.")
                        continue

                    # Convert ClaimVerdict to XferClaimModel
                    xfer_claim = XferClaimModel(
                        id=getattr(verdict, 'id', 0),
                        statement_id=verdict.statement_id,
                        entity_xid=verdict.virtual_entity_short_id,
                        valid_claim=verdict.valid_claim,
                        greenwashing=verdict.greenwashing,
                        verdict=verdict.verdict,
                        summary=verdict.summary,
                        conclusion=verdict.conclusion,
                        confidence=verdict.confidence,
                        text=getattr(verdict, 'text', ''),  # May not be present in ClaimVerdict
                        statement_text=verdict.statement.statement_text if verdict.statement else None,  # Get statement text from the statement
                        context=getattr(verdict, 'context', None),  # May not be present in ClaimVerdict
                        claim_doc=verdict.doc_title,
                        claim_doc_year=verdict.doc_year,
                        counters=verdict.counters,
                        citations=verdict.citations,
                        llm_greenwashing=getattr(verdict, 'llm_greenwashing', False),
                        importance=getattr(verdict, 'importance', 0),  # Get importance or default to 0
                        company=verdict.company,
                        company_id=getattr(verdict, 'company_id', 0),  # May not be present in ClaimVerdict
                        esg_claim=getattr(verdict, 'esg_claim', True),  # May not be present in ClaimVerdict
                        created_at=None  # Will be set by the database
                    )

                    # Prepare JSON model for persistence
                    model_json = xfer_claim.model_dump_json()

                    # Persist to xfer_gw_claims_v2 table
                    with get_cus_conn() as cus_conn:
                        with cus_conn.cursor() as cus_cur:
                            cus_cur.execute("""
                                INSERT INTO xfer_gw_claims_v2 (id, run_id, statement_id, entity_xid, verified, importance, model)
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                ON CONFLICT (id, run_id)
                                DO UPDATE SET
                                    statement_id = EXCLUDED.statement_id,
                                    entity_xid = EXCLUDED.entity_xid,
                                    verified = EXCLUDED.verified,
                                    importance = EXCLUDED.importance,
                                    model = EXCLUDED.model
                                RETURNING id
                            """, (
                                getattr(verdict, 'id', 0),
                                run_id,
                                verdict.statement_id,
                                verdict.virtual_entity_short_id,
                                verdict.valid_claim,
                                getattr(verdict, 'importance', 0),  # Add importance to the insert
                                model_json
                            ))

                            # Get the returned ID
                            result = cus_cur.fetchone()
                            if result:
                                synced_claim_ids.append(result[0])
                                logger.debug(f"Synced claim {result[0]} to xfer_gw_claims_v2 table")

                        cus_conn.commit()

                except Exception as e:
                    logger.error(f"Error syncing claim {getattr(verdict, 'id', 'unknown')}: {e}")
                    logger.exception(e)
                    continue

            logger.info(f"Successfully synced {len(synced_claim_ids)} claims to xfer_gw_claims_v2 table")

        except Exception as e:
            logger.error(f"Error syncing claims to xfer_gw_claims_v2 for run {run_id}: {e}")
            logger.exception(e)

        return synced_claim_ids

    @staticmethod
    def sync_cherry_to_xfer_v2(conn: Connection, run_id: int) -> List[int]:
        """
        Sync cherry picking data to the xfer_gw_cherry_v2 table for a specific run.
        Uses CherryPickingDAO and FloodingDAO to retrieve data.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            List of synced cherry picking analysis IDs
        """
        from eko.analysis_v2.selective_highlighting.data import CherryPickingDAO, FloodingDAO

        synced_cherry_ids = []

        try:
            # Get all virtual entities
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT id, short_id
                    FROM kg_virt_entities
                """)
                virt_entities = cur.fetchall()

            # Process cherry picking and flooding data
            cherry_models_with_entity = []
            flooding_models_with_entity = []

            # For each virtual entity, get cherry picking and flooding data for the run
            for virt_entity_id, short_id in virt_entities:
                # Use CherryPickingDAO to get cherry picking data
                cherry_models = CherryPickingDAO.get_by_entity(virt_entity_id, run_id)
                for model in cherry_models:
                    if model.run_id == run_id:
                        # Store model with entity short_id
                        cherry_models_with_entity.append((model, short_id))

                # Use FloodingDAO to get flooding data
                flooding_models = FloodingDAO.get_by_entity(virt_entity_id, run_id)
                for model in flooding_models:
                    if model.run_id == run_id:
                        # Store model with entity short_id
                        flooding_models_with_entity.append((model, short_id))

            logger.info(f"Syncing {len(cherry_models_with_entity)} cherry picking analyses to xfer_gw_cherry_v2 table for run {run_id}")

            for cherry_model, entity_xid in cherry_models_with_entity:
                try:
                    # Calculate score based on impacts
                    score = int(max(0, min(100, (cherry_model.average_positive_impact - cherry_model.negative_impact) * 10)))

                    # Use the LLM-generated fields from the model
                    analysis = cherry_model.analysis
                    reason = cherry_model.reason
                    label = cherry_model.label

                    # Convert to XferCherryModel
                    xfer_cherry = XferCherryModel(
                        id=cherry_model.id,
                        entity_xid=entity_xid,
                        label=label,
                        negative_statements=cherry_model.negative_statements,
                        positive_statements=cherry_model.positive_statements,
                        model="cherry_picking",
                        score=score,
                        explanation=reason,  # Use reason as explanation
                        analysis=analysis,
                        reason=reason,
                        citations=cherry_model.citations,
                        analysis_model=str(cherry_model.domain_vector) if cherry_model.domain_vector else None,
                        created_at=cherry_model.created_at.isoformat() if cherry_model.created_at else None,
                        severity=cherry_model.severity,
                        confidence=cherry_model.confidence,
                        authenticity=cherry_model.authenticity
                    )

                    # Prepare JSON model for persistence
                    model_json = xfer_cherry.model_dump_json()

                    # Persist to xfer_gw_cherry_v2 table
                    with get_cus_conn() as cus_conn:
                        with cus_conn.cursor() as cus_cur:
                            cus_cur.execute("""
                                INSERT INTO xfer_gw_cherry_v2 (id, entity_xid, run_id, label, model)
                                VALUES (%s, %s, %s, %s, %s)
                                ON CONFLICT (id)
                                DO UPDATE SET
                                    entity_xid = EXCLUDED.entity_xid,
                                    run_id = EXCLUDED.run_id,
                                    label = EXCLUDED.label,
                                    model = EXCLUDED.model
                                RETURNING id
                            """, (
                                cherry_model.id,  # id
                                entity_xid,  # entity_xid
                                run_id,
                                label,  # label
                                model_json
                            ))

                            # Get the returned ID
                            result = cus_cur.fetchone()
                            if result:
                                synced_cherry_ids.append(result[0])
                                logger.debug(f"Synced cherry picking analysis {result[0]} to xfer_gw_cherry_v2 table")

                        cus_conn.commit()

                except Exception as e:
                    logger.error(f"Error syncing cherry picking analysis {cherry_model.id}: {e}")
                    logger.exception(e)
                    continue

            logger.info(f"Successfully synced {len(synced_cherry_ids)} cherry picking analyses to xfer_gw_cherry_v2 table")

            # Now process flooding data
            logger.info(f"Syncing {len(flooding_models_with_entity)} flooding analyses to xfer_gw_cherry_v2 table for run {run_id}")

            for flooding_model, entity_xid in flooding_models_with_entity:
                try:
                    # Calculate score based on impacts
                    score = int(max(0, min(100, (flooding_model.average_positive_impact - flooding_model.negative_impact) * 10)))

                    # Use the LLM-generated fields from the model
                    analysis = flooding_model.analysis
                    reason = flooding_model.reason
                    label = flooding_model.label

                    # Convert to XferCherryModel (we use the same model for both cherry picking and flooding)
                    xfer_cherry = XferCherryModel(
                        id=flooding_model.id,
                        entity_xid=entity_xid,
                        label=label,
                        negative_statements=flooding_model.negative_statements,
                        positive_statements=flooding_model.positive_statements,
                        model="flooding",
                        score=score,
                        explanation=reason,  # Use reason as explanation
                        analysis=analysis,
                        reason=reason,
                        citations=flooding_model.citations,
                        analysis_model=str(flooding_model.domain_vector) if flooding_model.domain_vector else None,
                        created_at=flooding_model.created_at.isoformat() if flooding_model.created_at else None,
                        severity=flooding_model.severity,
                        confidence=flooding_model.confidence,
                        authenticity=flooding_model.authenticity
                    )

                    # Prepare JSON model for persistence
                    model_json = xfer_cherry.model_dump_json()

                    # Persist to xfer_gw_cherry_v2 table
                    with get_cus_conn() as cus_conn:
                        with cus_conn.cursor() as cus_cur:
                            cus_cur.execute("""
                                INSERT INTO xfer_gw_cherry_v2 (id, entity_xid, run_id, label, model)
                                VALUES (%s, %s, %s, %s, %s)
                                ON CONFLICT (id)
                                DO UPDATE SET
                                    entity_xid = EXCLUDED.entity_xid,
                                    run_id = EXCLUDED.run_id,
                                    label = EXCLUDED.label,
                                    model = EXCLUDED.model
                                RETURNING id
                            """, (
                                flooding_model.id,  # id
                                entity_xid,  # entity_xid
                                run_id,
                                label,  # label
                                model_json
                            ))

                            # Get the returned ID
                            result = cus_cur.fetchone()
                            if result:
                                synced_cherry_ids.append(result[0])
                                logger.debug(f"Synced flooding analysis {result[0]} to xfer_gw_cherry_v2 table")

                        cus_conn.commit()

                except Exception as e:
                    logger.error(f"Error syncing flooding analysis {flooding_model.id}: {e}")
                    logger.exception(e)
                    continue

            logger.info(f"Successfully synced a total of {len(synced_cherry_ids)} selective highlighting analyses to xfer_gw_cherry_v2 table")

        except Exception as e:
            logger.error(f"Error syncing selective highlighting analyses to xfer_gw_cherry_v2 for run {run_id}: {e}")
            logger.exception(e)

        return synced_cherry_ids

    @staticmethod
    def sync_vague_to_xfer_v2(conn: Connection, run_id: int) -> List[int]:
        """
        Sync vague term analysis data to the xfer_gw_vague_v2 table for a specific run.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            List of synced vague term analysis IDs
        """
        synced_vague_ids = []

        try:
            # Get vague term analyses for the run
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT
                        v.*,
                        ve.short_id as entity_xid
                    FROM
                        ana_vague_v2 v
                    JOIN
                        kg_virt_entities ve ON v.virt_entity_id = ve.id
                    WHERE
                        v.run_id = %s
                """, (run_id,))
                vague_analyses = cur.fetchall()

            logger.info(f"Syncing {len(vague_analyses)} vague term analyses to xfer_gw_vague_v2 table for run {run_id}")

            for vague in vague_analyses:
                try:
                    # Convert to XferVagueModel
                    xfer_vague = XferVagueModel(
                        id=vague[0],  # id
                        entity_xid=vague[-1],  # entity_xid (from the JOIN)
                        phrase=vague[3],  # phrase
                        score=vague[4],  # score
                        explanation=vague[5],  # explanation
                        analysis=vague[6],  # analysis
                        summary=vague[7],  # summary
                        citations=vague[9],  # citations
                        rank=vague[10],  # rank
                        created_at=vague[11].isoformat() if vague[11] else None  # created_at
                    )

                    # Prepare JSON model for persistence
                    model_json = xfer_vague.model_dump_json()

                    # Persist to xfer_gw_vague_v2 table
                    with get_cus_conn() as cus_conn:
                        with cus_conn.cursor() as cus_cur:
                            cus_cur.execute("""
                                INSERT INTO xfer_gw_vague_v2 (id, entity_xid, run_id, phrase, model)
                                VALUES (%s, %s, %s, %s, %s)
                                ON CONFLICT (id)
                                DO UPDATE SET
                                    entity_xid = EXCLUDED.entity_xid,
                                    run_id = EXCLUDED.run_id,
                                    phrase = EXCLUDED.phrase,
                                    model = EXCLUDED.model
                                RETURNING id
                            """, (
                                vague[0],  # id
                                vague[-1],  # entity_xid
                                run_id,
                                vague[3],  # phrase
                                model_json
                            ))

                            # Get the returned ID
                            result = cus_cur.fetchone()
                            if result:
                                synced_vague_ids.append(result[0])
                                logger.debug(f"Synced vague term analysis {result[0]} to xfer_gw_vague_v2 table")

                        cus_conn.commit()

                except Exception as e:
                    logger.error(f"Error syncing vague term analysis {vague[0]}: {e}")
                    logger.exception(e)
                    continue

            logger.info(f"Successfully synced {len(synced_vague_ids)} vague term analyses to xfer_gw_vague_v2 table")

        except Exception as e:
            logger.error(f"Error syncing vague term analyses to xfer_gw_vague_v2 for run {run_id}: {e}")
            logger.exception(e)

        return synced_vague_ids

    @staticmethod
    def sync_prediction_v2_to_xfer(conn: Connection, run_id: int, virtual_entity:VirtualEntityExpandedModel) -> Dict[str, int]:
        """
        Sync prediction-v2 data to the xfer tables for a specific run.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            Dictionary with counts of synced records by type
        """
        from eko.db.data.prediction_sync import sync_prediction_v2_to_xfer as sync_func
        return sync_func(conn, run_id, virtual_entity)



    @staticmethod
    def sync_score_to_xfer_v2(conn: Connection, run_id: int) -> List[str]:
        """
        Calculate entity scores from ana_effect_flags and sync to the xfer_score_v2 table for a specific run.

        This method implements the scoring logic from create_profile.py to calculate entity scores
        directly from the effect flags data.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            List of synced entity short IDs
        """

        return BayesianScoreSync.sync_score_to_xfer_v2(conn, run_id)
