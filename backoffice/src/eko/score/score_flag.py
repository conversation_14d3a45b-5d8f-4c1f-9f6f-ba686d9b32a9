"""score_sync_v2.py
====================
Drop‑in scoring utilities for **red‑flag risk**.  Two complementary models are
available; import the one you want and call ``sync_score_to_xfer_v2`` with the
usual `(conn, run_id)` signature:

1. **ScoreSync** – severity × frequency × recency probability model.  *Green
   flags are still collected for telemetry but **do not offset** the risk*.
2. **BayesianScoreSync** – Beta‑<PERSON> updater that likewise ignores green
   flags.
"""

from __future__ import annotations

import logging
import math
from datetime import datetime
from typing import List

import numpy as np
from psycopg import Connection

from eko.db import get_cus_conn
from eko.models.xfer.xfer_score import XferScoreModel
# Project‑level helpers assumed present in runtime env
from eko.settings import settings

logger = logging.getLogger(__name__)

###############################################################################
# Shared helpers
###############################################################################

def severity_to_prob(raw_value: float, *, midpoint: float, slope: float) -> float:
    """Logistic mapping from raw severity (0–∞) to (0–1)."""
    return 1.0 / (1.0 + math.exp(-slope * (raw_value - midpoint)))


def select_entities_with_flags(cur, run_id: int):
    cur.execute(
        """
        SELECT DISTINCT ve.id, ve.short_id
        FROM ana_effect_flags ef
                 JOIN kg_virt_entities ve ON ve.id = ef.virtual_entity_id
        WHERE ef.run_id = %s
        """,
        (run_id,),
    )
    return cur.fetchall()

###############################################################################
# 1. Severity × frequency × recency model (no mitigation)
###############################################################################

class ScoreSync:
    """Probability‑style red‑flag scorer **without green‑flag mitigation**."""

    MIDPOINT = getattr(settings, "flag_severity_midpoint", 12.0)
    SLOPE = getattr(settings, "flag_severity_slope", 0.4)
    LAMBDA = getattr(settings, "flag_recency_lambda", 0.4)

    @classmethod
    def sync_score_to_xfer_v2(cls, conn: Connection, run_id: int) -> List[str]:
        synced: List[str] = []
        now_year = datetime.now().year

        def flag_probability(flag):
            sev_p = severity_to_prob(flag["value"], midpoint=cls.MIDPOINT, slope=cls.SLOPE)
            years_old = max(0, now_year - (flag["end_year"] or flag["start_year"]))
            rec_w = math.exp(-cls.LAMBDA * years_old)
            return sev_p * rec_w

        def combine(p_list):
            return 0.0 if not p_list else 1.0 - np.prod(1.0 - np.array(p_list))

        with conn.cursor() as cur:
            entities = select_entities_with_flags(cur, run_id)

        for entity_id, entity_xid in entities:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        SELECT AVG(ef.impact * ef.confidence/100.0 * ef.credibility/100.0)           AS value,
                               COUNT(*)                                                   AS occ,
                               ef.effect_type,
                               ef.title,
                               ef.start_year,
                               ef.end_year
                        FROM ana_effect_flags ef
                        WHERE ef.confidence > 60
                          AND ef.impact > 1
                          AND ef.virtual_entity_id = %s
                          AND ef.run_id = %s
                        GROUP BY ef.effect_type, ef.title, ef.start_year, ef.end_year
                        """,
                        (entity_id, run_id),
                    )
                    rows = cur.fetchall()

                red_probs, green_probs = [], []
                red_count, green_count = 0, 0

                for value, occ, eff_type, title, s_year, e_year in rows:
                    if value is None:
                        continue
                    flag = {
                        "value": float(value),
                        "count": int(occ),
                        "start_year": s_year,
                        "end_year": e_year,
                    }
                    p_single = flag_probability(flag)
                    p_freq = 1.0 - (1.0 - p_single) ** max(1, flag["count"])
                    if eff_type == "red":
                        red_probs.append(p_freq)
                        red_count += 1
                    elif eff_type == "green":
                        green_probs.append(p_freq)  # kept for metrics only
                        green_count += 1

                red_risk = combine(red_probs)

                # —— Final score: NO green‑flag mitigation ——
                final_risk = red_risk
                final_score = int(round(100 * (1.0 - final_risk)))

                cls._persist_score(entity_xid, run_id, final_score,
                                   red_count, green_count,
                                   red_probs, green_probs,
                                   timestamp=datetime.now())
                synced.append(entity_xid)
            except Exception:
                logger.exception("ScoreSync failed for entity %s", entity_xid)

        logger.info("ScoreSync: %d entities processed for run %s", len(synced), run_id)
        return synced

    # ------------------------------------------------------------------
    # Shared helpers
    # ------------------------------------------------------------------

    @staticmethod
    def _rating_text(score: int) -> str:
        return ("Great" if score >= 85 else "Very Good" if score >= 75 else
        "Good" if score >= 60 else "Poor" if score >= 40 else "Very Poor")

    @staticmethod
    def _severity_label(score: int) -> str:
        inv = 100 - score
        return ("Very Serious" if inv >= 60 else "Serious" if inv >= 40 else
        "Major" if inv >= 20 else "Minor" if inv >= 10 else
        "Very Minor" if inv >= 5 else "Trivial")

    @classmethod
    def _persist_score(cls, entity_xid: str, run_id: int, score: int,
                       red_count: int, green_count: int,
                       red_probs: list[float], green_probs: list[float],
                       timestamp: datetime):
        xfer_score = XferScoreModel(
            entity_xid=entity_xid,
            score=score,
            rating_text=cls._rating_text(score),
            minor_major_text=cls._severity_label(score),
            red_flags_count=red_count,
            green_flags_count=green_count,
            red_flags_score=round(sum(red_probs) * 100, 2),
            green_flags_score=round(sum(green_probs) * 100, 2),
            average_red=(np.mean(red_probs) * 100) if red_probs else 0,
            average_green=(np.mean(green_probs) * 100) if green_probs else 0,
            median_red=(np.median(red_probs) * 100) if red_probs else 0,
            median_green=(np.median(green_probs) * 100) if green_probs else 0,
            created_at=timestamp.isoformat(),
        )
        model_json = xfer_score.model_dump_json()

        with get_cus_conn() as cus_conn:
            with cus_conn.cursor() as cus_cur:
                cus_cur.execute(
                    """
                    INSERT INTO xfer_score_v2 (entity_xid, run_id, score, model)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (entity_xid, run_id)
                        DO UPDATE SET score = EXCLUDED.score,
                                      model = EXCLUDED.model
                    """,
                    (entity_xid, run_id, score, model_json),
                )
            cus_conn.commit()

###############################################################################
# 2. Pure Bayesian Beta‑Bernoulli model (unchanged)
###############################################################################

class BayesianScoreSync(ScoreSync):
    """Bayesian risk model (green ignored, as before)."""

    ALPHA0 = getattr(settings, "bayes_alpha_prior", 2.0)
    BETA0 = getattr(settings, "bayes_beta_prior", 8.0)

    @classmethod
    def sync_score_to_xfer_v2(cls, conn: Connection, run_id: int) -> List[str]:
        synced: List[str] = []
        now_year = datetime.now().year

        with conn.cursor() as cur:
            entities = select_entities_with_flags(cur, run_id)

        for entity_id, entity_xid in entities:
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        SELECT AVG(ef.impact * ef.confidence/100.0 * ef.credibility/100.0)           AS value,
                               COUNT(*)                                                   AS occ,
                               ef.title,
                               ef.start_year,
                               ef.end_year
                        FROM ana_effect_flags ef
                        WHERE ef.confidence > 60
                          AND ef.impact > 1
                          AND ef.effect_type = 'red'
                          AND ef.virtual_entity_id = %s
                          AND ef.run_id = %s
                        GROUP BY ef.title, ef.start_year, ef.end_year
                        """,
                        (entity_id, run_id),
                    )
                    rows = cur.fetchall()

                alpha, beta = cls.ALPHA0, cls.BETA0
                red_probs = []

                for value, occ, title, s_year, e_year in rows:
                    if value is None:
                        continue
                    sev_prob = severity_to_prob(value, midpoint=cls.MIDPOINT, slope=cls.SLOPE)
                    years_old = max(0, now_year - (e_year or s_year))
                    rec_w = math.exp(-cls.LAMBDA * years_old)
                    p = sev_prob * rec_w
                    alpha += occ * p
                    beta += occ * (1.0 - p)
                    red_probs.append(p)

                posterior_p = alpha / (alpha + beta)
                final_score = int(round(100 * (1.0 - posterior_p)))

                cls._persist_score(entity_xid, run_id, final_score,
                                   red_count=len(rows), green_count=0,
                                   red_probs=red_probs, green_probs=[],
                                   timestamp=datetime.now())
                synced.append(entity_xid)
            except Exception:
                logger.exception("Bayesian failed for entity %s", entity_xid)

        logger.info("BayesianScoreSync: %d entities processed for run %s", len(synced), run_id)
        return synced
