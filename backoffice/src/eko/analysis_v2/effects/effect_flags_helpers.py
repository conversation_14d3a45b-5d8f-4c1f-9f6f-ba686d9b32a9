"""
Helper functions for effect flag creation and processing.

This module provides helper functions used in effect flag creation and processing.
"""
from collections import Counter

import datetime
import numpy as np
import time
import uuid
from loguru import logger
from psycopg import Cursor
from sklearn.cluster import DBSCAN
from typing import List, Dict, Tuple

from eko.analysis_v2.citations import expand_citations, extract_eko_citations
from eko.analysis_v2.effects.measure_impact import ImpactMeasurementService, ImpactEvaluationFramework, \
    EventImpactMeasurement
from eko.nlp.util import has_eko_citations
from eko.analysis_v2.effects.constants import HARM_IMPACT_SCALE, BENEFIT_IMPACT_SCALE
from eko.analysis_v2.effects.flag_demise import extract_effect_flag_demise
from eko.analysis_v2.effects.models import EffectFlagResponse
from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.db.data.statement import StatementData
from eko.llm import LLMModel, PROMPT_PRESERVE_CITATIONS, PROMPT_KEEP_FACTS
from eko.llm import PROMPT_CREATE_CITATIONS
from eko.llm.llm_nlp import combine_text
from eko.llm.main import call_llms_str, call_llms_typed, get_embedding, LLMOptions
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.effect import EffectModel, EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.settings import settings
from eko.typing import not_none


def calculate_credibility_score(cursor:Cursor, statements: List[StatementAndMetadata]) -> int:
    """
    Calculate a credibility score for an effect flag based on document scores and domain credibility.

    Args:
        cursor: Database cursor
        statements: List of statements in the effect flag

    Returns:
        Credibility score (0-100)
    """
    if not statements or len(statements) == 0:
        logger.warning("No statements provided for credibility score calculation")
        return 0


    # Get document IDs from statements
    doc_ids = [statement.doc_id for statement in statements if statement.doc_id is not None]

    if not doc_ids:
        logger.warning("No document IDs found for credibility score calculation")
        return 0


    # Query document scores and origin domains
    try:
        cursor.execute("""
            SELECT id, score, origin_domain, d.credibility as domain_credibility, kg_documents.credibility as doc_credibility
            FROM kg_documents LEFT JOIN kg_domains d ON origin_domain = domain
            WHERE id = ANY(%s)
        """, (doc_ids,))

        documents = cursor.fetchall()

        if not documents:
            logger.warning("No documents found for credibility score calculation")
            return 0

        total_score = 0
        count = 0

        for doc in documents:
            doc_id, doc_score, origin_domain, domain_credibility, doc_credibility = doc

            if domain_credibility:
                # Get domain credibility (default to 50 if not found)
                if domain_credibility < 60:
                    total_score = total_score + 10
                    count = count + 1
                else:
                    total_score= total_score + domain_credibility
                    count = count + 1

            if doc_credibility:
                if doc_credibility < 40:
                    total_score= total_score + 10
                    count = count + 1
                else:
                    total_score= total_score + doc_credibility
                    count = count + 1

        if count != 0:
            return int(total_score / count)
        else:
            return 0

    except Exception as e:
        logger.error(f"Error calculating credibility score from documents: {e}")
        logger.exception(e)
        return 0


def create_effect_flag_prompt(
        cursor,
        name: str,
        effect_model: EffectModel,
        effect_type: EffectType
) -> List[Dict]:
    """
    Create a prompt for the LLM to analyze an effect and generate a flag.

    Args:
        cursor: Database cursor
        name: The entity name
        effect_model: The effect model to analyze
        effect_type: Force a specific effect type (EffectType.RED or EffectType.GREEN)

    Returns:
        List of message dictionaries for the LLM
    """
    # Get region names (existing code)
    cursor.execute(
        """ SELECT array_agg(region_name) from kg_base_entities where (id = ANY(%s) or canonical_id = ANY(%s) ) and region_name is not null""",
        (effect_model.entity_ids, effect_model.entity_ids),
    )
    region_names = cursor.fetchone()[0]
    if region_names:
        region_names = sorted(list(set(region_names)))

    # Calculate average text embedding from effect model statements
    statement_ids = [s.id for s in effect_model.statements]
    cursor.execute("""
        SELECT AVG(text_embedding) as avg_embedding
        FROM kg_statements_v2
        WHERE id = ANY(%s)
    """, (statement_ids,))
    avg_embedding = cursor.fetchone()[0]

    # Find similar statements for the same entity
    cursor.execute("""
        WITH similar_statements AS (
            SELECT DISTINCT s.id, s.statement_text, s.context, dp.id as page_id,
                   d.title, d.extract, d.year,
                   (s.text_embedding <=> %s::vector) as similarity
            FROM kg_statements_v2 s
            JOIN kg_document_pages dp ON dp.id = s.doc_page_id
            JOIN kg_documents d ON d.id = dp.doc_id
            WHERE (s.company_id = ANY(%s)
                  OR s.subject_entities && %s::bigint[]
                  OR s.object_entities && %s::bigint[])
            AND s.id != ALL(%s)  -- Exclude statements already in effect_model
            AND (s.text_embedding <=> %s::vector) < 0.7  -- Similarity threshold
            ORDER BY similarity
            LIMIT 20
        )
        SELECT * FROM similar_statements
    """, (avg_embedding, effect_model.entity_ids, effect_model.entity_ids,
          effect_model.entity_ids, statement_ids, avg_embedding))

    supporting_statements = cursor.fetchall()

    # Get page texts for both original and supporting statements
    all_page_ids = effect_model.doc_page_ids + [s[3] for s in supporting_statements]

    cursor.execute("""
        SELECT DISTINCT dp.id, dp.page_text, dp.page, d.title, d.extract, d.year
        FROM kg_document_pages dp
        JOIN kg_documents d ON d.id = dp.doc_id
        WHERE dp.id = ANY(%s)
        OR dp.id IN (
            SELECT id FROM kg_document_pages
            WHERE doc_id IN (SELECT doc_id FROM kg_document_pages WHERE id = ANY(%s))
            AND page IN (
                SELECT page-1 FROM kg_document_pages WHERE id = ANY(%s)
                UNION
                SELECT page+1 FROM kg_document_pages WHERE id = ANY(%s)
            )
        )
        ORDER BY dp.id
    """, (all_page_ids, all_page_ids, all_page_ids, all_page_ids))

    page_texts = [f"<page id='{row[0]}' from-report='{row[3]}' report-year={row[5]}>{row[1]}</page>"
                  for row in cursor.fetchall()]

    combined_text = "\n\n".join(page_texts)[:300000]

    # Format statements, separating primary and supporting evidence
    primary_statements = sorted([f"<primary_statement page-id={x.page_id}>{x.statement_text}</primary_statement>"
                                 for x in effect_model.statements])
    supporting_statements_text = sorted(
        [f"<supporting-statement page-id={s[3]} similarity='{s[7]:.2f}'>{s[1]}</supporting-statement>"
         for s in supporting_statements])

    # Format domains and ethics
    # domains_str = ", ".join(sorted(effect_model.relevant_domains()))

    if effect_type == EffectType.GREEN:
        good_bad = (
            "You are looking for all the demonstrably positive actions this company has taken. "
            "You are only interested in actions reported by third parties, please ignore statements made by "
            "the company themselves about their own actions as this is unreliable. "
            "You are not interested in good intentions, desires or hopes, just demonstrable actions with beneficial outcomes."
            "If you can't find any don't just return the text 'IRRELEVANT'"
        )
    else:
        good_bad = " You are looking for the worst in the company. Try to find any demonstrable harm done by the company. But if there is none just return the text 'IRRELEVANT'."

    issue_instructions = """<p>Make sure to provide a concise 1-2 sentence summary of the analysis that captures the key points. This summary
    will be used as a quick reference.</p> """

    if not region_names or len(region_names) == 0:
        region_text = ""
    else:
        region_text = f"""If the company in the text is not operating out of the regions {region_names} return
         an empty analysis. This is to avoid confusing subsidiaries/branches/divisions from different countries.
         To be clear we're only interested in {name} which operates out of {region_names or 'global'} region(s)."""

    static_instructions = f"""
        <instructions><ol>
            <li> Provide a detailed analysis of the company's actions from the text.</li>
            <li> Only include information about **direct** company actions.</li>
            <li> Do not provide an intro or summary. Only provide the analysis. This text will be passed to another
            LLM to process, so no need to be formal or have sections. Just prose.</li>
            <li> Do not extrapolate or generalize. Do not hypothesize or imagine.</li>
            <li> You should aim for {'at least 2000' if len(combined_text) > 20000 else '500'} words of analysis if you are to return any.</li>
            <li> If you feel there is no relevant information to ESG matters return nothing. Do not explain why just return the text 'IRRELEVANT'.</li>
            <li> Please keep all time period, location and quantity information in your analysis.</li>
            <li> If there is no relevant information to ESG matters return nothing. Do not explain why just return the text 'IRRELEVANT'.</li>
            <li> Please pay attention to the time period the events are happening in. Do make sure to retain this
                information in your analysis The year today is {datetime.datetime.now().year}</li>
            <li> {PROMPT_CREATE_CITATIONS}</li>
            <li> Please provide a detailed response. Do not provide a summary or overview.</li>
        </ol></instructions>
        """

    # Modified prompt to include supporting evidence
    prompt= [
        {
            "role": "user",
            "content": f"""
<system>You are a precise, detailed and analytic researcher for an industry magazine, you report on companies' environmental, social and governance (ESG) issues. You always cite your sources, preserve facts and include direct quotations from the text.</system>
<guidelines>
<ol>
    <li>Maintain objectivity based solely on provided information.</li>
    <li>Focus on the specific company, avoiding generalizations about industries or groups.</li>
    <li>Prioritize significant, well-substantiated impacts from the text.</li>
    <li>Use both primary statements and supporting evidence to form a comprehensive analysis.</li>
    <li>Supporting statements should be used to reinforce or provide additional context to primary statements.</li>
    <li>Think carefully before responding</li>
</ol>
</guidelines>
{static_instructions}
<context><pages>{combined_text}</pages></context>
{issue_instructions}
<instructions><ol>
    <li>Analyze the company {name} operating out of the {region_names or "global"} region(s)".</li>
    <li>{region_text}</li>
    <li>The reader already knows about {name} no need to tell them about it.</li>
    <li>Use all the text which is describing actions by {name} no other entity. {good_bad}</li>
</ol></instructions>
{static_instructions}
<p>Please now write your analysis about {name} based on the following primary statements and supporting evidence, drawing on the supplied <context>. Provide text with no additional commentary.</p>

<primary_evidence>
{"".join(primary_statements)}
</primary_evidence>

<supporting_evidence>
{"".join(supporting_statements_text)}
</supporting_evidence>

<note>Exclude any statements that are not directly related to {effect_model.virtual_entity.for_referencing_in_prompts() if effect_model.virtual_entity else effect_model.entity_name}.</note>

<p>If you find that there is more than one completely different issue being described, then divide your analysis into sections with a `---` as a separator but please AVOID THIS WHERE POSSIBLE.</p> """,
        }
    ]
    logger.debug(prompt)
    return prompt


def process_effect_flag(
        cursor,
        effect_model: EffectModel,
        statement_ids: List[int],
        effect_type: EffectType,
        run_id: int,
        tracker: TraceabilityTracker,
        virtual_entity: VirtualEntityExpandedModel
) -> List[EffectFlagModel]:
    """
    Process an effect model to create effect flags.

    Args:
        cursor: Database cursor
        effect_model: The effect model to process
        statement_ids: List of statement IDs in the effect model
        effect_type: The effect type (RED or GREEN)
        run_id: ID of the analysis run
        tracker: Traceability tracker instance
        virtual_entity: The expanded virtual entity model containing base entities

    Returns:
        List of EffectFlagModel objects
    """
    # Extract entity name from virtual entity
    name = virtual_entity.name
    # Use provided tracker from caller - it should already be initialized at the top level
    if tracker is None:
        raise ValueError("Tracker must be provided from the top level and not created in lower-level functions")

    # Start timing for performance tracking
    start_time = time.time()

    # Create the prompt for the LLM
    messages = create_effect_flag_prompt(cursor, name, effect_model, effect_type)

    # Call the LLM to generate the analysis
    analysis_start_time = time.time()
    analysis = call_llms_str(
        [LLMModel.NORMAL_HQ, LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        32000,
        options=LLMOptions(
            eval=lambda x: x is not None
            and (("irrelevant" in x.lower() and len(x) < 50) or (len(x) > 100 and has_eko_citations(x))),
            escalate_to=[LLMModel.NORMAL_UHQ],
            thinking=False,
        ),
    )

    if "irrelevant" in analysis.lower() and len(analysis) < 50:
        logger.warning("Flag marked as irrelevant")
        return []

    analysis_time_ms = int((time.time() - analysis_start_time) * 1000)

    # Track analysis generation
    tracker.record_stat(
        entity=[virtual_entity],
        stage=PipelineStage.EFFECT_FLAG_CREATED,
        count=1,
        effect_type=effect_type,
        processing_time_ms=analysis_time_ms,
        metadata={
            "analysis_generation_time_ms": analysis_time_ms,
            "analysis_length": len(analysis) if analysis else 0,
            "effect_model_id": effect_model.id,
            "effect_model_trace_id": effect_model.trace_id,
            "num_statements": len(effect_model.statements)
        }
    )

    # If no analysis was generated, return an empty list
    if not analysis or len(analysis.strip()) < 100:
        logger.info(f"No analysis generated for effect model {effect_model.id}")
        return []

    # Split the analysis into sections if it contains the separator
    sections = analysis.split("---")
    logger.info(f"Split analysis into {len(sections)} sections")

    # Process each section to create an effect flag
    effect_flags = []
    for i, section in enumerate(sections):
        if not has_eko_citations(section):
            logger.warning(f"Skipping section {i+1}/{len(sections)} with no citations")
            continue
        section = section.strip()
        if not section or len(section) < 100:
            logger.info(f"Skipping empty or short section {i+1}/{len(sections)}")
            continue


        # Create the flag prompt based on the effect type
        flag_prompt_start_time = time.time()
        flag_prompt = [
            {
                "role": "user",
                "content": f"""
<s>You are a precise, detailed and analytic researcher for an industry magazine, you report on companies' environmental, social and governance (ESG) issues. You always cite your sources, preserve facts and include direct quotations from the text.</s>

<instructions>
Based on the following analysis about {virtual_entity.title}, create a detailed flag that captures the {'negative impact' if effect_type == EffectType.RED else 'positive impact'} of their actions.

The flag should include:
1. title: A concise title (1-2 sentences)
2. short_title: A very concise title (2-3 words maximum) suitable for use as a label in UI elements like badges
3. summary: A brief summary of the {'harmful' if effect_type == EffectType.RED else 'beneficial'} action (2-3 sentences) DON'T include citations in this.
4. reason: The reason why this action is {'harmful' if effect_type == EffectType.RED else 'beneficial'} (1 paragraph)
5. category:A category that best describes the primary focus of this flag (Environmental, Social, Animal, or Governance)
6. Ratings on a scale of 0-100 for:
   - impact: Impact: How significant is the {'harm' if effect_type == EffectType.RED else 'benefit'}?
   - confidence: How confident are you in this assessment?
7. start_year, end_year: The time period when this occurred (start and end years)

{'Use this impact scale to guide your rating:' + HARM_IMPACT_SCALE if effect_type == EffectType.RED else 'Use this impact scale to guide your rating:' + BENEFIT_IMPACT_SCALE}

If the analysis is not about the {virtual_entity.for_referencing_in_prompts()} then return confidence of 0.

If you cannot create a meaningful flag from this analysis, return confidence of 0.

</instructions>

<analysis>
{section}
</analysis>

```
"""
            }
        ]
        flag_prompt_time_ms = int((time.time() - flag_prompt_start_time) * 1000)

        # Call the LLM to generate the flag
        flag_start_time = time.time()
        # Fix for ModelMetaclass not JSON serializable error
        # Pass EffectFlagResponse as a type hint for call_llms_typed
        flag_response: EffectFlagResponse = call_llms_typed(
            [LLMModel.NORMAL_ALL_ROUNDER, LLMModel.NORMAL_FAST],
            flag_prompt,
            32000,
            response_model=EffectFlagResponse,
            options=(
                LLMOptions(
                    escalate_to=[LLMModel.NORMAL_HQ],
                    thinking=False,
                )
            ),
        )
        impact_measurement,impact_evaluation = ImpactMeasurementService(ImpactEvaluationFramework()).measure_impact(flag_response.impact_description, run_id)
        impact_measurement: EventImpactMeasurement
        # TODO: Look more carefully at the measurement and add it to the flag etc.
        if flag_response is not None and flag_response.confidence == 0:
            logger.info(f"No flag generated for section {i+1}/{len(sections)} because of no-confidence.")
            continue
        
        flag_time_ms = int((time.time() - flag_start_time) * 1000)

        if effect_type == EffectType.RED and impact_measurement.net_impact_score > 0.0:
            logger.warning(f"Skipping red flag {i+1}/{len(sections)} because it describes an entity doing good")
            continue

        if effect_type == EffectType.GREEN and impact_measurement.net_impact_score < 0.0:
            logger.warning(f"Skipping green flag {i+1}/{len(sections)} because it describes an entity causing harm")
            continue

        # Track flag generation
        tracker.record_stat(
            entity=[virtual_entity],
            stage=PipelineStage.EFFECT_FLAG_CREATED,
            count=1,
            effect_type=effect_type,
            processing_time_ms=flag_time_ms,
            metadata={
                "flag_generation_time_ms": flag_time_ms,
                "flag_prompt_time_ms": flag_prompt_time_ms,
                "section_index": i,
                "section_length": len(section),
            }
        )

        # If no valid flag was generated, skip this section
        if flag_response is None or flag_response.no_confidence():
            logger.info(f"No valid flag generated for section {i+1}/{len(sections)}")
            continue

        # Calculate DEMISE model for the effect flag
        flag_demise = None
        try:
            # Extract DEMISE model from the flag's title and text
            flag_title = flag_response.title
            flag_summary = flag_response.summary

            # Use the specialized extract_effect_flag_demise function
            # which is designed specifically for effect flags
            flag_demise = extract_effect_flag_demise(
                flag_text=(section),
                flag_summary=flag_summary,
                flag_title=flag_title,
                entity_name=name,
                effect_type=effect_type.value,
            )

            logger.info(f"Generated DEMISE model for effect flag: {flag_title}")
        except Exception as e:
            logger.error(f"Error generating DEMISE model for effect flag: {e}")
            raise

        # Extract citations from the analysis text
        citations_from_text = []
        citations_from_statements = []

        try:
            with cursor.connection.cursor() as citations_cursor:
                # Extract citation IDs from text and validate they exist
                citation_ids = extract_eko_citations(section)
                if citation_ids:
                    # Expand citations to get full metadata
                    citations_from_text = expand_citations(citations_cursor, citation_ids)

                # Extract all citations from statements
                if statement_ids:
                    # Get all doc_page_ids from statements
                    citations_cursor.execute("""
                        SELECT doc_page_id FROM kg_statements_v2
                        WHERE id = ANY(%s) AND doc_page_id IS NOT NULL
                    """, (statement_ids,))

                    statement_doc_page_ids = [row[0] for row in citations_cursor.fetchall()]

                    if statement_doc_page_ids:
                        # Expand citations for all statement doc_page_ids
                        citations_from_statements = expand_citations(citations_cursor, statement_doc_page_ids)

                        # Mark citations that are referenced in the analysis text
                        for citation in citations_from_statements:
                            citation_id = citation.doc_page_id
                            citation.referenced = citation_id in citation_ids
        except Exception as e:
            logger.error(f"Error extracting citations: {e}")
            logger.exception(e)

        # Get the statements for this flag based on statement_ids
        flag_statements = []
        try:
            with cursor.connection.cursor() as statements_cursor:
                if statement_ids:
                    flag_statements= [StatementData.get_by_id(cursor.connection, id) for id in statement_ids]
        except Exception as e:
            logger.error(f"Error fetching statements for flag: {e}")
            logger.exception(e)

        # Calculate credibility score based on statements and document information
        credibility_score = 80
        if flag_statements:
            # Pass the cursor to access document information
            # TODO: FIX URGENTLY
            # credibility_score = calculate_credibility_score(cursor, flag_statements)
            pass
        
        # Check if all source documents are disclosures
        is_disclosure_only = True
        if flag_statements:
            # Get all document IDs from statements
            doc_ids = [statement.doc_id for statement in flag_statements if statement.doc_id is not None]

            if doc_ids:
                # Query to check if all documents have 'disclosure' in their research_categories
                with cursor.connection.cursor() as doc_cursor:
                    doc_cursor.execute("""
                        SELECT COUNT(*)
                        FROM kg_documents
                        WHERE id = ANY(%s) AND NOT 'disclosure' = ANY(research_categories)
                    """, (doc_ids,))

                    non_disclosure_count = doc_cursor.fetchone()[0]
                    is_disclosure_only = (non_disclosure_count == 0)

                    if is_disclosure_only:
                        logger.info(f"Flag has only disclosure sources: {len(doc_ids)} documents")
                    else:
                        logger.info(f"Flag has non-disclosure sources: {non_disclosure_count} of {len(doc_ids)} documents")
        #Create the domain list from the statements with the top 5 most commonly used  domains
        domain_counts = Counter()
        for statement in flag_statements:
            if statement.demise:
                domain_counts.update(statement.demise.domain.to_kv_sparse_percentage())
        top_domains = [domain for domain, count in domain_counts.most_common(settings.effect_flag_top_n_domains)]



        # Create the effect flag model
        effect_flag = EffectFlagModel(
            id=None,
            trace_id=str(uuid.uuid4()),
            run_id=run_id,
            entity_name=name,
            entity_ids=sorted([entity.id for entity in virtual_entity.base_entities]),
            virtual_entity_id=virtual_entity.id,
            virtual_entity_short_id=virtual_entity.short_id,
            virtual_entity=virtual_entity,
            title=flag_response.title,
            short_title=flag_response.short_title,
            summary=flag_response.summary,
            reason=flag_response.reason,
            category=flag_response.category,
            analysis=(section),
            impact=int(round(abs(impact_measurement.net_impact_score * 100.0), 0)),
            impact_measurement=impact_measurement,
            impact_evaluation=impact_evaluation,
            confidence=flag_response.confidence,
            credibility=credibility_score,
            start_year=flag_response.start_year,
            end_year=flag_response.end_year,
            effect_type=effect_type,
            model_sections={},  # Will be populated later
            effect_model_ids=[effect_model.id] if effect_model.id else [],
            effect_model_trace_ids=[effect_model.trace_id] if effect_model.trace_id else [],
            effect_flag_trace_ids=[],
            effect_flags=[],
            effect_models=[effect_model] if effect_model else [],
            statement_ids=statement_ids,
            statements=flag_statements,
            full_demise_centroid=flag_demise or effect_model.full_demise_centroid,
            citations=citations_from_text + citations_from_statements,
            is_disclosure_only=is_disclosure_only,
            domains=top_domains,
        )

        # Add the flag to the list
        effect_flags.append(effect_flag)


    # Track the total processing time
    total_time_ms = int((time.time() - start_time) * 1000)
    tracker.record_stat(
        entity=[virtual_entity],
        stage=PipelineStage.EFFECT_FLAG_CREATED,
        count=len(effect_flags),
        effect_type=effect_type,
        processing_time_ms=total_time_ms,
        metadata={
            "total_processing_time_ms": total_time_ms,
            "effect_model_id": effect_model.id,
            "effect_model_trace_id": effect_model.trace_id,
            "num_sections": len(sections),
            "num_flags_created": len(effect_flags)
        }
    )

    return effect_flags


def extract_vectors_from_flags(effect_flags: List[EffectFlagModel]) -> Tuple[List[np.ndarray], List[EffectFlagModel]]:
    """
    Extract domain vectors from effect flags' DEMISE models.

    Args:
        effect_flags: List of effect flags to extract vectors from

    Returns:
        Tuple containing (list of domain vectors, list of corresponding valid flags)
    """
    domain_vectors = []
    valid_flags = []

    for flag in effect_flags:
        # Extract just the domain part of the DEMISE model
        domain_vector = flag.full_demise_centroid.domain.to_vector()
        if domain_vector and len(domain_vector) > 0:
            domain_vectors.append(domain_vector)
            valid_flags.append(flag)
        else:
            logger.warning(f"Empty domain vector for flag {flag.title}, using fallback")
            # Fallback to text embedding if domain vector is empty
            embedding = get_embedding(not_none(flag.summary))
            if embedding:
                domain_vectors.append(embedding[:512])  # Truncate to match domain vector size
                valid_flags.append(flag)

    return domain_vectors, valid_flags


def cluster_by_domain(domain_vectors: List[np.ndarray], valid_flags: List[EffectFlagModel]) -> Dict[int, List[int]]:
    """
    Cluster flags by their domain vectors using DBSCAN.

    Args:
        domain_vectors: List of domain vectors extracted from flags
        valid_flags: List of corresponding flags

    Returns:
        Dictionary mapping cluster IDs to lists of flag indices
    """
    # Convert to numpy array
    domain_vectors_array = np.array(domain_vectors)

    # Parameters for DBSCAN for domain clustering
    domain_min_samples = 1  # Minimum cluster size

    # Apply DBSCAN for domain clustering
    domain_dbscan = DBSCAN(eps=settings.effect_flag_merging_domain_eps, min_samples=domain_min_samples,
                           metric='cosine')
    domain_clusters = domain_dbscan.fit_predict(domain_vectors_array)

    # Group flags by domain cluster
    domain_cluster_groups = {}
    for i, cluster_id in enumerate(domain_clusters):
        if cluster_id not in domain_cluster_groups:
            domain_cluster_groups[cluster_id] = []
        domain_cluster_groups[cluster_id].append(i)

    return domain_cluster_groups


def get_text_embeddings(flags: List[EffectFlagModel]) -> Tuple[List[np.ndarray], List[int]]:
    """
    Get text embeddings for a list of flags.

    Args:
        flags: List of flags to get embeddings for

    Returns:
        Tuple of (list of embeddings, list of valid indices)
    """
    embeddings = []
    valid_indices = []

    for i, flag in enumerate(flags):
        # Use summary for embedding, fallback to title if summary is None
        text_for_embedding = flag.summary if flag.summary else flag.title
        embedding = get_embedding(text_for_embedding)
        if embedding:
            embeddings.append(embedding)
            valid_indices.append(i)

    return embeddings, valid_indices


def cluster_by_text(flags: List[EffectFlagModel]) -> List[List[int]]:
    """
    Cluster flags by their text embeddings using DBSCAN.

    Args:
        flags: List of flags to cluster

    Returns:
        List of lists, where each inner list contains indices of flags to merge
    """
    # Get text embeddings for summaries
    summary_embeddings, valid_indices = get_text_embeddings(flags)

    if len(valid_indices) <= 1:
        return []

    # Convert to numpy array
    summary_vectors_array = np.array(summary_embeddings)

    # Parameters for DBSCAN for summary text embedding clustering
    summary_eps = settings.effect_flag_merging_text_eps
    summary_min_samples = 1  # Minimum cluster size

    # Apply DBSCAN clustering on summary embeddings
    summary_dbscan = DBSCAN(eps=summary_eps, min_samples=summary_min_samples, metric='cosine')
    summary_clusters = summary_dbscan.fit_predict(summary_vectors_array)

    # Group by summary cluster
    summary_cluster_groups = {}
    for i, cluster_id in enumerate(summary_clusters):
        if cluster_id not in summary_cluster_groups:
            summary_cluster_groups[cluster_id] = []
        summary_cluster_groups[cluster_id].append(valid_indices[i])

    to_merge = []
    for summary_cluster_id, indices in summary_cluster_groups.items():
        to_merge.append(indices)

    return to_merge


def cluster_flags_by_text(effect_flags: List[EffectFlagModel], tracker: TraceabilityTracker) -> List[EffectFlagModel]:
    """
    Cluster and merge flags by their text embeddings.

    Args:
        effect_flags: List of effect flags to cluster and merge
        tracker: Traceability tracker instance

    Returns:
        List of merged flags (text-based merging)
    """
    if len(effect_flags) <= 1:
        return effect_flags

    # Cluster by text
    text_merge_groups = cluster_by_text(effect_flags)

    if not text_merge_groups:
        return effect_flags

    # Prepare for merging
    merged_flags = []
    merged_indices = set()

    # Perform text-based merging
    for flag_indices in text_merge_groups:
        # Collect all flags to be merged
        flags_to_merge = [effect_flags[idx] for idx in flag_indices]

        # Add indices to the merged set
        for idx in flag_indices:
            merged_indices.add(idx)

        # Create a merged flag
        merged_flag = merge_flag_group(flags_to_merge)

        # Track the merge operation
        tracker.track_effect_flag_merged(
            merged_flag=merged_flag,
            similarity_threshold=settings.effect_flag_merging_text_eps
        )

        merged_flags.append(merged_flag)

    # Add the flags that weren't merged
    for i, flag in enumerate(effect_flags):
        if i not in merged_indices:
            merged_flags.append(flag)

    logger.info("*" * 80)
    logger.info("*" * 80)
    logger.info(f"After text merging: {len(merged_flags)} flags (from {len(effect_flags)})")
    logger.info("*" * 80)
    logger.info("*" * 80)

    return merged_flags


def cluster_flags_by_domain(effect_flags: List[EffectFlagModel], tracker: TraceabilityTracker) -> List[EffectFlagModel]:
    """
    Cluster and merge flags by their domain vectors.

    Args:
        effect_flags: List of effect flags to cluster and merge
        tracker: Traceability tracker instance

    Returns:
        List of merged flags (domain-based merging)
    """
    if len(effect_flags) <= 1:
        return effect_flags

    # Extract domain vectors and get valid flags
    domain_vectors, valid_flags = extract_vectors_from_flags(effect_flags)

    if len(valid_flags) <= 1:
        logger.warning("Not enough valid flags with domain vectors for clustering")
        return effect_flags

    # Cluster by domain
    domain_cluster_groups = cluster_by_domain(domain_vectors, valid_flags)

    # Prepare for merging
    merged_flags = []
    merged_indices = set()
    domain_merge_groups = []

    # Collect domain clusters to merge
    for domain_cluster_id, domain_indices in domain_cluster_groups.items():
        # Only process domain clusters with more than one flag
        if len(domain_indices) > 1 and domain_cluster_id != -1:  # Skip noise points (cluster_id = -1)
            # Group by year first
            year_groups = {}
            for idx in domain_indices:
                flag = valid_flags[idx]
                # Group by start year (rounded to nearest 2 years)
                year_key = (flag.start_year // 2) * 2 if flag.start_year else 0
                if year_key not in year_groups:
                    year_groups[year_key] = []
                year_groups[year_key].append(idx)

            # Add each year group as a merge group
            for year_indices in year_groups.values():
                if len(year_indices) > 1:
                    domain_merge_groups.append(year_indices)

    # Perform domain-based merging
    for flag_indices in domain_merge_groups:
        # Collect all flags to be merged
        flags_to_merge = [valid_flags[idx] for idx in flag_indices]

        # Find the original indices in effect_flags
        original_indices = []
        for flag in flags_to_merge:
            logger.info(f"Flag domains: {flag.domains}")

            for i, orig_flag in enumerate(effect_flags):
                if orig_flag.trace_id == flag.trace_id:
                    original_indices.append(i)
                    break

        # Add indices to the merged set
        for idx in original_indices:
            merged_indices.add(idx)

        # Create a merged flag
        merged_flag = merge_flag_group(flags_to_merge)
        logger.info(f"Merged flag domains: {merged_flag.domains}")

        # Get the domain eps value
        # Track the merge operation
        tracker.track_effect_flag_merged(
            merged_flag=merged_flag,
            similarity_threshold=(settings.effect_flag_merging_domain_eps)
        )

        merged_flags.append(merged_flag)

    # Add the flags that weren't merged
    for i, flag in enumerate(effect_flags):
        if i not in merged_indices:
            merged_flags.append(flag)


    logger.info("*" * 80)
    logger.info("*" * 80)
    logger.info(f"After domain merging: {len(merged_flags)} flags (from {len(effect_flags)})")
    logger.info("*" * 80)
    logger.info("*" * 80)

    return merged_flags


def merge_flag_group(flags_to_merge: List[EffectFlagModel]) -> EffectFlagModel:
    """
    Merge a group of flags into a single flag.

    Args:
        flags: List of flags to merge

    Returns:
        A new merged flag
    """
    if not flags_to_merge:
        raise ValueError("Cannot merge empty list of flags")

    flags = sorted(flags_to_merge, key=lambda f: f"{f.impact * f.confidence * f.credibility:08d}{f.title}", reverse=True)

    # Use the first flag as the base
    primary_flag = flags[0]

    # Combine effect IDs and statement IDs
    all_effect_ids = set()
    all_statement_ids = set()
    for flag in flags:
        all_effect_ids.update(flag.effect_model_ids or [])
        all_statement_ids.update(flag.statement_ids or [])

    # Determine the time period (use the widest range)
    start_years = [not_none(flag.start_year) for flag in flags if flag.start_year is not None]
    end_years = [not_none(flag.end_year) for flag in flags if flag.end_year is not None]

    # Use defaults if no valid years are found
    start_year = min(start_years) if start_years else primary_flag.start_year
    end_year = max(end_years) if end_years else primary_flag.end_year

    for flag in flags:
        if not has_eko_citations(not_none(flag.analysis)):
            raise ValueError(f"No citations found in analysis for flag {flag.title} prior to merging.")

    # Combine analyses with a separator
    combined_analysis = combine_text(
        [flag.analysis or "" for flag in flags if flag.analysis],
        PROMPT_PRESERVE_CITATIONS + " " + PROMPT_KEEP_FACTS,
        model=LLMModel.NORMAL_ALL_ROUNDER,
    )

    # Handle case where combine_text returns None
    if combined_analysis is None:
        combined_analysis = ""

    if not has_eko_citations(combined_analysis):
        raise ValueError("No citations found in combined analysis")


    # Calculate merged centroid from all flags
    merged_demise_centroid = DEMISEModel.model_construct()

    # Collect all DEMISE vectors and calculate the average
    valid_centroids = []
    for flag in flags:
        if hasattr(flag, 'full_demise_centroid') and flag.full_demise_centroid:
            valid_centroids.append(flag.full_demise_centroid)

    if valid_centroids:
        # Calculate average of all vectors
        merged_kv = {}
        for centroid in valid_centroids:
            for k, v in centroid.to_kv_sparse().items():
                if k in merged_kv:
                    merged_kv[k] += v
                else:
                    merged_kv[k] = v

        # Normalize by dividing by count
        for k in merged_kv:
            merged_kv[k] /= len(valid_centroids)

        # Create new DEMISE model from merged values
        merged_demise_centroid = DEMISEModel.from_sparse_kv(merged_kv)
    else:
        # If no valid centroids, use the primary flag's centroid
        merged_demise_centroid = primary_flag.full_demise_centroid if hasattr(primary_flag, 'full_demise_centroid') else DEMISEModel.model_construct()

    # Collect all domains from merging flags
    all_domains = set()
    for flag in flags:
        if hasattr(flag, 'domains') and flag.domains:
            all_domains.update(flag.domains)

    # Combine all statements from merging flags
    all_statements = []
    for flag in flags:
        if hasattr(flag, 'statements') and flag.statements:
            all_statements.extend(flag.statements)

    # Remove duplicate statements by ID
    unique_statements = {}
    for statement in all_statements:
        if statement.id and statement.id not in unique_statements:
            unique_statements[statement.id] = statement

    # Combine citations from all flags
    all_citations = []
    for flag in flags:
        if hasattr(flag, 'citations') and flag.citations:
            all_citations.extend(flag.citations)

    # Remove duplicates by doc_page_id
    unique_citations = {}
    for citation in all_citations:
        if citation.doc_page_id not in unique_citations:
            unique_citations[citation.doc_page_id] = citation

    # Use the maximum credibility from all flags
    # This ensures that high-credibility sources are not diluted by lower ones
    max_credibility = max(getattr(flag, 'credibility', 50) for flag in flags) if flags else 50

    # Determine if the merged flag is disclosure-only
    # A merged flag is disclosure-only only if ALL merged flags are disclosure-only
    is_disclosure_only = all(getattr(flag, 'is_disclosure_only', False) for flag in flags)

    # Create the merged flag
    combined_models = sorted([model for flag in flags if hasattr(flag, 'effect_models') for model in flag.effect_models], key=lambda x: x.id or 0)
    merged_flag = EffectFlagModel(
        id=None,
        trace_id=str(uuid.uuid4()),
        run_id=primary_flag.run_id,
        entity_name=primary_flag.entity_name,
        entity_ids=primary_flag.entity_ids,
        virtual_entity_id=primary_flag.virtual_entity_id,
        virtual_entity_short_id=primary_flag.virtual_entity_short_id,
        virtual_entity=primary_flag.virtual_entity,
        title=primary_flag.title,
        short_title=primary_flag.short_title,
        summary=primary_flag.summary,
        reason=primary_flag.reason,
        analysis=combined_analysis,
        impact=primary_flag.impact,
        confidence=primary_flag.confidence,
        credibility=primary_flag.credibility,
        start_year=start_year,
        end_year=end_year,
        effect_type=primary_flag.effect_type,
        effect_model_ids=sorted(list(all_effect_ids)),
        statement_ids=sorted(list(all_statement_ids)),
        statements=sorted(list(unique_statements.values()), key=lambda x: x.id or 0),
        effect_flag_ids=sorted([flag.id for flag in flags if flag.id]),
        effect_flag_trace_ids=sorted([flag.trace_id for flag in flags]),
        effect_flags=flags,
        effect_models=combined_models,
        full_demise_centroid=merged_demise_centroid,
        domains=sorted(list(all_domains)),
        category=primary_flag.category,
        citations=sorted(list(unique_citations.values()), key=lambda x: x.doc_page_id),
        is_disclosure_only=is_disclosure_only,
        impact_evaluation=primary_flag.impact_evaluation,
        impact_measurement=primary_flag.impact_measurement,
    )

    return merged_flag


def merge_similar_effect_flags(effect_flags: List[EffectFlagModel], tracker:TraceabilityTracker) -> List[EffectFlagModel]:
    """
    Merge similar effect flags in a two-step process:
    1. First cluster all flags by domain and merge them
    2. Then cluster all merged flags by text similarity and merge again

    Args:
        effect_flags: List of effect flags to merge
        tracker: Traceability tracker instance

    Returns:
        List of merged effect flags
    """
    if len(effect_flags) <= 1:
        return effect_flags

    logger.info(f"Starting with {len(effect_flags)} flags before merging")

    # Step 1: Cluster by domain and merge
    domain_merged_flags = cluster_flags_by_domain(effect_flags, tracker)

    # If no domain merging occurred, return the original flags
    if len(domain_merged_flags) == len(effect_flags):
        logger.info("No domain-based merging occurred")
    else:
        logger.info(f"Domain-based merging: {len(effect_flags)} -> {len(domain_merged_flags)} flags")

    # Step 2: Cluster by text and merge
    text_merged_flags = cluster_flags_by_text(domain_merged_flags, tracker)

    # If no text merging occurred, return the domain-merged flags
    if len(text_merged_flags) == len(domain_merged_flags):
        logger.info("No text-based merging occurred")
    else:
        logger.info(f"Text-based merging: {len(domain_merged_flags)} -> {len(text_merged_flags)} flags")

    # Log final clustering statistics
    tracker.record_stat(
        entity=None,
        stage=PipelineStage.EFFECT_FLAG_MERGED,
        count=len(text_merged_flags),
        effect_type=None,
        metadata={
            "clustering_method": "Sequential Domain-Text DBSCAN",
            "clustering_metric": "cosine",
            "domain_eps": getattr(settings, 'effect_flag_text_merging_domain_eps', 0.8),
            "summary_eps": getattr(settings, 'effect_flag_text_merging_summary_eps', 0.8),
            "domain_min_samples": 1,
            "summary_min_samples": 1,
            "initial_count": len(effect_flags),
            "after_domain_merge_count": len(domain_merged_flags),
            "final_count": len(text_merged_flags),
            "total_merged_count": len(effect_flags) - len(text_merged_flags),
            "using_domain_vectors": True,
            "using_summary_embeddings": True
        }
    )

    return text_merged_flags
